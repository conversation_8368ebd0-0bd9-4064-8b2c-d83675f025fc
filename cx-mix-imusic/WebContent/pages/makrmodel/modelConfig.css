.page {
  height: 100%;
  z-index: 10;
}
.page .header {
  position: relative;
  height: 142px;
  padding: 24px;
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 4px;
}
.page .header .pic {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  background: rgba(5, 85, 206, 0.05);
  border-radius: 50%;
}
.page .header .info .name {
  font-size: 20px;
  color: #262626;
  font-weight: bold;
  line-height: 28px;
  margin-bottom: 16px;
}
.page .header .info .box {
  display: flex;
  align-items: center;
}
.page .header .info .box .row {
  display: flex;
  align-items: center;
  line-height: 22px;
  margin-right: 32px;
}
.page .header .info .box .row:last-child {
  margin-right: 0;
}
.page .header .info .box .row .label {
  color: #868686;
}
.page .header .info .box .row .value {
  color: #262626;
}
.page .header .trainBtn {
  position: absolute;
  right: 40px;
  top: 40px;
  color: #0555CE;
  font-size: 48px;
  cursor: pointer;
}
.page .main {
  display: flex;
  height: calc(100% - 158px);
  border-radius: 4px;
  background: #ffffff;
}
.page .main .l-box {
  width: 288px;
  height: 100%;
  border-radius: 4px 0 0 4px;
  border-right: 1px solid #ebf1f8;
  box-sizing: border-box;
}
.page .main .l-box .title {
  height: 52px;
  line-height: 51px;
  padding: 0 24px;
  font-weight: bold;
  border-bottom: 1px solid #ebf1f8;
  box-sizing: border-box;
}
.page .main .l-box .el-tree {
  padding: 0 16px;
}
.page .main .l-box .el-tree .el-tree-node.is-current.is-expanded {
  background-color: transparent;
}
.page .main .l-box .el-tree .el-tree-node__expand-icon::before {
  position: absolute;
  background: url('./images/file.png') center / 20px 20px no-repeat;
  width: 20px;
  height: 20px;
  left: 0;
  top: 0;
  content: '';
}
.page .main .l-box .el-tree .el-tree-node__expand-icon {
  width: 20px;
  height: 20px;
  padding: 0;
}
.page .main .l-box .el-tree .el-tree-node__label {
  margin-left: 8px;
}
.page .main .l-box .el-tree .el-tree-node__expand-icon.is-leaf {
  display: none;
}
.page .main .l-box .el-tree .el-tree-node__expand-icon.expanded {
  transform: rotate(0deg);
}
.page .main .r-box {
  position: relative;
  flex: 1;
  overflow: hidden;
  padding: 16px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.page .main .r-box .title {
  position: relative;
  padding-left: 12px;
  color: #262626;
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 16px;
}
.page .main .r-box .title::before {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 14px;
  background: #0555CE;
  content: '';
}
.page .main .r-box .empty-img {
  margin: 120px auto;
}
.page .main .r-box .footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: right;
  height: 72px;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  border-top: 1px solid #E8E8E8;
}
.page .main .r-box .keyword {
  width: 100%;
  height: 240px;
  position: relative;
  padding-bottom: 32px;
  background: #f2f4f7;
  border-radius: 4px;
}
.page .main .r-box .keyword .el-input {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}
.page .main .r-box .keyword .el-input .el-input__inner:hover,
.page .main .r-box .keyword .el-input .el-input__inner:focus {
  background-color: #f2f4f7 !important;
  border-color: transparent !important;
  box-shadow: none !important;
}
.page .main .r-box .keyword .box {
  padding: 16px;
  display: flex;
  grid-gap: 8px;
  flex-wrap: wrap;
}
.page .main .r-box .keyword .box .tag {
  height: 22px;
  line-height: 22px;
  padding: 0 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: normal;
  text-align: center;
  cursor: pointer;
  color: #0555CE;
  background: rgba(5, 85, 206, 0.1);
}
