var mixin = {
  data() {
    return {
      isProvince: false,
    }
  },
  methods: {
    getQueryObject() {
      var url = window.location.search || window.location.hash;
      var search = url.substring(url.lastIndexOf('?') + 1);
      var obj = {};
      var reg = /([^&=]+)=([^&=]*)/g;
      search.replace(reg, function (rs, $1, $2) {
        var name = decodeURIComponent($1);
        var val = decodeURIComponent($2.replace(/\+/g, '%20'));
        val = String(val);
        obj[name] = val;
        return rs;
      });
      return obj;
    },
    getUseBusiData(callBack) {
      if (!this.form.CALLER) return
      var params = {
        svc: "34317",
        para: {
          phonenumber: this.form.CALLER || "",
        },
      }
      return yq.remoteCall('/cx-mix-api/servlet/ayyBusiServlet?action=busiData', params, data => {
        callBack && callBack(data)
      })
    },
    getUseBusiList(callBack) {
      if (!this.form.CALLER) return
      var params = {
        svc: "80023",
        para: {
          phonenumber: this.form.CALLER || "",
        },
      }
      return yq.remoteCall('/cx-mix-api/servlet/ayyBusiServlet?action=busiData', params, data => {
        callBack && callBack(data)
      })
    },
    getBackBusiData(callBack, startData, endData) {
      if (!this.form.CALLER) return
      var params = {
        svc: "80028",
        para: {
          phonenumber: this.form.CALLER || "",
          startData: startData || '',
          endData: endData || ''
        },
      }
      return yq.remoteCall('/cx-mix-api/servlet/ayyBusiServlet?action=busiData', params, data => {
        callBack && callBack(data)
      })
    },
    getBuyBusiData(callBack, startData, endData) {
      if (!this.form.CALLER) return
      var params = {
        svc: "9099",
        para: {
          sign: 1,
          phonenumber: this.form.CALLER || "",
          startData: startData || '',
          endData: endData || '',
          pro: this.isProvince ? "9099" : ''
        },
      }
      return yq.remoteCall('/cx-mix-api/servlet/ayyBusiServlet?action=busiData', params, data => {
        callBack && callBack(data)
      })
    },
    getBuyOtherBusiData(callBack, startData, endData) {
      if (!this.form.CALLER) return
      var params = {
        svc: "90010",
        para: {
          phonenumber: this.form.CALLER || "",
          startData: startData || '',
          endData: endData || ''
        },
      }
      return yq.remoteCall('/cx-mix-api/servlet/ayyBusiServlet?action=busiData', params, data => {
        callBack && callBack(data)
      })
    },
    getUnifyBusiData(callBack, startData, endData) {
      if (!this.form.CALLER) return
      var params = {
        svc: "900232",
        para: {
          phonenumber: this.form.CALLER || "",
          startData: startData || '',
          endData: endData || ''
        },
      }
      return yq.remoteCall('/cx-mix-api/servlet/ayyBusiServlet?action=busiData', params, data => {
        callBack && callBack(data)
      })
    },
    // 缓存接口
    addProductCache() {
      const data = {
        phonenumber: this.form.CALLER,
        stat: this.searchForm.date && this.searchForm.date[0] || '',
        end: this.searchForm.date && this.searchForm.date[1] || '',
      }
      yq.remoteCall('/cx-mix-api//servlet/orderBusiServlet?action=addProductCache', data, res => {

      })
    },
    //查询退费次数/收费次数
    getCount(callBack, startData, endData, productId, productCode) {
      if (!this.form.CALLER) return
      var params = {
        phonenumber: this.form.CALLER || "",
        stat: startData || '',
        end: endData || '',
        productId: productId || "",
        productCode: productCode || ""
      }
      return yq.remoteCall('/cx-mix-imusic/servlet/orderBusiServlet?action=addCount', params, data => {
        callBack && callBack(data)
      })
    },


  },
}

// 各个节点对应的mixin
var nodeMixins = {
  'USERTASK_w3zyktmeba': {
    created() {
    },
    mounted() {

    }
  }
}