/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.0.0-1 (2019-02-04)
 */
!function(){"use strict";var n,e,t,r,o,u,i=function(n){var e=n,t=function(){return e};return{get:t,set:function(n){e=n},clone:function(){return i(t())}}},c=tinymce.util.Tools.resolve("tinymce.PluginManager"),a=function(n){return{isEnabled:function(){return n.get()}}},f=function(n,e){return n.fire("VisualChars",{state:e})},l={"\xa0":"nbsp","\xad":"shy"},s=function(n,e){var t,r="";for(t in n)r+=t;return new RegExp("["+r+"]",e?"g":"")},d=function(n){var e,t="";for(e in n)t&&(t+=","),t+="span.mce-"+n[e];return t},m={charMap:l,regExp:s(l),regExpGlobal:s(l,!0),selector:d(l),charMapToRegExp:s,charMapToSelector:d},N=function(n){return function(){return n}},g=N(!1),E=N(!0),h=g,p=E,v=function(){return T},T=(r={fold:function(n,e){return n()},is:h,isSome:h,isNone:p,getOr:t=function(n){return n},getOrThunk:e=function(n){return n()},getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:function(){return null},getOrUndefined:function(){return undefined},or:t,orThunk:e,map:v,ap:v,each:function(){},bind:v,flatten:v,exists:h,forall:p,filter:v,equals:n=function(n){return n.isNone()},equals_:n,toArray:function(){return[]},toString:N("none()")},Object.freeze&&Object.freeze(r),r),O=function(t){var n=function(){return t},e=function(){return o},r=function(n){return n(t)},o={fold:function(n,e){return e(t)},is:function(n){return t===n},isSome:p,isNone:h,getOr:n,getOrThunk:n,getOrDie:n,getOrNull:n,getOrUndefined:n,or:e,orThunk:e,map:function(n){return O(n(t))},ap:function(n){return n.fold(v,function(n){return O(n(t))})},each:function(n){n(t)},bind:r,flatten:n,exists:r,forall:r,filter:function(n){return n(t)?o:T},equals:function(n){return n.is(t)},equals_:function(n,e){return n.fold(h,function(n){return e(t,n)})},toArray:function(){return[t]},toString:function(){return"some("+t+")"}};return o},y=function(n){return null===n||n===undefined?T:O(n)},D=(o="function",function(n){return function(n){if(null===n)return"null";var e=typeof n;return"object"===e&&Array.prototype.isPrototypeOf(n)?"array":"object"===e&&String.prototype.isPrototypeOf(n)?"string":e}(n)===o}),_=function(n,e){for(var t=0,r=n.length;t<r;t++)e(n[t],t,n)},C=(Array.prototype.slice,D(Array.from)&&Array.from,function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:N(n)}}),M={fromHtml:function(n,e){var t=(e||document).createElement("div");if(t.innerHTML=n,!t.hasChildNodes()||1<t.childNodes.length)throw console.error("HTML does not have a single root node",n),new Error("HTML must have a single root node");return C(t.childNodes[0])},fromTag:function(n,e){var t=(e||document).createElement(n);return C(t)},fromText:function(n,e){var t=(e||document).createTextNode(n);return C(t)},fromDom:C,fromPoint:function(n,e,t){var r=n.dom();return y(r.elementFromPoint(e,t)).map(C)}},A=(Node.ATTRIBUTE_NODE,Node.CDATA_SECTION_NODE,Node.COMMENT_NODE,Node.DOCUMENT_NODE,Node.DOCUMENT_TYPE_NODE,Node.DOCUMENT_FRAGMENT_NODE,Node.ELEMENT_NODE,Node.TEXT_NODE),b=(Node.PROCESSING_INSTRUCTION_NODE,Node.ENTITY_REFERENCE_NODE,Node.ENTITY_NODE,Node.NOTATION_NODE,function(n){return n.dom().nodeValue}),S=(u=A,function(n){return n.dom().nodeType===u}),k=function(n){return'<span data-mce-bogus="1" class="mce-'+m.charMap[n]+'">'+n+"</span>"},x=function(n,e){var t=[],r=function(n,e){for(var t=n.length,r=new Array(t),o=0;o<t;o++){var u=n[o];r[o]=e(u,o,n)}return r}(n.dom().childNodes,M.fromDom);return _(r,function(n){e(n)&&(t=t.concat([n])),t=t.concat(x(n,e))}),t},w={isMatch:function(n){return S(n)&&b(n)!==undefined&&m.regExp.test(b(n))},filterDescendants:x,findParentElm:function(n,e){for(;n.parentNode;){if(n.parentNode===e)return n;n=n.parentNode}},replaceWithSpans:function(n){return n.replace(m.regExpGlobal,k)}},P=function(t,n){var r,o,e=w.filterDescendants(M.fromDom(n),w.isMatch);_(e,function(n){var e=w.replaceWithSpans(b(n));for(o=t.dom.create("div",null,e);r=o.lastChild;)t.dom.insertAfter(r,n.dom());t.dom.remove(n.dom())})},I=function(e,n){var t=e.dom.select(m.selector,n);_(t,function(n){e.dom.remove(n,1)})},B=P,R=I,U=function(n){var e=n.getBody(),t=n.selection.getBookmark(),r=w.findParentElm(n.selection.getNode(),e);r=r!==undefined?r:e,I(n,r),P(n,r),n.selection.moveToBookmark(t)},V=function(n,e){var t,r=n.getBody(),o=n.selection;e.set(!e.get()),f(n,e.get()),t=o.getBookmark(),!0===e.get()?B(n,r):R(n,r),o.moveToBookmark(t)},j=function(n,e){n.addCommand("mceVisualChars",function(){V(n,e)})},q=tinymce.util.Tools.resolve("tinymce.util.Delay"),G=function(e,t){var r=q.debounce(function(){U(e)},300);!1!==e.settings.forced_root_block&&e.on("keydown",function(n){!0===t.get()&&(13===n.keyCode?U(e):r())})},H=function(n){return n.getParam("visualchars_default_state",!1)},L=function(e,t){e.on("init",function(){var n=!H(e);t.set(n),V(e,t)})},F=function(t,r){return function(e){e.setActive(r.get());var n=function(n){return e.setActive(n.state)};return t.on("VisualChars",n),function(){return t.off("VisualChars",n)}}};c.add("visualchars",function(n){var e,t,r=i(!1);return j(n,r),t=r,(e=n).ui.registry.addToggleButton("visualchars",{tooltip:"Show invisible characters",icon:"paragraph",onAction:function(){return e.execCommand("mceVisualChars")},onSetup:F(e,t)}),e.ui.registry.addToggleMenuItem("visualchars",{text:"Show invisible characters",onAction:function(){return e.execCommand("mceVisualChars")},onSetup:F(e,t)}),G(n,r),L(n,r),a(r)}),function Y(){}}();