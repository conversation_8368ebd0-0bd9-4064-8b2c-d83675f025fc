<!DOCTYPE html>
<html>

<head>
    <title>退赔功能费配置页</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
    <!-- 基础的 css js 资源 -->
    <link
            rel="stylesheet"
            href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css"
    />
    <link
            rel="stylesheet"
            href="/easitline-cdn/vue-yq/theme/core.css?v=1.1.0"
    />
    <link
            rel="stylesheet"
            href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.0"
    />
    <link
            rel="stylesheet"
            href="/cc-quality/static/css/qualityControl.css?v=1.0.0"
    />
    <script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
    <script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
    <script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
    <script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.0"></script>
    <script src="/cc-quality/static/lib/wavesurfer/wavesurfer.js"></script>
    <script src="/cc-quality/static/lib/wavesurfer/wavesurfer.timeline.min.js"></script>
    <script
            type="text/javascript"
            src="/cc-quality/static/js/my_i18n.js?v=2021110901"
    ></script>
    <script
            type="text/javascript"
            src="/cc-base/static/js/i18n.js?v=20140426"
    ></script>
    <style>
        .yq-table-page {
            background-color: #fff;
        }

        .title-box {
            padding: 16px;
            border-bottom: 1px solid #E8E8E8;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .search-box {
            padding: 10px 24px 0;
        }

        .el-form.el-form--inline .el-input__inner {
            height: auto !important;
            line-height: auto !important;
            background: #f2f4f7;
        }

        .table-box {
            padding: 24px;
            padding-top: 0;
        }
    </style>
</head>

<body class="yq-page-full vue-box">
<div id="functionFee" class="flex yq-table-page" element-loading-text="加载中..." v-cloak>
    <div class="flex">
        <div class="title-box">
            <div class="title">退赔功能费配置页</div>
            <div>
                <!-- <el-button size="small" type="primary">质检结果明细</el-button> -->
                <el-button size="small" type="primary"  @click="add">添加</el-button>
            </div>
        </div>
        <div class="search-box">
            <el-form :inline="true" :model="formData" ref="form" inline>
              <el-form-item label="省份" prop="province">
                <el-select v-model="formData.provinceCode" placeholder="省份">
                    <el-option v-for="(key,value) of provinces" :label="key" :value="value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="产品名称" prop="func">
                    <el-input
                            placeholder="请输入内容"
                            v-model="formData.productName">
                    </el-input>
              </el-form-item>

            <el-form-item style="margin-left: 70px;">
                <el-button type="primary" size="small" icon="el-icon-search" @click="doSearch">{{getI18nValue('查询')}}</el-button>
            </el-form-item>
                
            </el-form>
        </div>
        <div class="table-box yq-table">
            <el-table :data="tableData" stripe fit style="width: 100%" height="100%" v-loading="pageNav.loading"
                      :default-sort= "{prop: 'CREATE_TIME', order: 'descending'}" >
                <el-table-column type="index" :label="getI18nValue('选择')" width="50" fixed>
                </el-table-column>
                <el-table-column prop="PROVINCE_NAME" :label="getI18nValue('省份')"   show-overflow-tooltip sortable min-width="150">
                </el-table-column>
                <el-table-column prop="FUNCTION_NAME" :label="getI18nValue('产品名称')"   show-overflow-tooltip sortable min-width="150">
                </el-table-column>
                <el-table-column prop="FUNCTION_ID" :label="getI18nValue('产品ID')"   show-overflow-tooltip sortable min-width="150">
                </el-table-column>
                <el-table-column prop="CREATE_TIME" :label="getI18nValue('添加时间')"   show-overflow-tooltip sortable min-width="150">
                </el-table-column>
                <el-table-column prop="CREATE_ACC" :label="getI18nValue('操作人')" min-width="150" show-overflow-tooltip sortable min-width="150">
                </el-table-column>
                
                <el-table-column prop="opt" :label="getI18nValue('操作')" width="200" fixed="right">
                    <template slot-scope="scope">
                        <el-link type="primary" size="small" 
                                     @click="del(scope.row)">{{getI18nValue('删除')}}</el-link>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                           :current-page="pageNav.page" :page-sizes="[10, 15, 20, 30]" :page-size="pageNav.size"
                           layout="total, sizes, prev, pager, next, jumper" :total="pageNav.total">
            </el-pagination>
        </div>

        <el-dialog :model="smallForm" :title="dialogTitle" :visible.sync="dialogFormVisible">
            <el-form :model="smallForm" ref="smallForm">
                <el-form-item :label="getI18nValue('省份')"  prop="PROVINCE_CODE" :rules="[{ required: true, message: '不能为空'}]">
                    <el-select v-model="smallForm.PROVINCE_CODE" :placeholder="getI18nValue('请选择')"
                    style="width:100%;"
                    :disabled="!showDetailValue">
                        <el-option v-for="(key,value) of provinces" :label="key" :value="value"></el-option>
                    </el-select>
                  </el-form-item>
    
                <el-form-item label="产品名称"   prop="FUNCTION_NAME" :rules="[{ required: true, message: '不能为空'}]" >
                    <el-input
                        placeholder="请输入内容"
                        v-model="smallForm.FUNCTION_NAME">
                        </el-input>
                  </el-form-item>
                  <el-form-item label="产品ID"   prop="FUNCTION_ID" :rules="[{ required: true, message: '不能为空'}]" >
                      <el-input
                          placeholder="请输入内容"
                          v-model="smallForm.FUNCTION_ID">
                          </el-input>
                    </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
              <el-button @click="dialogFormVisible = false">取 消</el-button>
              <el-button v-if="showDetailValue == true " type="primary" @click="save" >确 定</el-button>
            </div>
          </el-dialog>
    </div>

</div>

<script>
    // const iframeComm = new IframeCommunication();
    // 每个页面的容器id和实例变量要不一样
    var orderFramePage = new Vue({
        components: { //加载组件
            // 'orderMainFrame': httpVueLoader('comp/orderMainFrame.vue')
        },
        el: '#functionFee',
        data: function () {
            return {
              provinces: {
                "0":"全部",
                "1":"广东省",
                "2":"广西省",
                "3":"湖南省",
                "4":"湖北省",
                "5":"陕西省",
                "6":"东北省",
                
              },
              funces:{
                "0":"全部",
                "1" : "视频彩铃",
                "2" : "音频彩铃",
                "3" : "全能听"
              },
            dialogFormVisible: false,
            showDetailValue: true,
            dialogTitle: "添加",
            formData: {
                provinceCode: '',
                productName: '',
                productId: '',
            },
            smallForm: {
                PROVINCE_CODE: "",
                PROVINCE_NAME: "",
                FUNCTION_NAME : "",
                FUNCTION_ID: ""
            },
            pageNav: {
                size: 20,
                page: 1,
                total: 0,
                loading: false
            },
            tableData: [],
            show: false,
            }
        },
        computed: {

        },
        beforeCreate() {
        },
        created() {

        },
        watch: {
            'formData.date': {
                handler(n) {
                    if (yq.isNull(n) && n.length > 0) {
                        return;
                    }
                    this.formData.beginStartDate = n[0]
                    this.formData.endStartDate = n[1]
                }
            },
            'formData.date2': {
                handler(n) {
                    if (yq.isNull(n) && n.length > 0) {
                        return;
                    }
                    this.formData.beginServiceTime = n[0]
                    this.formData.endServiceTime = n[1]
                }
            }
        },
        methods: {
            save() {
                this.$refs.smallForm.validate(valid => {
                        if (valid) {
                            var _this = this
                            _this.smallForm.PROVINCE_NAME = _this.provinces[_this.smallForm.PROVINCE_CODE]

                            if (_this.smallForm.PROVINCE_CODE === "" || _this.smallForm.FUNCTION_NAME === "" || _this.smallForm.FUNCTION_ID === "") return ;
                            var query = {}
                            for (var key in _this.smallForm) {
                                query["funcObject." + key] = _this.smallForm[key]
                            }
                            yq.remoteCall("/cx-mix-imusic/servlet/compensationFunc?action=addOrEditFunc", query , function (result) {
                                // if (result.state == 1) {
                                //
                                // }
                                _this.dialogFormVisible = false
                                _this.$message({type: "success", message: result.msg})

                                _this.doSearch()
                                _this.smallForm = {}
                            })
                        }
                })

            },
          add() {
            this.dialogFormVisible = true
          },

          doSearch() {

              let data = {
                  data: this.formData,
                  pageSize: this.pageNav.size,
                  pageIndex: this.pageNav.page,
                  pageType: '3'
              }

              let that = this
              yq.tableCall('/cx-mix-imusic/webcall?action=compensationFuncDao.getList', data, function (res) {
                  if (res.state == 1) {
                      that.tableData = res.data
                      that.pageNav.total = res.totalRow
                  }
              }).finally(res => {
                  this.pageNav.loading = false
              })
          },
            handleCurrentChange(val) {
                this.pageNav.page = val
                this.doSearch()
            },
            handleSizeChange(val) {
                this.pageNav.size = val
                this.doSearch()
            },
          del(row) {
              this.$confirm(getI18nValue('是否删除该信息?'), getI18nValue('提示'), {
                  confirmButtonText: getI18nValue('确定'),
                  cancelButtonText: getI18nValue('取消'),
                  type: 'warning'
              }).then(() => {
                  this.delApi([row.ID])
              }).catch(() => {
              });


          },
            delApi: function(ids) {
                var _this = this;
                var query = {
                    ids
                }
                yq.remoteCall("/cx-mix-imusic/servlet/compensationFunc?action=delete", query , function (result) {
                    _this.$message({type: "success", message: result.msg})
                    _this.doSearch()
                })
            },

            getDict() {
                let data = {
                    params: {},
                    controls: ["common.getProvinceDict"]
                }
                let _this = this
                yq.daoCall(data, null, {
                    "contextPath": "/cx-mix-imusic"
                }).then(function (res) {//接口的方式渲染
                    _this.provinces = res["common.getProvinceDict"].data

                })
            }
        },
        mounted: function () {
            this.doSearch()
            this.getDict()
        }
    })
</script>
</body>

</html>