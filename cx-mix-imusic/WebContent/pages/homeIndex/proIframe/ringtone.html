<!DOCTYPE html>
<html>
<head>
    <title>在用业务</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta
            name="viewport"
            content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no"
    />
    <!-- 基础的 css js 资源 -->
    <link
            rel="stylesheet"
            href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css"
    />
    <link
            rel="stylesheet"
            href="/easitline-cdn/vue-yq/theme/core.css?v=1.1.0"
    />
    <script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
    <script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
    <script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
    <script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.0"></script>
    <!-- 引用几个页面共用的css文件 -->
    <link rel="stylesheet" href="/cx-mix-imusic/pages/information/css/index.css"/>
    <style>

        .el-table .warning-row {
            background: oldlace;
        }

        .el-table .success-row {
            background: #b99b8f;
        }

        .el-checkbox .el-checkbox__inner {
            background: #ffffff;
            border: 1px solid rgba(38, 38, 38, 0.2);
        }

        .el-checkbox__input.is-disabled .el-checkbox__inner {
            background-color: #f7f7f7;
            border-color: #dcdfe6;
            cursor: not-allowed;
        }

        /* .el-checkbox-inner {
            background: #ffffff;
            border: 1px solid rgba(38, 38, 38, 0.2);
          } */
    </style>
</head>

<body class="page">
<div id="ringtoneAll" style="display: flex; flex-direction: column">
    <div
            class=""
            style="display: flex;"
            v-if="!isIframe"
            v-auth:[permissions]="'cx-mix-imusic-pro-btn'"
    >
        <div style="display: flex;align-items: center;flex-wrap: wrap">
            <div style="margin-right: 10px;">查询时间</div>
            <el-date-picker
                    size="small"
                    style="width:200px"
                    v-model="formList.date[0]"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择日期">
            </el-date-picker>
            <div style="margin: 0 5px">~</div>
            <el-date-picker
                    size="small"
                    style="width:200px"
                    v-model="formList.date[1]"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择日期">
            </el-date-picker>
            <el-input
                    size="small"
                    style="width: 210px;margin-right: 10px; margin-left: 10px"
                    v-model.trim="formList.name"
                    placeholder="请输入手机号码"
                    clearable
            >
            </el-input>
            <el-button
                    @click="searchPhone"
                    icon="el-icon-search"
                    type="primary"
                    size="small"
            >查询
            </el-button>
            <el-button v-if="permissions['cx-mix-imusic-pro-btn']" type="primary" size="small" @click="SengOrder"
            >派单
            </el-button>
            <el-button type="primary" size="small" @click="unOrder"
            >退订
            </el-button>
            <el-button type="primary" size="small" @click="UnsubRingtone"
            >退订商务彩铃
            </el-button>
            <el-button type="primary" size="small" @click="delUser"
            >退订视频彩铃功能
            </el-button>
            <!--      <el-button type="primary" size="small" @click="settingPayMode"-->
            <!--      >设置播放模式-->
            <!--      </el-button>-->
            <!--      <el-button type="primary" size="small" @click="settingDefaultRing"-->
            <!--      >设置默认彩铃-->
            <!--      </el-button>-->
            <el-button type="primary" size="small" @click="skipBlack"
            >黑名单
            </el-button>
            <el-button type="primary" size="small" @click="skipWhite"
            >白名单
            </el-button>
            <!--      <el-button type="primary" size="small" @click="toAuth"-->
            <!--      >鉴权-->
            <!--      </el-button>-->
        </div>
    </div>
    <div>
        <el-form
                :inline="false"
                ref="form"
                size="small"
                label-width="100px"
                style="display: flex; height: 10px; margin-top: 15px"
        >
            <el-form-item label="功能属性" show-overflow-tooltip prop="funcClass">
                <el-input readonly v-model="funcClass"></el-input>
            </el-form-item>
            <el-form-item label="用户属性" show-overflow-tooltip prop="userClass">
                <el-input readonly v-model="userClass"></el-input>
            </el-form-item>
        </el-form>
    </div>
    <div style="margin-top: 30px; max-height: 83vh; overflow-y: scroll">
        <el-collapse v-model="activeNames">
            <el-collapse-item title="在用产品列表" name="1">
                <el-table
                        :data="totalList"
                        v-loading="loading1"
                        @selection-change="totalChange"
                        stripe
                        border
                        empty-text="在用产品列表为空"
                        max-height="300"
                        :default-sort="{prop: 'orderTime', order: 'descending'}"
                >
                    <el-table-column

                            :resizable="true" type="selection" width="55"></el-table-column>

                    <el-table-column

                            :resizable="true"
                            show-overflow-tooltip
                            prop="productName" sortable
                            label="产品名称"
                            min-width="110"
                    >
                    </el-table-column>
                    <el-table-column

                            :resizable="true"
                            show-overflow-tooltip
                            prop="productid"
                            label="产品ID" sortable
                    >
                    </el-table-column>

                    <el-table-column

                            :resizable="true"
                            show-overflow-tooltip
                            prop="feeProductId"
                            label="集团计费平台编码" sortable min-width="140"
                    >
                    </el-table-column>
                    <el-table-column

                            :resizable="true"
                            show-overflow-tooltip
                            prop="packageType"
                            label="产品类型" sortable
                    >
                    </el-table-column>
                    <el-table-column

                            :resizable="true"
                            show-overflow-tooltip
                            prop="price"
                            label="价格" sortable
                    >
                        <template slot-scope="scope">
                            {{ scope.row.price > 50 ? scope.row.price / 100 : scope.row.price }}
                        </template>
                    </el-table-column>
                    <el-table-column
                            :resizable="true"
                            show-overflow-tooltip
                            prop="orderTime"
                            label="订购时间" sortable
                    >
                    </el-table-column>
                    <el-table-column

                            :resizable="true"
                            show-overflow-tooltip
                            prop="state" sortable
                            label="订购状态"
                    >
                    </el-table-column>
                    <el-table-column
                      :resizable="true"
                      show-overflow-tooltip
                      prop="publish_id"
                      label="备案号"
                      sortable
                      min-width="110"
                      >
                    </el-table-column>
                    <el-table-column

                      :resizable="true"
                      show-overflow-tooltip
                      prop="imusic_product_name"
                      label="产品权益介绍"
                      min-width="140"
                    >
                    </el-table-column>
                    <el-table-column
                            :resizable="true"
                            label="操作"
                    >
                        <template slot-scope="scope">
                            <!--v-if="scope.row.state=='未退订'"-->
                            <el-link @click="toUnsub(scope.row)" type="danger">退订</el-link>
                        </template>
                    </el-table-column>

                </el-table>
            </el-collapse-item>

            <el-collapse-item title="商户彩铃列表" name="2">
                <template slot="title">
                    <div>商户彩铃列表 <span
                            style="color: #c9302c">(退订音乐名片、商务音视频彩铃业务，请通过“商彩在用列表”进行退订或上方“退订商务彩铃”按键退订！)</span>
                    </div>
                </template>
                <div id="busiRingList">
                    <el-table
                            v-loading="loading2"
                            :data="busiRingList"
                            :empty-text="emptyText4"
                            @selection-change="busiRingListChange"
                            stripe
                            border
                            max-height="300"
                            :default-sort="{prop: 'videoSettingTime', order: 'descending'}"
                    >
                        <el-table-column

                                :resizable="true" type="selection" width="55"></el-table-column>

                        <el-table-column

                                :resizable="true"
                                show-overflow-tooltip
                                prop="apersonnelPhone"
                                label="成员号码"
                                min-width="110" sortable
                        ></el-table-column>
                        <el-table-column

                                :resizable="true"
                                show-overflow-tooltip
                                prop="userName" min-width="130"
                                label="商户名称" sortable
                        >
                        </el-table-column>
                        <el-table-column

                                :resizable="true"
                                show-overflow-tooltip min-width="130"
                                prop="apersonnelAuditMsg"
                                label="审核状态" sortable
                        >
                        </el-table-column>
                        <el-table-column

                                :resizable="true"
                                show-overflow-tooltip min-width="130"
                                prop="feeType"
                                label="收费模式"
                                min-width="110" sortable
                        >
                            <template slot-scope="scope">
                                {{ scope.row.feeType == 1 ? "个付" : (scope.row.feeType == 2 ? "统付" : "阶梯付费") }}
                            </template>
                        </el-table-column>
                        <el-table-column

                                :resizable="true"
                                show-overflow-tooltip min-width="130"
                                prop="auserMoney"
                                label="商户资费"
                                align="center"
                                width="270" sortable
                        >
                            <template slot="header" slot-scope="scope">
                              <div style="text-align: center;">商户资费</div>
                              <span style="font-size: 12px;color: #868686;">（支付方式如为统付，成功号码不收费）</span>
                            </template>
                            <template slot-scope="scope">
                                {{ scope.row.auserMoney > 50 ? scope.row.auserMoney / 100 : scope.row.auserMoney }}
                            </template>
                        </el-table-column>
                        <el-table-column

                                :resizable="true"
                                show-overflow-tooltip min-width="130"
                                prop="isUnifyPayMsg"
                                label="收费方式" sortable
                        >
                        </el-table-column>
                        <el-table-column

                                :resizable="true"
                                show-overflow-tooltip min-width="130"
                                prop="isVideoAuser"
                                label="商彩类型" sortable
                        >
                            <template slot-scope="scope">
                                {{ scope.row.isVideoAuser == 1 ? "视频" : "音频" }}
                            </template>
                        </el-table-column>
                        <el-table-column

                                :resizable="true"
                                show-overflow-tooltip min-width="130"
                                prop="productMsg"
                                label="业务名称" sortable
                        >
                        </el-table-column>
                        <el-table-column

                                :resizable="true"
                                show-overflow-tooltip min-width="130"
                                prop="videoRingCode"
                                label="视频业务编码" sortable
                        >
                        </el-table-column>
                        <el-table-column

                                :resizable="true"
                                show-overflow-tooltip min-width="180"
                                prop="ringCode"
                                label="音乐名片业务编码" sortable
                        ></el-table-column>

                        <el-table-column

                                :resizable="true"
                                show-overflow-tooltip min-width="130"
                                prop="apersonnelCreateTime"
                                label="用户开通时间" sortable
                        >
                        </el-table-column>

                        <el-table-column

                                :resizable="true"
                                show-overflow-tooltip min-width="130"
                                prop="openDoor"
                                label="是否本地网"
                        >
                            <template slot-scope="scope">
                                {{ scope.row.openDoor == 0 ? "否" : "是" }}
                            </template>
                        </el-table-column>
                        <el-table-column

                                :resizable="true"
                                show-overflow-tooltip min-width="130"
                                prop="openDoorMsg" sortable
                                label="开通门户"
                        >
                        </el-table-column>

                        <!--                        <el-table-column-->

                        <!--                                :resizable="true"-->
                        <!--                                show-overflow-tooltip min-width="130"-->
                        <!--                                prop="mainChannelName"-->
                        <!--                                label="主渠道商" sortable-->
                        <!--                                min-width="110"-->
                        <!--                        ></el-table-column>-->
                        <el-table-column

                                :resizable="true"
                                show-overflow-tooltip min-width="130"
                                prop="apersonnelRingStateMsg"
                                label="铃音状态" sortable
                        >
                        </el-table-column>
                        <el-table-column

                                :resizable="true"
                                show-overflow-tooltip min-width="130"
                                prop="provinceName"
                                label="省份" sortable
                        >
                        </el-table-column>
                        <!--                        <el-table-column-->

                        <!--                                :resizable="true"-->
                        <!--                                show-overflow-tooltip min-width="130"-->
                        <!--                                prop="isSendSmsMsg"-->
                        <!--                                label="短信状态" sortable-->
                        <!--                                min-width="110"-->
                        <!--                        ></el-table-column>-->
                        <el-table-column

                                :resizable="true"
                                show-overflow-tooltip min-width="130"
                                prop="isRingUserMsg" sortable
                                label="彩铃功能"
                        >
                        </el-table-column>
                        <el-table-column

                                :resizable="true"
                                show-overflow-tooltip min-width="130"
                                prop="sourceMsg"
                                label="群组类型" sortable
                        >
                        </el-table-column>
                        <el-table-column

                                :resizable="true"
                                show-overflow-tooltip min-width="130"
                                prop="videoSettingTime"
                                label="视频设置时间" sortable
                        >
                        </el-table-column>
                        <el-table-column

                                :resizable="true"
                                show-overflow-tooltip min-width="180"
                                prop="settingTime"
                                label="音乐名片设置时间"
                                sortable
                        ></el-table-column>

                        <!--                        <el-table-column-->

                        <!--                                :resizable="true"-->
                        <!--                                show-overflow-tooltip min-width="130"-->
                        <!--                                prop="channelName"-->
                        <!--                                label="子渠道商" sortable-->
                        <!--                        >-->
                        <!--                        </el-table-column>-->
                        <!--                        <el-table-column-->

                        <!--                                :resizable="true"-->
                        <!--                                show-overflow-tooltip min-width="130"-->
                        <!--                                prop="basere"-->
                        <!--                                label="计费类型" sortable-->
                        <!--                        >-->
                        <!--                        </el-table-column>-->
                    </el-table>
                </div>
            </el-collapse-item>
            <el-collapse-item title="历史产品列表" name="3">
                <div id="orderRecordList">
                    <!-- <div class="title">订购记录</div> -->
                    <el-table
                            v-loading="loading3"
                            @sort-change="sortChange"
                            ref="orderRecordData"
                            :data="orderRecordData"
                            @selection-change="orderRecordDataChange"
                            stripe
                            border
                            max-height="300"
                            :default-sort="{prop: 'orderTime', order: 'descending'}">
                        <el-table-column

                                :resizable="true" type="selection" width="55"></el-table-column>
                        <!--                        <el-table-column-->

                        <!--                                :resizable="true"-->
                        <!--                                show-overflow-tooltip prop="phoneNumber"-->
                        <!--                                label="用户号码"-->
                        <!--                                min-width="110" sortable>-->
                        <!--                        </el-table-column>-->
                        <el-table-column

                                :resizable="true" show-overflow-tooltip prop="productName" sortable
                                label="产品名称"></el-table-column>
                        <el-table-column

                                :resizable="true"
                                show-overflow-tooltip prop="productId"
                                label="产品ID" sortable
                                min-width="110">
                        </el-table-column>
                        <el-table-column

                                :resizable="true" show-overflow-tooltip prop="otherid" sortable label="产品编码">
                            <template slot-scope="scope">
                                {{ scope.row.otherid == "null" ? "" : scope.row.otherid }}
                            </template>
                        </el-table-column>

                        <el-table-column
                                :resizable="true" show-overflow-tooltip prop="subject" sortable label="产品类型">
                            <!--                            <template slot-scope="scope">-->
                            <!--                                {{ PRODUCT_TYPES[scope.row.productType]}}-->
                            <!--                            </template>-->
                        </el-table-column>
                        <el-table-column

                                :resizable="true" show-overflow-tooltip prop="price" sortable label="价格">
                            <template slot-scope="scope">
                                {{ scope.row.price > 50 ? scope.row.price / 100 : scope.row.price }}
                            </template>
                        </el-table-column>
                        <!--                        <el-table-column-->

                        <!--                                :resizable="true" show-overflow-tooltip prop="opResult" sortable-->
                        <!--                                label="返回码描述"></el-table-column>-->
                        <el-table-column

                                :resizable="true" show-overflow-tooltip prop="channel" sortable
                                label="开通方式"></el-table-column>
                        <el-table-column

                                :resizable="true" show-overflow-tooltip prop="orderTime" sortable
                                label="订购时间" min-width="120"></el-table-column>
                        <el-table-column

                                :resizable="true" show-overflow-tooltip prop="ununTime" sortable="custom"
                                label="退订时间" min-width="120"></el-table-column>
                        <el-table-column
                          :resizable="true"
                          show-overflow-tooltip
                          prop="publish_id"
                          label="备案号"
                          sortable
                          min-width="110"
                          >
                        </el-table-column>
                        <el-table-column
    
                          :resizable="true"
                          show-overflow-tooltip
                          prop="imusic_product_name"
                          label="产品权益介绍"
                          min-width="140"
                        >
                        </el-table-column>

                        <!--                        <el-table-column-->

                        <!--                                :resizable="true" show-overflow-tooltip prop="department" sortable-->
                        <!--                                label="所属部门"></el-table-column>-->
                        <!--                        <el-table-column-->

                        <!--                                :resizable="true" show-overflow-tooltip prop="returnCode" sortable-->
                        <!--                                label="返回码"></el-table-column>-->
                        <!--                        <el-table-column-->

                        <!--                                :resizable="true" show-overflow-tooltip prop="orderType" sortable-->
                        <!--                                label="支付方式"></el-table-column>-->

                    </el-table>
                </div>
            </el-collapse-item>
            <el-collapse-item title="功能开通/销户记录" name="4">
                <el-table v-loading="loading4" :data="videoRingRecord"
                          :default-sort="{prop: 'recordtime', order: 'descending'}" stripe
                          border max-height="300">
                    <el-table-column :resizable="true" show-overflow-tooltip prop="phonenumber" sortable label="号码"
                                     min-width="150"></el-table-column>
                    <el-table-column :resizable="true" show-overflow-tooltip prop="business_type" sortable
                                     label="业务类型" min-width="150"></el-table-column>
                    <el-table-column :resizable="true" show-overflow-tooltip prop="action_desc" sortable
                                     label="开销户操作" min-width="150">
                        <template slot-scope="scope">
                            {{ scope.row.action_desc.includes("开户") ? "开户" : "销户" }}
                        </template>
                    </el-table-column>
                    <el-table-column :resizable="true" show-overflow-tooltip prop="recordtime" sortable label="操作时间"
                                     min-width="150"></el-table-column>
                    <el-table-column :resizable="true" show-overflow-tooltip prop="return_desc" sortable
                                     show-overflow-tooltip label="操作结果" min-width="150"></el-table-column>
                    <el-table-column :resizable="true" show-overflow-tooltip prop="prodoffer_name" sortable
                                     label="销售品名称" min-width="150"></el-table-column>
                    <el-table-column :resizable="true" show-overflow-tooltip prop="prodoffer_code" sortable
                                     label="销售品ID" min-width="150"></el-table-column>
                    <el-table-column
                    :resizable="true"
                    show-overflow-tooltip
                    prop="publish_id"
                    label="备案号"
                    sortable
                    min-width="110"
                    >
                  </el-table-column>
                  <el-table-column

                    :resizable="true"
                    show-overflow-tooltip
                    prop="imusic_product_name"
                    label="产品权益介绍"
                    min-width="140"
                  >
                  </el-table-column>
                    <!-- <el-table-column :resizable="true" show-overflow-tooltip prop="deviceid" sortable
                                     label="爱音乐内部门户ID" min-width="170"></el-table-column>
                    <el-table-column :resizable="true" show-overflow-tooltip prop="door_name" sortable label="门户名称"
                                     min-width="150"></el-table-column> -->
<!--                    <el-table-column :resizable="true" show-overflow-tooltip prop="channel" sortable label="订购渠道号"-->
<!--                                     min-width="150"></el-table-column>-->
                    <el-table-column :resizable="true" show-overflow-tooltip prop="channel_name" sortable
                                     label="订购渠道名" min-width="150"></el-table-column>
                    <el-table-column :resizable="true" show-overflow-tooltip prop="provincename" sortable label="省份"
                                     min-width="150"></el-table-column>
                    <el-table-column :resizable="true" show-overflow-tooltip prop="f13" sortable label="执行方"
                                     min-width="150"></el-table-column>
                </el-table>

            </el-collapse-item>
        </el-collapse>
    </div>

    <el-dialog
            :title="centerDialogVisibleTitle"
            :visible.sync="centerDialogVisible"
            width="60%"
            :close-on-click-modal="false"
            :before-close="selectClose"
            center
    >
        <div
                style="margin-bottom: 15px"
                v-if="centerDialogVisibleTitle == '设置播放模式'"
        >
            <span>请选择需要设置的模式</span>
            <el-select v-model="payMode" placeholder="请选择">
                <el-option
                        v-for="(key,value) of payModes"
                        :label="key"
                        :value="value"
                ></el-option>
            </el-select>
        </div>
        <span>你已选中以下产品，请确认</span>
        <div style="margin-top: 15px; max-height: 40vh; overflow-y: scroll">
            <div id="videoRing">
                <!-- <div class="title">视频彩铃</div> -->
                <el-table
                        :data="totalSelectList"
                        stripe
                        border
                >
                    <el-table-column

                            :resizable="true"
                            show-overflow-tooltip
                            prop="productName"
                            label="产品名称"
                    >
                    </el-table-column>
                    <el-table-column

                            :resizable="true"
                            show-overflow-tooltip
                            prop="productid"
                            label="产品ID"
                            min-width="110"
                    >
                    </el-table-column>

                    <el-table-column

                            :resizable="true"
                            show-overflow-tooltip
                            prop="price"
                            label="资费"
                    >
                    </el-table-column>
                    <el-table-column

                            :resizable="true" show-overflow-tooltip prop="unOrderState" label="退订结果">
                        <template slot-scope="scope">
                            {{ scope.row.unOrderState ? (scope.row.unOrderState == "0" ? "退订成功" : "退订失败") : ""
                            }}
                        </template>
                    </el-table-column>
                    <el-table-column
                            v-if="!isAllSuccess"
                            :resizable="true" show-overflow-tooltip prop="resMsg" label="失败原因">
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <template v-if="isOrderSu == true">
                 <el-button @click="selectClose">取 消</el-button>
                 <el-button type="primary" @click="unOrderSure">确 定</el-button>
            </template>
            <template v-else>
                <el-button type="primary" @click="selectClose">确 定</el-button>
            </template>
        </span>
    </el-dialog>
    <el-dialog
            title="功能销户"
            :show-close="false"
            :close-on-click-modal="false"
            :visible.sync="centerDialogVisible2"
            width="40%"
            center
    >
        <span v-if="isVringTitleContent || isRingTitleContent"
        >{{vringTitleContent}}</span
        >

        <div style="margin-top: 15px; max-height: 50vh; overflow-y: scroll">
            <div id="ringtone2" v-if="isRingTitleContent">
                <div class="title">{{ringTitle}}</div>
                <el-table
                        :data="ringtone2"
                        stripe
                        border
                        @selection-change="ringtoneChange2"
                        height="300"
                >
                    <el-table-column

                            :resizable="true" type="selection" width="55"></el-table-column>
                    <el-table-column

                            :resizable="true" show-overflow-tooltip prop="ringId" label="编码">
                    </el-table-column>
                    <el-table-column

                            :resizable="true"
                            show-overflow-tooltip
                            prop="ringName"
                            label="铃音名称"
                    >
                    </el-table-column>
                </el-table>
            </div>

            <div id="videoRing2" v-if="isVringTitleContent">
                <div class="title">{{vringTitle}}</div>
                <el-table
                        :data="videoRing2"
                        stripe
                        border
                        @selection-change="videoRingChange2"
                        height="300"
                >
                    <el-table-column

                            :resizable="true" type="selection" width="55"></el-table-column>
                    <el-table-column

                            :resizable="true"
                            show-overflow-tooltip
                            prop="ringId"
                            label="编码"
                            min-width="110"
                    ></el-table-column>
                    <el-table-column

                            :resizable="true" show-overflow-tooltip prop="name" label="名称">
                    </el-table-column>
                </el-table>
            </div>
        </div>

        <span slot="footer" class="dialog-footer">
          <el-button @click="closeDelDialog">取 消</el-button>
          <el-button type="primary" @click="SureDelUser">确 定</el-button>
        </span>
    </el-dialog>
</div>
<script>
    var appPage = new Vue({
        components: {
            //用于加载自定义组件
        },
        el: "#ringtoneAll",
        data: function () {
            return {
                isAllSuccess: true,
                loading1: false,
                loading2: false,
                loading3: false,
                loading4: false,
                permissions: {},
                totalList: [],
                orderRecordData: [],
                orderRecordDataCopy: [],
                orderRecordDataSelectList: [],
                videoRingRecord: [],
                isOrderSu: true,
                isShowRingtone: false,
                isShowISMP: false,
                isShowVring: false,
                isShowBusiRing: false,
                productIds: [],
                isIframe: false,
                iframeSelList1: [],
                payMode: "",
                payModes: {
                    0: "固定播放",
                    1: "随机播放",
                },
                IM_ORDER_CLAIMANT: {},
                centerDialogVisible2: false,
                isVringTitleContent: false,
                isRingTitleContent: false,
                vringTitleContent: "",
                centerDialogVisibleTitle: "",
                centerDialogVisible: false,
                ringTitle: "铃音",
                vringTitle: "视频彩铃",
                funcDist: {
                    0: "无功能",
                    1: "音频彩铃功能",
                    2: "视频彩铃功能",
                    3: "音频+视频彩铃功能",
                },
                userDist: {
                    0: "无",
                    1: "个彩",
                    2: "音乐名片",
                    3: "商务视频彩铃",
                },
                funcClass: "无功能",
                userClass: "无",
                activeNames: ["1", "2", "3", "4"],
                activeNames2: ["1", "2", "3", "4"],
                formList: {name: "", date: []},
                PRODUCT_TYPES: {
                    1: "彩铃",
                    2: "音乐盒",
                    3: "ISMP 业务",
                    4: "非ISMP 业务",
                    5: "视频彩铃",
                    6: "视频音乐盒"
                },
                ringtone: [],
                ringtone2: [],
                ISMP: [],
                videoRing: [],
                videoRing2: [],
                busiRingList: [],
                totalSelectList: [],
                ringtoneSelectList: [],
                ringtoneSelectList2: [],
                ismpSelectList: [],
                videoRingSelectList: [],
                videoRingSelectList2: [],
                busiRingListSelectList: [],
                flatOne: 1,
                flatTwo: 1,
                auth: 1,
                emptyText1: "暂无数据",
                emptyText3: "暂无数据",
                emptyText4: "暂无数据",
                oldphone: "",
                unableunRecordFlat: false,
                productResultList: [],
            };
        },
        watch: {
          totalList: {
            handler(newValue) {
              if (newValue && newValue.length) {
                if (this.activeNames.indexOf("1") === -1) {
                  this.activeNames.push("1")
                }
              } else {
                this.activeNames = this.activeNames.filter(item => item != "1")
              }
            },
            deep: true,
            immediate: true
          },
          busiRingList: {
            handler(newValue) {
              if (newValue && newValue.length) {
                if (this.activeNames.indexOf("2") === -1) {
                  this.activeNames.push("2")
                }
              } else {
                this.activeNames = this.activeNames.filter(item => item != "2")
              }
            },
            deep: true,
            immediate: true
          },
          orderRecordData: {
            handler(newValue) {
              if (newValue && newValue.length) {
                if (this.activeNames.indexOf("3") === -1) {
                  this.activeNames.push("3")
                }
              } else {
                this.activeNames = this.activeNames.filter(item => item != "3")
              }
            },
            deep: true,
            immediate: true
          },
          videoRingRecord: {
            handler(newValue) {
              if (newValue && newValue.length) {
                if (this.activeNames.indexOf("4") === -1) {
                  this.activeNames.push("4")
                }
              } else {
                this.activeNames = this.activeNames.filter(item => item != "4")
              }
            },
            deep: true,
            immediate: true
          },
        },
        methods: {
            sortChange(column) {
                if (column.order !== null && column.prop) {
                    let data1 = [];
                    let data2 = [];
                    for (let i = 0; i < this.orderRecordData.length; i++) {
                        let temp = null;
                        temp = this.orderRecordData[i][column.prop];
                        if (!temp) {
                            data2.push(this.orderRecordData[i]);
                        } else {
                            data1.push(this.orderRecordData[i]);
                        }
                    }
                    if (column.order === "ascending") {
                        data1 = data1.sort(this.compare(column.prop, "ascending"));
                    } else {
                        data1 = data1.sort(this.compare(column.prop, "descending"));
                    }
                    this.$nextTick(() => {
                        this.orderRecordData = data1.concat(data2);
                    });
                }
                if (column.order === null) {
                    this.orderRecordData = this.orderRecordDataCopy; // tabData存放的是list副本，不排序时恢复到初始状态
                }
            },
            compare(property, type, prop) {
                return function (obj1, obj2) {
                    if (type === "ascending") {
                        return new Date(obj1[property]).getTime() - new Date(obj2[property]).getTime();
                    } else {
                        return new Date(obj2[property]).getTime() - new Date(obj1[property]).getTime();
                    }
                };
            },
            selectClose() {
                this.centerDialogVisible = false
                this.isOrderSu = true
                this.centerDialogVisibleTitle = "";
                this.searchPhone()
            },
            get80031() {
                if (!this.formList.name) return
                this.loading4 = true;
                var data = {
                    "svc": "80031",
                    "para": {
                        "phonenumber": this.formList.name,
                        "start": this.formList.date.length ? this.formList.date[0] : '',
                        "end": this.formList.date.length ? this.formList.date[1] : ''
                    },
                    pro: 'over'
                }
                yq.remoteCall('/cx-mix-api/servlet/ayyBusiServlet?action=busiData', data, res => {
                    this.loading4 = false;
                    if (res.res.st != 0) {
                        this.$message.error(res.res.msg)
                        return;
                    }
                    this.videoRingRecord = res.inf.list
                }).then(() => {

                })
            },
            get90010() {
                this.loading3 = true;
                var data = {
                    svc: "90010",
                    para: {
                        phonenumber: this.formList.name,
                        startData: this.formList.date.length ? this.formList.date[0] : '',
                        endData: this.formList.date.length ? this.formList.date[1] : '',
                    },
                };
                yq.remoteCall(
                    "/cx-mix-api/servlet/ayyBusiServlet?action=busiData",
                    data,
                    (res) => {
                        if (res.res.st != 0) {
                            this.$message.error(res.res.msg);
                            return;
                        }
                        let list = res.inf.list || []
                        // 统一字段
                        list.forEach((item) => {
                            item.phoneNumber = item.phonenumber
                            item.productId = item['bill_code']
                            item.productName = item.productname
                            item.opResult = item.returndesc
                            item.expenses = item.price > 50 ? item.price / 100 : item.price || 0
                            item.orderTime = item.recordtime
                            item.channel = item.name
                            item.chargeNum = 0
                            item.refundedNum = 0
                            item.refundNum = ""
                            item.refundMultiple = 1
                            item.claimant = ""
                            item.refundBills = ""
                            item.orderChannel = item.channel || ""
                            item.refundTime = item.ununTime || ""
                            this.orderRecordData.push(item)
                            this.orderRecordDataCopy.push(item)

                        })
                    }
                ).then(() => {
                })
            },
            get80015() {
                if (!this.formList.name) return
                this.loading3 = true;
                var data = {
                    svc: "9099",
                    para: {
                        sign: 1,
                        start: this.formList.date.length ? this.formList.date[0] : '',
                        end: this.formList.date.length ? this.formList.date[1] : '',
                        phonenumber: this.formList.name,
                        pro: "two"
                    },
                };
                yq.remoteCall(
                    "/cx-mix-api/servlet/ayyBusiServlet?action=busiData",
                    data,
                    (res) => {
                        this.orderRecordData = []
                        this.orderRecordDataCopy = []
                        if (res.res.st != 0) {
                            this.$message.error(res.res.msg);
                            return;
                        }
                        var l = res.inf.list;
                        for (var i = 0; i < l.length; i++) {
                            var objectT = res.inf.list[i];
                            objectT.expenses = objectT.price > 50 ? objectT.price / 100 : objectT.price
                            objectT.chargeNum = 0
                            objectT.productCode = objectT.otherid
                            objectT.refundedNum = 0
                            objectT.refundNum = ""
                            objectT.refundMultiple = 1
                            objectT.claimant = ""
                            objectT.refundBills = ""
                            objectT.orderChannel = objectT.channel || ""
                            objectT.refundTime = objectT.ununTime || ""
                            if (objectT.returnCode == 0) {
                                this.orderRecordData.push(objectT);
                                this.orderRecordDataCopy.push(objectT);

                            }
                        }
                        this.loading3 = false;

                    }
                ).then(() => {
                });
            },
            get34317() {
                if (!this.formList.name) return;
                this.flatOne = 0;
                var data = {
                    svc: "34317",
                    para: {
                        phonenumber: this.formList.name || "",
                    },
                };
                this.ringtone = [];
                this.ISMP = [];
                this.videoRing = [];
                this.loading1 = true;
                yq.remoteCall(
                    "/cx-mix-api/servlet/ayyBusiServlet?action=busiData",
                    data,
                    (res) => {
                        this.loading1 = false;
                        if (res.codeIsmp != 0) {
                            // this.$message.error(res.msgIsmp);
                            // return;
                        } else {
                            this.ISMP = res.ismp;
                            this.ISMP.forEach(function (obj) {
                                obj.delTyle = 'ismpSelectList'
                            })
                        }
                        if (res.codeRing != 0) {
                            this.emptyText1 = res.msgRing
                            // this.$message.error(res.msgRing);
                            // return;
                        } else {

                            if ((typeof res.ring != 'undefined')) {
                                this.ringtone = res.ring;
                                this.ringtone.forEach(function (obj) {
                                    obj.productName = obj.ringName
                                    obj.productid = obj.ringId
                                    obj.delTyle = 'ringtoneSelectList'
                                    if (obj.ringId.toString().startsWith("81009999")) {
                                        obj.isBox = "音乐盒";
                                    } else {
                                        obj.isBox = "铃音";
                                    }
                                });
                            }
                        }
                        if (res.codeVring != 0) {
                            this.emptyText3 = res.msgVring
                            // this.$message.error(res.msgVring);
                            // return;
                        } else {
                            if ((typeof res.vring != 'undefined')) {
                                this.videoRing = res.vring;
                                this.videoRing.forEach(function (obj) {
                                    obj.productid = obj.productId
                                    obj.delTyle = 'videoRingSelectList'
                                    if (obj.type == 0) {
                                        obj.isBox = "视频彩铃";
                                    } else {
                                        obj.isBox = "视频音乐盒";
                                    }
                                });
                            }
                        }
                        this.get80001()
                        // if (this.ISMP.length == 0 || this.ISMP == []) return;
                        // var feeProductldes = this.ISMP.map((item) => {
                        //     return item.feeProductId;
                        // });

                        // feeProductldes.map(Number)
                        // if (this.ringtone.length != 0 || this.ringtone != []) {
                        //     var ringIdes = this.ringtone.map((item) => {
                        //         return item.ringId;
                        //     });
                        //     ringIdes.map(String)
                        //     for (var i = 0; i < ringIdes.length; i++) {
                        //         if (feeProductldes.indexOf(String(ringIdes[i])) != -1) {
                        //             this.ISMP = this.ISMP.filter((val) => val.feeProductId != String(ringIdes[i]));
                        //         }
                        //     }
                        // }

                        // if (this.videoRing.length != 0 || this.videoRing != []) {
                        //     var videoRing = this.videoRing.map((item) => {
                        //         return item.ringId;
                        //     });
                        //     videoRing.map(Number)

                        //     for (var i = 0; i < videoRing.length; i++) {
                        //         if (feeProductldes.indexOf(String(videoRing[i])) != -1) {
                        //             this.ISMP = this.ISMP.filter((val) => val.feeProductId != String(videoRing[i]));
                        //         }
                        //     }

                        // }
                        let list = [...this.ringtone, ...this.ISMP, ...this.videoRing].filter((item) => {
                            return item.state !== '已退订'
                        })
                        this.totalList = JSON.parse(JSON.stringify(list)) || []

                        this.totalList.forEach((item) => {
                            item.packageType = item.packageType || item.isBox
                            item.productId = item.productid || item.ringid
                            item.productid = item.productid || item.ringid
                            item.productCode = item.feeProductId || ""
                            item.expenses = item.price > 50 ? item.price / 100 : item.price || 0
                            item.chargeNum = 0
                            item.refundedNum = 0
                            item.refundNum = ""
                            item.refundMultiple = 1
                            item.claimant = ""
                            item.refundBills = ""
                        })
                    }
                ).then(() => {

                });
            },
            async delUser() {
                await this.isAuth()
                if (this.auth == 0) return;
                // 判断是否存在视频彩铃产品
                // 如果存在产品就进入退订界面
                if (
                    this.formList.name == "" ||
                    this.formList.name == undefined ||
                    this.formList.name == null
                ) {
                    this.$alert("手机号为空", "", {
                        confirmButtonText: "确定",
                        callback: (action) => {
                            this.$message({
                                type: "info",
                                message: "已取消",
                            });
                        },
                    });
                    return;
                }

                // 销户
                this.cancelUser();
            },
            cancelUser() {
                this.$confirm("请确认是否继续进行功能销户", "功能销户", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    dangerouslyUseHTMLString: true,
                    message: `<div>请确认是否为<span style="font-size: 20px;font-weight: bold;color: #e5224b">${this.formList.name}</span>号码退订视频彩铃功能吗</div>`,
                    type: "warning",
                }).then(() => {
                    if (!this.formList.name) return;
                    // 退订
                    var data = {
                        svc: "34317",
                        para: {
                            phonenumber: this.formList.name || "",
                        },
                    };
                    if (!this.formList.name) return

                    yq.remoteCall(
                        "/cx-mix-api/servlet/ayyBusiServlet?action=busiData",
                        data,
                        (res) => {
                            if (res.codeRing != 0) {
                                return;
                            }
                            res.ring == undefined
                                ? (this.ringtone2 = [])
                                : (this.ringtone2 = res.ring);
                            // this.ringtone2 = res.inf.ring.filter(function (obj) {
                            //   // return obj.state == "未退订";
                            //   // return obj;
                            // });
                        }
                    ).then(() => {
                    });
                    // 静态测试数据
                    // this.ringtone2 = [
                    //   { ringId: "12121", ringName: "dadsa" },
                    //   { ringId: "12121", ringName: "dadsa" },
                    //   { ringId: "12121", ringName: "dadsa" },
                    // ];

                    if (this.ringtone2.length != 0) {
                        console.log(this.ringtone2.length);
                        this.vringTitleContent = "是否一同取消音频彩铃产品";
                        this.ringTitle = "铃音";
                        this.isVringTitleContent = false;
                        this.isRingTitleContent = true;
                        this.centerDialogVisible2 = true;
                    } else {
                        console.log("111");
                        this.cancelUserRequest();
                    }
                });
            },
            cancelUserRequest() {
                var data = {
                    svc: "80119",
                    para: {
                        phonenumber: this.formList.name || "",
                    },
                };
                if (!this.formList.name) return

                yq.remoteCall(
                    "/cx-mix-api/servlet/ayyBusiServlet?action=busiData",
                    data,
                    (res) => {
                        if (res.res.st != 0) {
                            this.$message.error(res.res.msg);
                            return;
                        }
                        this.$message({
                            type: "success",
                            message: "发起请求成功！视频彩铃功能取消需要2分钟，请稍后查询结果",
                        });
                    }
                ).then(() => {
                });
            },
            UnsubRingtone() {
                if (!this.formList.name) return

                var data = {
                    svc: "80120",
                    para: {
                        phonenumber: this.formList.name || "",
                    },
                };
                yq.remoteCall(
                    "/cx-mix-api/servlet/ayyBusiServlet?action=busiData",
                    data,
                    (res) => {
                        if (res.res.st != 0) {
                            this.$message.error(res.res.msg);
                            return;
                        }
                        this.$message({
                            type: "success",
                            message: "操作成功!",
                        });
                    }
                ).then(() => {
                });
            },
            async unOrder() {
                if (this.ringtoneSelectList.length == 0 && this.ismpSelectList.length == 0 && this.videoRingSelectList.length == 0) {
                    this.$message.error("请选择产品");
                    return;
                }
                if (this.unableunRecordFlat == true) {
                    this.$message.error("用户末开通或已取消视频彩铃功能");
                    return;
                }
                this.isAllSuccess = true;
                await this.isAuth()
                if (this.auth == 0) return;
                this.isSowdialog();
                this.centerDialogVisibleTitle = "退订";
                this.centerDialogVisible = true;

                // TODO 接口未确定
                // for (var i = 0; i < this.busiRingListSelectList.length; i++) {
                // }
                // console.log(this.ringtoneSelectList)
                // console.log(this.ismpSelectList)
                // console.log(this.videoRingSelectList)
                // console.log(this.busiRingListSelectList)
            },
            tableRowClassName({row, rowIndex}) {
                if (row.defaultRing == 1) {
                    return 'success-row';
                }
                return '';
            },
            SureDelUser() {
                for (var i = 0; i < this.ringtoneSelectList2.length; i++) {
                    var data = {
                        svc: "80104",
                        para: {
                            phonenumber: this.formList.name || "",
                            productid: this.ringtoneSelectList2[i].ringId,
                            // phonenumber: 16626625693,
                            // productid: 11,
                        },
                    };
                    if (!this.formList.name) return

                    yq.remoteCall(
                        "/cx-mix-api/servlet/ayyBusiServlet?action=busiData",
                        data,
                        (res) => {
                            if (res.res.st != 0) {
                                this.$message.error(res.res.msg);
                                return;
                            }
                            this.$message.success(res.res.msg);
                        }
                    ).then(() => {
                    });
                }
                this.cancelUserRequest();
                this.centerDialogVisible2 = false;
            },
            closeDelDialog() {
                this.cancelUserRequest();
                this.centerDialogVisible2 = false;
            },
            unOrderSure() {
                if (this.centerDialogVisibleTitle == "退订") {
                    this.productResultList = []
                    var data = {
                        svc: "90024",
                        para: {
                            phonenumber: this.formList.name || "",
                            ringtoneSelectList: this.ringtoneSelectList,
                            ismpSelectList: this.ismpSelectList,
                            videoRingSelectList: this.videoRingSelectList,
                        },
                    };
                    if (!this.formList.name) return
                    yq.remoteCall(
                        "/cx-mix-api/servlet/ayyBusiServlet?action=busiData",
                        data,
                        (res) => {
                            if (res.res.st != 0) {
                                this.$message.error(res.res.msg);
                                return;
                            }
                            this.ringtoneSelectList = []
                            this.ismpSelectList = []
                            this.videoRingSelectList = []
                            this.totalSelectList = res.productList || []
                            this.totalSelectList.forEach((item) => {
                                if (item.unOrderState != 0) {
                                    this.isAllSuccess = false;
                                }
                            })
                            if (this.isAllSuccess) {
                                this.$message.success("退订成功");
                            } else {
                                this.$message.error("退订失败");
                            }
                            this.isOrderSu = false

                        }
                    ).then(() => {
                    });
                } else if (this.centerDialogVisibleTitle == "设置默认彩铃") {
                    for (var i = 0; i < this.ringtoneSelectList.length; i++) {
                        var data = {
                            svc: "80106",
                            para: {
                                phonenumber: this.formList.name || "",
                                productid: this.ringtoneSelectList[i].ringId,
                                // phonenumber: 16626625693,
                                // productid: 11,
                            },
                        };
                        if (!this.formList.name) return

                        yq.remoteCall(
                            "/cx-mix-api/servlet/ayyBusiServlet?action=busiData",
                            data,
                            (res) => {
                                if (res.res.st != 0) {
                                    this.$message.error(res.res.msg);
                                    return;
                                }
                                this.$message.success(res.res.msg);
                            }
                        ).then(() => {
                        });
                    }

                    var data = {
                        svc: "90028",
                        para: {
                            phonenumber: this.formList.name || "",
                            productids: this.videoRingSelectList.length > 0 ? this.videoRingSelectList.map(item => item.ringId).join(',') : "",
                            // phonenumber: 16626625693,
                            // productid: 11,
                        },
                    };
                    if (!this.formList.name) return
                    yq.remoteCall(
                        "/cx-mix-api/servlet/ayyBusiServlet?action=busiData",
                        data,
                        (res) => {
                            if (res.res.st != 0) {
                                this.$message.error(res.res.msg);
                                return;
                            }
                            this.$message.success(res.res.msg);
                            this.searchPhone()
                        }
                    ).then(() => {
                    });
                    // for (var i = 0; i < this.videoRingSelectList.length; i++) {
                    //   var data = {
                    //     svc: "80116",
                    //     para: {
                    //       phonenumber: this.formList.name||"",
                    //       productid: this.videoRingSelectList[i].ringId,
                    //       // phonenumber: 16626625693,
                    //       // productid: 11,
                    //     },
                    //   };
                    //   if(!this.formList.name) return
                    //
                    //   yq.remoteCall(
                    //           "/cx-mix-api/servlet/ayyBusiServlet?action=busiData",
                    //           data,
                    //           (res) => {
                    //             if (res.res.st != 0) {
                    //               this.$message.error(res.res.msg);
                    //               return;
                    //             }
                    //             this.$message.success(res.res.msg);
                    //           }
                    //   ).then(() => {});
                    // }
                    this.centerDialogVisibleTitle = "";
                    this.centerDialogVisible = false;
                } else if (this.centerDialogVisibleTitle == "设置播放模式") {
                    for (var i = 0; i < this.ringtoneSelectList.length; i++) {
                        var data = {
                            svc: "80111",
                            para: {
                                phonenumber: this.formList.name || "",
                                playmode: this.payMode,
                                // phonenumber: 16626625693,
                                // productid: 11,
                            },
                        };
                        if (!this.formList.name) return

                        yq.remoteCall(
                            "/cx-mix-api/servlet/ayyBusiServlet?action=busiData",
                            data,
                            (res) => {
                                if (res.res.st != 0) {
                                    this.$message.error(res.res.msg);
                                    return;
                                }
                                this.$message.success(res.res.msg);
                            }
                        ).then(() => {
                        });
                    }

                    for (var i = 0; i < this.videoRingSelectList.length; i++) {
                        var data = {
                            svc: "80117",
                            para: {
                                phonenumber: this.formList.name || "",
                                playmode: this.payMode,
                                // phonenumber: 16626625693,
                                // productid: 11,
                            },
                        };
                        if (!this.formList.name) return

                        yq.remoteCall(
                            "/cx-mix-api/servlet/ayyBusiServlet?action=busiData",
                            data,
                            (res) => {
                                if (res.res.st != 0) {
                                    this.$message.error(res.res.msg);
                                    return;
                                }
                                this.$message.success(res.res.msg);

                            }
                        ).then(() => {
                        });
                    }

                    this.centerDialogVisibleTitle = "";
                    this.centerDialogVisible = false;
                }
            },
            get80023() {
                if (!this.formList.name) return;
                this.flatOne;
                var data = {
                    svc: "80023",
                    para: {
                        phonenumber: this.formList.name || "",
                    },
                };
                this.loading2 = true;
                yq.remoteCall(
                    "/cx-mix-api/servlet/ayyBusiServlet?action=busiData",
                    data,
                    (res) => {
                        this.loading2 = false;
                        if (res.res.st != 0) {
                            // this.$message.error(res.res.msg);
                            // 用户属性
                            var data = {
                                svc: "80001",
                                para: {
                                    phonenumber: this.formList.name || "",
                                },
                            };
                            if (!this.formList.name) return

                            yq.remoteCall(
                                "/cx-mix-api/servlet/ayyBusiServlet?action=busiData",
                                data,
                                (res) => {
                                    if (res.res.st != 0) {
                                        this.$message.error(res.res.msg);
                                        return;
                                    }
                                    var resList = res.inf.list;
                                    var usertypes = resList.map((item) => {
                                        return item.usertype;
                                    });
                                    if (
                                        usertypes.includes("4") ||
                                        usertypes.includes("6") ||
                                        usertypes.includes("2") ||
                                        usertypes.includes("1")
                                    ) {
                                        this.userClass = this.userDist[1];
                                    } else {
                                        this.userClass = this.userDist[0];
                                    }
                                }
                            ).then(() => {
                            });
                            return;
                        }
                        if (res.inf.data != undefined) this.busiRingList = res.inf.data;
                        this.busiRingList.forEach((item)=>{
                            item.productId = item.orderProductId||''
                            item.productName = item.orderProductName||''
                            item.orderTime = item.orderProductTime||''
                            item.expenses = item.auserMoney > 50 ? item.auserMoney / 100 : item.auserMoney || 0
                            item.channelName = item.orderProductChannelId||''
                            item.productCode = item.orderProductId||''
                            item.refundTime = '无'
                            // item.productDept = '无'
                            item.chargeNum = 0
                            item.refundedNum = 0
                            item.refundMultiple = 1
                        })

                        var resList = res.inf.data;
                        var isVideoAuseres = resList.map((item) => {
                            return item.isVideoAuser;
                        });
                        if (isVideoAuseres.includes(1)) {
                            this.userClass = this.userDist[3];
                        } else {
                            this.userClass = this.userDist[2];
                        }
                    }
                ).then(() => {
                });
            },
            get80001() {
                var data = {
                    svc: "80001",
                    para: {
                        phonenumber: this.formList.name || "",
                    },
                };
                if (!this.formList.name) return

                yq.remoteCall(
                    "/cx-mix-api/servlet/ayyBusiServlet?action=busiData",
                    data,
                    (ress) => {
                        console.log(ress)
                        if (ress.res.st != 0) {
                            this.$message.error(ress.res.msg);
                            return;
                        }
                        var resList = ress.inf.list;
                        var usertypes = resList.map((item) => {
                            return item.usertype;
                        });
                        if (usertypes.includes("4")) {
                            this.funcClass = this.funcDist[2];
                            this.handleISMPRing("8") // 只有视频 就是没有音频
                        } else if (usertypes.includes("6")) {
                            this.funcClass = this.funcDist[3];
                        } else if (usertypes.includes("2") || usertypes.includes("1")) {
                            this.funcClass = this.funcDist[1];
                            this.handleISMPRing("9") // 只有音频 就是没有视频
                        } else if (!usertypes.includes("1") && !usertypes.includes("2") && !usertypes.includes("6")) {
                            // 没有音频,就修改音频的字段
                            this.handleISMPRing("8")
                        } else if (!usertypes.includes("4") && !usertypes.includes("6")) {
                            // 没有视频，就修改视频的字段
                            this.handleISMPRing("9")
                        } else {
                            this.funcClass = this.funcDist[0]; // 无功能
                        }
                        if (
                            !usertypes.includes("1") &&
                            !usertypes.includes("2") &&
                            !usertypes.includes("4") &&
                            !usertypes.includes("6")
                        ) {

                            this.handleISMPRing("8")
                            this.handleISMPRing("9")
                            this.unableunRecordFlat = false
                            if (this.ringtone.length != 0) {
                                this.unableunRecordFlat = true
                                this.ringTitle = "铃音 备注：（功能已销户，产品默认保留35天，未重新开户则不计费）";
                            } else {
                                this.ringTitle = "铃音";
                            }
                            if (this.videoRing.length != 0) {
                                this.unableunRecordFlat = true
                                this.vringTitle = "视频彩铃 备注：（功能已销户，产品默认保留35天，未重新开户则不计费）";
                            } else {
                                this.vringTitle = "视频彩铃";
                            }
                        } else {
                            this.ringTitle = "铃音";
                            this.vringTitle = "视频彩铃";
                        }
                    }
                ).then(() => {
                });
            },
            // 处理ISMP ISMP里面集团计费编码9(视频)或8（音频）开头，“计费成功”替换为“不计费“未退订”替换为"已退订
            handleISMPRing(n) {
                if (this.ISMP.length != 0) {
                    for (let i = 0; i < this.ISMP.length; i++) {
                        let object = this.ISMP[i]
                        let fd = object.feeProductId;
                        if (fd.toString().startsWith(n)) {
                            if (this.ISMP[i].fee_status == "计费成功") {
                                this.ISMP[i].fee_status = "不计费"
                            }
                            if (this.ISMP[i].state == "未退订") {
                                this.ISMP[i].state = "已退订"
                            }
                        }
                    }
                }
            },
            getDict: function () {
                var _this = this;
                yq.daoCall(
                    {
                        controls: ["common.getDict(IM_ORDER_CLAIMANT)"],
                        params: {},
                    },
                    function (data) {
                        _this.IM_ORDER_CLAIMANT =
                            data["common.getDict(IM_ORDER_CLAIMANT)"].data;
                    },
                    {contextPath: "cx-mix-imusic"}
                );
            },
            isSowdialog() {
                if (this.ringtoneSelectList.length != 0) {
                    this.isShowRingtone = true;
                } else {
                    this.isShowRingtone = false;
                }
                if (this.videoRingSelectList.length != 0) {
                    this.isShowVring = true;
                } else {
                    this.isShowVring = false;
                }
                if (this.ismpSelectList.length != 0) {
                    this.isShowISMP = true;
                } else {
                    this.isShowISMP = false;
                }
                if (this.busiRingListSelectList.length != 0) {
                    this.isShowBusiRing = true;
                } else {
                    this.isShowBusiRing = false;
                }
            },
            toUnsub(row) {
                let msg = `是否退订 号码：${this.formList.name} 产品id：${row.productid || row.feeProductId} 产品名称：${row.productName} 该信息 `
                this.$confirm(msg, "提示", {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    dangerouslyUseHTMLString: true,
                    message: `<div>是否退订 号码：<span style="font-size: 20px;font-weight: bold;color: #e5224b">${this.formList.name}</span> 产品id：<span style="font-size: 20px;font-weight: bold;color: #e5224b">${row.productid || row.feeProductId}</span> 产品名称：<span style="font-size: 20px;font-weight: bold;color: #e5224b">${row.productName}</span> 该信息</div>`,
                    type: 'warning'
                }).then(() => {
                    if (row.delTyle == 'videoRingSelectList') {
                        if (row.type == 0) {
                            this.singalDel(row, '80113')
                        } else {
                            this.singalDel(row, '80115')
                        }
                    } else if (row.delTyle == 'ringtoneSelectList') {
                        this.singalDel(row, '80104')
                    } else if (row.delTyle == 'ismpSelectList') {
                        this.singalDel(row, '80108')
                    }
                }).catch(() => {
                });
            },
            singalDel(row, svc) {
                var data = {
                    svc: svc,
                    para: {
                        phonenumber: this.formList.name || "",
                        productid: row.productid || "",
                    },
                };
                yq.remoteCall(
                    "/cx-mix-api/servlet/ayyBusiServlet?action=busiData",
                    data,
                    (res) => {
                        if (res.res.st != 0) {
                            this.$message.error(res.res.msg);
                            return;
                        }

                        this.$confirm(res.res.st == 0 ? "退订成功" : "退订失败", '提示', {
                            confirmButtonText: '确定',
                            type: res.res.st == 0 ? 'success' : 'error'
                        }).then(() => {

                        })
                        this.get34317();
                        this.get80023();
                        // this.$message({
                        //     type: 'success',
                        //     message: '退订成功'
                        // });
                    }
                ).then(() => {
                });
            },
            async settingDefaultRing() {
                await this.isAuth()
                if (this.auth == 0) return;
                this.isSowdialog();
                this.centerDialogVisibleTitle = "设置默认彩铃";
                this.centerDialogVisible = true;
            },
            async settingPayMode() {
                await this.isAuth()
                if (this.auth == 0) return;
                this.isSowdialog();
                this.centerDialogVisibleTitle = "设置播放模式";
                this.centerDialogVisible = true;
            },

            async isAuth() {
                var _this = this
                var data = {
                    svc: "80117",
                    para: {
                        phonenumber: this.formList.name || "",
                    },
                };
                await yq.remoteCall(
                    "/cx-mix-api/servlet/ayyBusiAuthServlet?action=busiAuthData",
                    data,
                    (res) => {
                        if (res.res.st == 999) {
                            _this.$message.error(res.res.msg);
                            _this.auth = 0
                        } else {
                            _this.auth = 1
                        }
                    }
                ).then(() => {
                });
            },

            async searchPhone() {
                if (this.formList.name == "") {
                    this.$message.error("手机号码为空");
                    return;
                }
                if (new Date(this.formList.date[1]).getTime() < new Date(this.formList.date[0]).getTime()) {
                    this.$message.error("开始时间不能大于结束时间")
                    return
                }
                localStorage.setItem("phoneName", this.formList.name || '');
                (this.ringtone = []);
                (this.ringtone2 = []);
                (this.ISMP = []);
                (this.videoRing = []);
                (this.videoRing2 = []);
                (this.busiRingList = []);
                (this.ringtoneSelectList = []);
                (this.ringtoneSelectList2 = []);
                (this.ismpSelectList = []);
                (this.videoRingSelectList = []);
                (this.videoRingSelectList2 = []);
                (this.busiRingListSelectList = []);
                this.orderRecordData = []
                this.totalSelectList = []
                this.orderRecordDataCopy = []
                this.totalList = []
                this.funcClass = "无功能"
                this.userClass = "无"
                await this.isAuth()
                if (this.auth == 1) {
                    this.get34317();
                    this.get80023();
                    this.get80031()
                    this.get80015()
                } else {
                    this.funcClass = "无功能"
                    this.userClass = "无"
                }
            },
            totalChange(val) {
                let list = val || []
                this.totalSelectList = list || []
                this.ismpSelectList = []
                this.ringtoneSelectList = []
                this.videoRingSelectList = []
                list.forEach((item) => {
                    if (item.delTyle == 'ismpSelectList') {
                        this.ismpSelectList.push(item)
                    }
                    if (item.delTyle == 'ringtoneSelectList') {
                        this.ringtoneSelectList.push(item)
                    }
                    if (item.delTyle == 'videoRingSelectList') {
                        this.videoRingSelectList.push(item)
                    }
                })
            },

            ringtoneChange(val) {
                this.ringtoneSelectList = val;
                this.iframeSelList1 = val || [];
            },
            ismpChange(val) {
                this.ismpSelectList = val;
                this.iframeSelList1 = val || [];
            },
            videoRingChange(val) {
                this.videoRingSelectList = val;
                this.iframeSelList1 = val || [];
            },
            busiRingListChange(val) {
                this.busiRingListSelectList = val;
                this.iframeSelList1 = val || [];
            },
            orderRecordDataChange(val) {
                this.orderRecordDataSelectList = val;
            },
            ringtoneChange2(val) {
                this.ringtoneSelectList2 = val;
            },
            videoRingChange2(val) {
                this.videoRingSelectList2 = val;
            },
            SengOrder() {
                var productIdss = "";
                let productList = []
                if (this.ringtoneSelectList.length != 0) {
                    var RringId = this.ringtoneSelectList.map((item) => {
                        if (item.ringId != "") return item.ringId;
                    });
                    productIdss = RringId.join();
                    productList = productList.concat(this.ringtoneSelectList)
                }
                if (this.orderRecordDataSelectList.length != 0) {
                    var productId = this.orderRecordDataSelectList.map((item) => {
                        if (item.productId != "") return item.productId;
                    });
                    productIdss = productId.join();
                    productList = productList.concat(this.orderRecordDataSelectList)
                }
                if (this.ismpSelectList.length != 0) {
                    var SringId = this.ismpSelectList.map((item) => {
                        if (item.productid != "") return item.productid;
                    });
                    if (productIdss != "") productIdss = productIdss + ",";
                    productIdss = productIdss + SringId.join();
                    productList = productList.concat(this.ismpSelectList)
                }
                //商彩
                if (this.busiRingListSelectList.length != 0) {
                    var SringId = this.busiRingListSelectList.map((item) => {
                        if (item.productId) return item.productId;
                    });
                    if (productIdss != "") productIdss = productIdss + ",";
                    productIdss = productIdss + SringId.join();
                    productList = productList.concat(this.busiRingListSelectList)
                }
                if (this.videoRingSelectList.length != 0) {
                    var VringId = this.videoRingSelectList.map((item) => {
                        if (item.ringId != "") return item.ringId;
                    });
                    if (productIdss != "") productIdss = productIdss + ",";
                    productIdss = productIdss + VringId.join();
                    productList = productList.concat(this.videoRingSelectList)
                }
                if (productIdss == "") {
                    this.$message.error("选择产品为空");
                    return;
                }
                productList.forEach(item => {
                  item.price = item.expenses || ''
                })
                const data = {event: 'editRecode', parameter: productIdss};
                window.parent.postMessage(data, '*'); // '*' 表示接收所有来源的消息，可以根据需要设置安全来源。
                // top.openTab({url: "/cc-eorder/pages/eorder/search/order-index.jsp?type=add&flowKey=normal_flow&productIds=" +
                //             productIdss +
                //             "&type=01",
                // 			title: '派单', id : ''});
                let prefix = parseInt(Math.random() * 100000)
                sessionStorage.setItem(prefix + this.formList.name, JSON.stringify(productList))
                var url1 = "/cc-eorder/pages/eorder/search/order-index.jsp?type=add&flowKey=normal_flow&productIds=" +
                    productIdss +
                    "&type=01&custPhone=" + this.formList.name + "&prefix=" + prefix
                top.popup.openTab({url: url1, title: '派单'});
            },
            toAuth() {
                top.popup.layerShow({
                    url: '/cx-mix-imusic/pages/order/comp/vaifySMS.html',
                    title: '短信校验',
                    data: {phone: this.formList.name},
                    area: ['30%', '25%'],
                    type: 2,
                })
            },
            skipBlack() {
                top.popup.openTab({
                    url: "/cx-mix-imusic/pages/black/black.html?phone=" + this.formList.name,
                    title: '黑名单'
                });
            },
            skipWhite(){
                top.popup.openTab({url: "/cx-mix-imusic/pages/black/white.html?phone=" + this.formList.name, title: '白名单'});
            },
            handleStorageChange(event) {
                if (event.key === 'phoneName') {
                    // 当localStorage的myData键值发生变化时，更新组件的数据
                    this.formList.name = event.newValue || '';
                    // this.searchPhone()
                }
                if (event.key === "oneIndex") {
                    if (event.newValue == 0 && this.oldphone != this.formList.name) {
                        this.oldphone = this.formList.name
                        this.searchPhone()
                    }
                }
            },
        },
        beforeDestroy() {
            // 在组件销毁之前移除storage事件监听器
            window.removeEventListener('storage', this.handleStorageChange);
        },
        mounted() {
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, "0");
            const day = String(today.getDate()).padStart(2, "0");
            const older = year - 4
            this.formList.date = [
                `${older}-${month}-${day}`,
                `${year}-${month}-${day}`,
            ];
            // 添加storage事件监听器
            window.addEventListener('storage', this.handleStorageChange);
            this.formList.name = localStorage.getItem("phoneName") != undefined ? localStorage.getItem("phoneName") : "";

            window.parent.isIframe && window.parent.isIframe == "01";
            if (this.isIframe) {
                this.getDict();
                this.formList.name = window.parent.phonenumber || "";
                this.productIds = window.parent.productIds || [];
                this.searchPhone();
            }
            if (this.formList.name) {
                this.searchPhone()
            }
            //方便父页面调取
            parent.iframeSelList1 = this.iframeSelList1 || [];
        },
    });
</script>
</body>
</html>
