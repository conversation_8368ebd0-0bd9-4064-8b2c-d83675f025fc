<!--折线图组件-->
<template>
  <div id="BaseLineChart" :ref="name" :style="{ width: chartWidth, height: chartHeight }" />
</template>

<script>
module.exports = {
  name: "BaseLineChart",
  props: {
    name: {
      type: String,
      default: "BaseLineChart",
    },
    tooltipFormatter: {
      type: Function,
      default: undefined,
    },
    // 显示拐点
    showSymbol: {
      type: Boolean,
      default: false,
    },
    symbolList: {
      type: Array,
      default: () => [],
    },
    symbol: {
      type: String,
      default: "emptyCircle",
    },
    // 显示图例
    showLegend: {
      type: Boolean,
      default: true,
    },
    // 图例位置
    legendPos: {
      type: Object,
      default: () => {
        return {

        };
      },
    },
    // 图表位置
    grid: {
      type: Object,
      default: () => {
        return {
          left: 30,
          bottom: 30,
          top: 70,
          right: 30,
        };
      },
    },
    // 图表堆叠面积
    areaStyle: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // x轴标签宽度 ， 可设置宽度使标签换行（break）
    xAaxisLabelWidth: {
      type: Number,
      default: 0,
    },
    // 展示y轴轴线
    showyAaxisLine: {
      type: Boolean,
      default: true,
    },
    // 展示x轴轴线
    showxAaxisLine: {
      type: Boolean,
      default: true,
    },
    // 展示y轴标签
    showyAaxisLabel: {
      type: Boolean,
      default: true,
    },
    // 展示x轴标签
    showxAaxisLabel: {
      type: Boolean,
      default: true,
    },
    // 多坐标轴
    appendYAxis: {
      type: Object,
      default: () => {},
    },
    // 数据，多条数据格式 [ [1,2,3],[4,5,6] ]
    chartData: {
      type: Array,
      default: () => [],
    },
    // X轴数据，一般为时间数组 [‘1-1’,‘1-2’,‘1-3’ ]；
    timeX: {
      type: Array,
      default: () => [],
    },
    // 图表宽度
    chartWidth: {
      type: String,
      default: "100%",
    },
    // 图表高度
    chartHeight: {
      type: String,
      default: "320px",
    },
    // 每条折线对应名称
    seriesName: {
      type: Array,
      default: () => [],
    },
    // tooltip显示单位
    showTooltipUnit: {
      type: Boolean,
      default: true,
    },
    // 图表数据的显示单位
    chartUnit: {
      type: String,
      default: "数量",
    },
    tooltipUnit: {
      type: String,
      default: "",
    },
    xAxis: {
      type: Array,
      default: null,
    },
    yAxis: {
      type: Array,
      default: null,
    },
    // 自定义颜色
    color: {
      type: Array,
      default: () => ["#04cbd8", "#5CC6F5", "#7B879A"],
    },
    // x轴间隔数值
    intervalY: {
      type: Number,
      default: 0,
    },
    // x轴间隔数量
    intervalX: {
      type: Number,
      default: 0,
    },
    // y轴最小数值
    minY: {
      type: Number,
      default: 0,
    },
    // y轴最大数值
    maxY: {
      type: Number,
      default: 0,
    },
    // 追加series
    appendSeries: {
      type: Object | Array,
      default: null,
    },
    toolTipUnit: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      baseLineChart: null,
    };
  },
  computed: {
    chartOptions() {
      let chartUnit = this.showTooltipUnit ? this.toolTipUnit || this.chartUnit : "";
      const options = {
        grid: {
          left: "30",
          bottom: "60",
          top: "30",
          right: "0",
          ...this.grid,
        },
        tooltip: {
          show: true,
          trigger: "axis",
          className: "line-tooltip",
          showDelay: 0, // 显示延迟，添加显示延迟可以避免频繁切换，单位ms
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          padding: 16,
          textStyle: {
            color: "#333333",
          },
          backgroundColor: "#ffffff",
          formatter:
            this.tooltipFormatter ||
            ((params) => {
              let relVal = `<span class="tooltip-title">${params[0].name}</span>`;
              for (let i = 0; i < params.length; i++) {
                if (params[i].data.unit) {
                  chartUnit = params[i].data.unit;
                }
                if (params[i].value !== "-" && params[i].value != 0) {
                  relVal +=
                    `<br/>` +
                    params[i].marker +
                    params[i].seriesName +
                    `<span style="margin-left: 10px; float: right">${params[i].value}${this.tooltipUnit}</span>`;
                }
              }
              return relVal;
            }),
        },
        color: this.color,
        legend: {
          show: true,
          top: 10,
          left: 0,
          itemWidth: 14,
          itemHeight: 14,
          itemGap: 24,
          icon:"rect",
          // 虚线
          // icon: "path://M6 5L8 5L8 6L6 6ZM9 5L11 5L11 6L9 6ZM12 5L14 5L14 6L12 6Z",
          // 实线
          // icon: "path://M6 4L30 4L30 8L6 8",
          ...this.legendPos
        },
        xAxis: this.xAxis
          ? this.xAxis
          : [
              {
                type: "category",
                axisTick: { show: false },
                data: [],
                axisLine: {
                  show: this.showxAaxisLine,
                  lineStyle: {
                    color: "#CCCCCC",
                  },
                },
                axisLabel: {
                  show: this.showxAaxisLabel,
                  interval: this.intervalX || 0,
                  fontSize: 12,
                  color: "#999999",
                  overflow: "break",
                  width: this.xAaxisLabelWidth || undefined,
                },
              },
            ],
        yAxis: this.yAxis
          ? this.yAxis
          : [
              {
                splitLine: {
                  lineStyle: {
                    type: "dashed", //虚线
                  },
                  show: true,
                },
                type: "value",
                name: "",
                nameTextStyle: {
                  padding: [0, 15, 0, 0],
                  color: "#999999",
                },
                min: this.minY || undefined,
                max: this.maxY || undefined,
                interval: this.intervalY || undefined,
                axisLine: {
                  show: this.showyAaxisLine,
                  lineStyle: {
                    color: "#CCCCCC",
                  },
                },
                axisLabel: {
                  show: this.showyAaxisLabel,
                  color: "#999999",
                },
              },
              this.appendYAxis,
            ],
        series: [],
      };
      return options;
    },
  },
  watch: {
    chartData: {
      handler(newValue, oldValue) {
        this.initData();
      },
      deep: true,
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
      if (this.chartData) {
        this.initData();
      }
    });
  },
  methods: {
    initChart() {
      const _this = this;
      _this.baseLineChart = echarts.init(
        this.$refs[this.name],
        {},
        {
          locale: "ZH",
        }
      );
      window.addEventListener("resize", function () {
        _this.baseLineChart.resize();
      });
    },
    initData() {
      let sData = [];
      if (this.chartData) {
        sData = this.chartData.map((val, index) => {
          let symbol = this.symbol;
          if (this.symbolList.length && this.symbolList[index]) {
            symbol = this.symbolList[index];
          }
          return {
            yAxisIndex:  undefined,
            data: val,
            name: this.seriesName[index],
            type: "line",
            showSymbol: !!val[0].showSymbol?true:false,
            symbolSize: 8,
            symbol: symbol, // emptyCircle
            // areaStyle: this.areaStyle,
            areaStyle: {//区域填充渐变颜色
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: val[0].color1||"transparent" // 0% 处的颜色
                }, {
                  offset: 1, color: val[0].color2||"transparent" // 100% 处的颜色
                }],
                global: false // 缺省为 false
              }
            },
              smooth: true,
          };
        });
        if (Array.isArray(this.appendSeries) && this.appendSeries.length) {
          sData.push(...this.appendSeries);
        } else if (this.appendSeries) {
          sData.push(this.appendSeries);
        }
        this.chartOptions.series = sData;
      }
      if (this.timeX && this.timeX.length) {
        this.chartOptions.xAxis[0].data = this.timeX;
      }
      this.chartOptions.yAxis[0].name = `${this.chartUnit}`;
      this.baseLineChart.setOption(this.chartOptions, true);
    },
  },
};
</script>
