<?xml version="1.0" encoding="UTF-8"?>
<config>
	<param key="NLP_SERVER_ADDR" name="NLP服务地址" type="String"  description="NLP服务地址" value="http://ip:port"></param>
	<param index="2" key="IS_START_TASK" name="总定时任务" type="radio" items="{Y:是,N:否}"  description="是否开启该模块定时任务目前影响XXX，默认关闭" value="N"/>
	<param key="NLP_SUMMARY_ADDR" name="NLP小结地址" type="String"  description="NLP小结地址" value="http://*************:7720"></param>
	<param key="NLP_APP_ID" name="NLP APPID" type="String"  description="NLP APPID" value="testMsg"></param>
	<param key="NLP_TEXT_MATCHING_ADDR" name="NLP相似度匹配URL" type="String"  description="NLP相似度匹配URL:http://ip:port/imusic/textMatchingSingle" value="" index="9"></param>
	<param key="SMS_AUTH_CODE_TEMPLATE" name="短信验证模板CODE+预占值" type="String"  description="例:1091549233,randcode" value="" index="10"></param>
	<param key="NLP_EXTRACT_INFO_ADDR" name="NLP意图识别服务地址" type="String"  description="NLP意图识别服务地址" value="http://ip:port" index="11"></param>
	<param key="REFUND_REQ_MAX_NUM" name="退费请求最大次数" type="String"  description="退费请求最大次数" value="5" index="12"></param>
	<param key="REFUND_NODES" name="退费流程节点" type="String"  description="退费流程节点" value="USERTASK_gey5ho3tr,USERTASK_trhrowc3uf,USERTASK_hwkomzhh2i,USERTASK_stzpviez0h,USERTASK_w3zyktmeba,USERTASK_v7f0w03pxb,USERTASK_trhrowc3uf,USERTASK_6bky5k9et" index="13"></param>
	<param key="REFUND_WAIT_NODE_ID" name="退费等待节点ID" type="String"  description="退费等待节点ID" value="RECEIVETASK_ncmgs9wnh" index="13"></param>
	<param key="AUTO_HANDLE_NODE_ID" name="自动处理节点ID" type="String"  description="自动处理节点ID" value="RECEIVETASK_80kx403twn,RECEIVETASK_tkcozd4g86" index="14"></param>
	<param key="AGENT_DEAL_NODES" name="坐席处理节点" type="String"  description="坐席处理节点" value="USERTASK_3ly0yoolfg,USERTASK_z6av5qbpe,USERTASK_6ds73wh5wo,USERTASK_th0pxtti2" index="14"></param>
	<param key="AGENT_DEAL_ADD_FOLLOW" name="坐席处理追加诉求节点ID" type="String"  description="坐席处理追加诉求节点ID" value="USERTASK_z6av5qbpe" index="15"></param>
	<param key="AUTO_HANDLE_SWITCH" name="自动处理开关" type="String"  description="自动处理开关" value="Y" index="16"></param>
	<param key="AUTO_REPLY_BASE" name="自动回复消息" type="String"  description="自动回复消息" value="收到，您的需求已记录，我们处理完成会第一时间回复，谢谢" index="17"></param>
	<param key="USER_RES_ID" name="省份资源id配置" type="String"  description="省份资源id配置" value="" index="18"></param>
	<param key="SKIP_ORDER_KEYS" name="越级工单匹配关键字(迁移至模型标注)" type="String"  description="越级工单匹配关键字" value="越级工单,通管局,12315,315,市长热线,消委会" index="19"></param>
	<param key="OPINION_ORDER_KEYS" name="舆情工单匹配关键字(迁移至模型标注)" type="String"  description="舆情工单匹配关键字" value="舆情工单,黑猫网,微博,12300,媒体曝光" index="20"></param>
	<param key="MIIT_ORDER_KEYS" name="工信部匹配关键字(迁移至模型标注)" type="String"  description="工信部匹配关键字" value="工信部,工信部转派,集团定责,申诉报告" index="21"></param>
	<param key="DEFAULT_WORK_ID" name="默认缺省工作组ID" type="String"  description="默认缺省工作组ID" value="82988488461528904683959" index="22"></param>
	<param key="NO_NEED_REPLY_KEYCODE" name="无需回复关键字(迁移至模型标注)" type="String"  description="无需回复关键字" value="无需联系,不用联系,直接退费即可,直接退订退费,无需回访,无需回电" index="23"></param>

	<param key="ORDER_AUTO_ASSIGN_NUMS" name="工单自动分配最大待办数" type="int" description="工单自动分配最大待办数" value="10"/>

	<param key="ORDER_AUTO_RECOVERY_WORKGROUPS" name="工单自动回收的工作组" type="String" description="工单自动回收的工作组" value="W(初级客服),W(中级客服),W(高级客服)"/>
	<param key="ORDER_AUTO_ASSIGN_WORKGROUPS" name="工单自动分派的工作组" type="String" description="工单自动分派的工作组" value="初级客服,中级客服,高级客服"/>


	<param key="AYY_ORDER_FTP_SERVER" name="历史工单FTP服务地址" type="String"  description="历史工单FTP服务地址" value="" index="24"></param>
	<param key="AYY_ORDER_FTP_SERVER_ACC" name="历史工单FTP账号" type="String"  description="历史工单FTP账号" value="" index="25"></param>
	<param key="AYY_ORDER_FTP_SERVER_PWD" name="历史工单FTP密码" type="String"  description="历史工单FTP密码" value="" index="26"></param>

	<param key="AYY_WOEKGROUP_SEND_CONFIG" name="坐席处理转派工作组" type="String"  description="坐席处理转派工作组名字,用英文逗号分割（,）" value="" index="27"></param>


	<param key="AYY_ORDER_SUPER_TIME" name="工单超时时限" type="String"  description="大于创建时间+时限" value="24" index="28"></param>
	<param key="AYY_ORDER_TIMEOUT_TIME" name="工单报警时限" type="String"  description="工单超时时限前多少小时报警" value="6" index="28"></param>
	<param key="AYY_ORDER_TIMEOUT_NOTICE_WORKGROUP" name="预警通知工作组" type="String"  description="时间点 通知工作组 例如：10:10,10:20,10:30,10:40 客服主管" value="客服主管,爱音乐客服组人员" index="29"></param>
	<param key="AYY_ORDER_MINISTRY_WORKGROUP" name="建单识别工信部通知工作组" type="String"  description="建单识别工信部通知工作组,用英文逗号分割（,）" value="客服主管" index="30"></param>
	<param key="AYY_ORDER_MINISTRY_WORKGROUP_OPEN" name="建单识别工信部通知工作组开关" type="radio" items="{Y:开启,N:关闭}"  description="开启即可发送工信部工单通知" value="N" index="31"></param>

	<param key="AYY_ORDER_NOTLOGIN_TIME" name="下线时限" type="String"  description="冻结未登录账号天数(天)" value="30" index="33"></param>
	<param key="AYY_ORDER_REMINDER_WORKGROUP" name="催单通知工作组" type="String"  description="催单通知工作组，用逗号分隔" value="客服主管,客服组长" index="34"></param>
	<param key="AYY_ORDER_UPDATE_EXPORT" name="导出更新数据开关" type="radio" items="{Y:开启,N:关闭}"  description="导出更新数据开关" value="Y" index="31"></param>
	<param key="AYY_RPA_GRAP_FAIL_NOTICE" name="rpa抓取预警通知" type="String"  description="rpa抓取预警通知" value="" index="37"></param>

	<param key="AYY_ORDER_UPDATE_FIELD_TIME" name="工单字段更新时间长度" type="String"  description="工单字段更新时间长度" value="7" index="34"></param>

	<param key="AYY_ORDER_RPA_NOTICE_PHONE" name="群掉线预警通知手机号" type="String"  description="群掉线预警通知手机号，用,分割" value="" index="90"></param>

	<param key="AYY_ORDER_MINISTRY_EXPORT_WORKGROUP" name="工信部清单推送工作组" type="String"  description="时间点 每天预警工作组 每个月汇总弄工作组 例如：10:10,10:20,10:30,10:40 客服主管" value="客服主管" index="34"></param>

	<param key="CHANNEL_FORWORD_PROC_TIMES" name="渠道转发默认处理时限(min)" type="String"  description="渠道转发默认处理时限(min)" value="120" index="35"></param>

	<param key="PRODUCT_TAG_MAP" name="产品标签映射" type="String"  description="产品标签映射,00代表其他未配置工单类型，放到最后兜底" value="05:vv3,07:vv4,00:vv2" index="36"></param>
	<param key="AYY_SSO_ACC_DEPT" name="爱音乐单点登录账号部门" type="String"  description="爱音乐单点登录账号部门， 以,号分割多个" value="" index="37"></param>
	<param key="AYY_HANDLE_REPLY_NODE" name="处理回复权限节点" type="String"  description="处理回复权限节点,用英文逗号分割（,）" value="坐席处理1,坐席处理二,人工回复" index="340"></param>

	<param key="AYY_CONTACT_KEY_WORDS" name="联系号码关键词" type="String"  description="联系号码关键词,用英文逗号分割（,）" value="联系号码,请联系,联系电话,联系方式,拨打" index="340"></param>
	<param key="AYY_REFUND_KEY_WORDS" name="退费号码关键词" type="String"  description="退费号码关键词,用英文逗号分割（,）" value="退至,退到,退费至,退费到,退费号码" index="341"></param>
	<param key="AYY_CRM_WARNING_CONFIG" name="crm录单预警配置" type="String"  description="CRM预警时间范围?CRM多长时间没有录单(分钟)?CRM接收人手机号@广东工作流预警时间范围?广东工作流多长时间没有录单(分钟)?广东工作流接收人手机号" value="08:00,22:00?60?16626625693@08:00,22:00?60?16626625693" index="340"></param>

	<param key="ORDER_VERSION" name="表单版本是否使用新版本" type="radio" items="{Y:是,N:否}"  description="表单版本是否使用新版本，默认关闭，开启后切换到新版本" value="N" index="340"></param>

	<param key="AYY_FORMARK_AUTH" name="一键处理权限角色" type="String"  description="配置了的角色名称拥有一键处理权限" value="" index="340"></param>

	<param key="AYY_NOPRODUCT_IDQQQQ" name="不是功能彩铃产品ID" type="String"  description="不是功能彩铃产品ID,以,号分割" value="7360110000100955,7360110000100956,7360110000100957,7360110000100958,7360110000100911,7360110000100912,7360110000100959,7360110000100933,7360110000100934,7360110000100960,7360110000100961,7360110000100962" index="341"></param>


	<param key="AYY_COMPLAINTN_EO_TYPE" name="不允许追诉的投诉协商记录的操作类型" type="String"  description="不允许追诉的投诉协商记录的操作类型" value="USER_CREATE_COMPLAINT" index="341"></param>

	<param key="WX_CONTENT_MIDS" name="微信退款商户id" type="String"  description="商户id，多个以,号分割" value="1220973601" index="99"></param>

	<param key="QW_NOTICE_USER_BY_ZG" name="企微通知账户-客服主管" type="String"  description="企微通知账户-客服主管，多个使用|分隔" value="" index="100"></param>
	<param key="QW_NOTICE_USER_BY_JL" name="企微通知账户-客服经理" type="String"  description="企微通知账户-客服经理，多个使用|分隔" value="" index="101"></param>
	<param key="QW_NOTICE_CORP_ID" name="企微通知-企业微信ID" type="String"  description="企微通知-企业微信ID" value="wxef66dffc2d2f1a10" index="102"></param>
	<param key="QW_NOTICE_AGENT_ID" name="企微通知-企业AgentId" type="String"  description="企微通知-企业AgentId" value="1000072" index="103"></param>


	<param key="CRM_UPLAOD_FTP_IPPORT" name="上传CRM集团FTP地址" type="String"  description="CRM集团FTP地址" value="***********:2600" index="340"></param>
	<param key="CRM_UPLAOD_FTP_USERNAME" name="上传CRM集团FTP账号" type="String"  description="CRM集团FTP账号" value="67d82127d01cc09d6e8c8c7e073a8488" index="340"></param>
	<param key="CRM_UPLAOD_FTP_PWD" name="上传CRM集团FTP密码" type="String"  description="CRM集团FTP密码" value="dc37d0fb8762f08651a3f30c66a8e472" index="340"></param>

	<param key="CRM_DOWNLAOD_FTP_IPPORT" name="下载CRM集团FTP地址" type="String"  description="CRM集团FTP地址" value="***********:2600" index="340"></param>
	<param key="CRM_DOWNLAOD_FTP_USERNAME" name="下载CRM集团FTP账号" type="String"  description="CRM集团FTP账号" value="ayy_ftp_upload" index="340"></param>
	<param key="CRM_DOWNLAOD_FTP_PWD" name="下载CRM集团FTP密码" type="String"  description="CRM集团FTP密码" value="8d27caa7daba3521b5e3b157d7560018" index="340"></param>
	<param key="CRM_DOWNLAOD_FILE_PATH_PREFIX" name="下载CRM集团FTP路径前缀" type="String"  description="下载CRM集团FTP路径前缀，默认appid+download" value="/1933400870118481922/download/" index="340"></param>

</config>
