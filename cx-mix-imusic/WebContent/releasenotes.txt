---------------------------------------------------------------------------------------
版本号:  1.0#20250701-1
修改日期: 2025-07-01
修改人: 许正佳
脚本目录： 无
版本说明：
    1.修复自动处理无群组时意图判断异常问题
    2.修复自动处理操作描述类型错误问题
---------------------------------------------------------------------------------------
版本号:  1.0#20250625-1
修改日期: 2025-06-25
修改人: 许正佳
脚本目录： 无
同步操作：
    1、解析工单系统在C_BOX_MUSIC_STANDARD_EX表中添加CRM相关字段
影响程度: 低
    - 工单录入兼容联系号码获取
    - 自动处理兼容
        - 自动处理：重构自动处理代码逻辑，增加CRM撤诉|归档时，跳过自动处理，流转到人工的逻辑
        - 请求退费：判断是否CRM撤诉|归档，流转到退费重试
        - 退费重试：增加定时任务，扫描已被撤诉、归档的退费重试工单，流转到人工
    - 添加追诉处理和催单处理方法
    - 在C_BOX_MUSIC_STANDARD_EX表中添加CRM相关字段
---------------------------------------------------------------------------------------
版本号:  1.0#20250617-1
修改日期: 2025-06-17
修改人: 张杨宋
脚本目录： 无
影响程度: 低
    - 修复微信退款相关问题
---------------------------------------------------------------------------------------
版本号:  1.0#20250616-1
修改日期: 2025-06-16
修改人: 张杨宋
脚本目录： 无
影响程度: 低
    - 优化微信支付退款金额计算
---------------------------------------------------------------------------------------
版本号:  1.0#20250612-1
修改日期: 2025-06-12
修改人: 张杨宋、许正佳
脚本目录： 无
影响程度: 低
    feat(template): 增加联系成功与否的筛选功能
    - 在模板匹配页面添加是否联系成功的筛选条件
    - 在模板匹配的 SQL 查询中增加 CONTACT_RESULT 字段的查询
    - 更新数据库表结构，在 cx_template_match 表中添加 CONTACT_RESULT 列
    - 优化模板匹配结果的展示，增加是否联系成功的列
    - RPA建单话费账单识别修改为受理号码

---------------------------------------------------------------------------------------
版本号:  1.0#20250611-1
修改日期: 2025-06-11
修改人: 张杨宋
脚本目录： 无
影响程度: 低
    - 更新数据库查询以获取 APPEAL_SOURCE 字段
    - 添加日志记录以提高可追溯性
    - 优化省份来源判断逻辑
    - 修改 STATUS 字段类型以适应业务需求
    - 在 LoadTemplateHandler 中增加联系信息匹配逻辑
---------------------------------------------------------------------------------------
版本号:  1.0#20250610-1
修改日期: 2025-06-10
修改人: 张杨宋
脚本目录： 无
影响程度: 低
    - 修改工单处理逻辑，增加来源号码处理
    - 更新数据库表结构，移至指定数据库并添加索引
    - 添加工单来源号码更新的 SQL 语句
---------------------------------------------------------------------------------------
版本号:  1.0#20250610-1
修改日期: 2025-06-10
修改人: 林佳兴
脚本目录： 无
影响程度: 低
    1、 修复收银台短信验证码列表手机号显示字段
    2、修改菜单名称及位置
---------------------------------------------------------------------------------------
版本号:  1.0#20250606-3
修改日期: 2025-06-06
修改人: 许正佳
脚本目录： 无
影响程度: 低
    1.添加退费号码识别功能并优化订单信息查询
        - 在Constants.java中添加了获取退费关键字的方法getAyyRefundKeyWords。
        - 在OrderBaseService.java中添加了退费号码识别方法findRefundPhone，并在处理消息时识别退费号码。
    2.优化订单信息查询
        - 在RpaService.java中优化了getOldOrderInfo方法，增加了来源排序，优先匹配相同来源的工单。
---------------------------------------------------------------------------------------
版本号:  1.0#20250606-2
修改日期: 2025-06-06
修改人: 张杨宋
脚本目录： 无
影响程度: 低
    - 新增 ImusicStatService 和 PetraDataStat 类实现统计功能
    - 添加定时任务和数据字典支持- 优化订单处理流程，增加来源单号必填规则
    - 更新数据库表结构，增加统计相关字段
---------------------------------------------------------------------------------------
版本号:  1.0#20250605-2
修改日期: 2025-06-05
修改人: 林佳兴
脚本目录： 无
影响程度: 低
    1、 修复工单一键处理未修改三级分类下拉框的中文值
    2、工单联系情况改为多选
    3、业务查询处理的其他渠道新增收银台短信验证码列表
---------------------------------------------------------------------------------------
版本号:  1.0#20250605-1
修改日期: 2025-06-05
修改人: 张杨宋
脚本目录： 无
影响程度: 低
- 在数据库中增加 CONTACT_ARRAY 字段用于存储联系用户情况
- 实现联系用户情况数据的处理和展示
-优化模板处理逻辑，支持成功联系情况和失败联系情况的展示- 修复了一些与订单流程相关的小问题
---------------------------------------------------------------------------------------
版本号:  1.0#20250604-1
修改日期: 2025-06-04
修改人: 许正佳
脚本目录： 无
影响程度: 低
    - 前置客服2.3场景增加追加退费金额逻辑
---------------------------------------------------------------------------------------
版本号:  1.0#20250529-4
修改日期: 2025-05-29
修改人: 许正佳
脚本目录： 无
影响程度: 低
    - 完善前置客服捞单需求
    - 修复待办节点名称查询限制
    - 增加模型标注 - 产品信息错误反馈（需要同步字典，配置训练关键字）
---------------------------------------------------------------------------------------
版本号:  1.0#20250527-1
修改日期: 2025-05-27
修改人: 许正佳
脚本目录： 无
影响程度: 低
    - 迁移调整前置客服工单处理类
    - 迁移RpaService方法，增加到OrderBaseService
    - 增加前置客服追诉、归档需求(temp)
---------------------------------------------------------------------------------------
版本号:  1.0#20250528-1
修改日期: 2025-05-28
修改人: 许正佳
脚本目录： 无
影响程度: 低
    - 业务工单回复增加type入参，控制返回手里号码是否加密
---------------------------------------------------------------------------------------
版本号:  1.0#20250527-1
修改日期: 2025-05-27
修改人: 许正佳
脚本目录： 无
影响程度: 低
    - 工单回复增加searchCode参数，支持工单号+手机号筛选
---------------------------------------------------------------------------------------
版本号:  1.0#20250516-3
修改日期: 2025-05-16
修改人: 许正佳
脚本目录： 无
影响程度: 低
    - 增加企微通知接口，增加企微通知相关配置项QW_NOTICE_USER_BY_ZG、QW_NOTICE_USER_BY_JL、QW_NOTICE_CORP_ID、QW_NOTICE_AGENT_ID
    - 工单回复接口增加创建人账户、创建人姓名
---------------------------------------------------------------------------------------
版本号:  1.0#20250508-1
修改日期: 2025-05-08
修改人: 许正佳、张杨宋
脚本目录： 无
影响程度: 低
    - 业务处理工单查询接口增加流程名称、表单key字段
    - 优化订单列表展示和统计功能
    - 在 AllOrderListDao 中对订单数据按时间降序排序
    - 在 ApiStatScheduleService 中优化 API 调用统计逻辑
    - 在前端 hisOrder.html 中添加订单时间列的排序功能
    - 修复部分省份名称显示问题- 优化退费流程中的工单创建和处理逻辑
---------------------------------------------------------------------------------------
版本号:  1.0#20250429-1
修改日期: 2025-04-29
修改人: 许正佳
脚本目录： 无
影响程度: 低
    - 修复RPA追诉未识别到坐席处理节点问题
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20250427-1
修改日期: 2025-04-27
修改人: 许正佳
脚本目录： 无
影响程度: 低
    - 增加H5话单查询接口 OrderBusiServlet.actionForGetProductBillStat
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20250422-1
修改日期: 2025-04-22
修改人: 许正佳
脚本目录： 无
影响程度: 低
    - 定制实时人力监控界面改造
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20250421-1
修改日期: 2025-04-21
修改人: 许正佳
脚本目录： 无
影响程度: 低
    - 迁移实时人力监控界面并增加IVR成功率、IVR放弃率指标
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20250418-1
修改日期: 2025-04-18
修改人: 张杨宋
脚本目录： 无
影响程度: 低
- 更新通话记录页面路径
- 注释掉部分报表相关代码
- 修改呼叫类型和成功标志字段
- 优化模板处理逻辑
- 调整黑名单处理方式
- 更新开放平台认证参数

---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20250416-1
修改日期: 2025-04-16
修改人: 许正佳
脚本目录： 无
影响程度: 低
说明:
	1、增加定制工单通知场景
	    工单转派通知
	    工单催办通知
	    工单超时通知
	    工信部工单通知
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20250415-1
修改日期: 2025-04-15
修改人: 许正佳
脚本目录： 无
影响程度: 低
说明:
	1、批量建单逻辑优化：批量建单正则校验前，兼容连续的多个特殊字符转换成单空格，以满足批量建单需求
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20250411-1
修改日期: 2025-04-11
修改人: 许正佳
脚本目录： 无
影响程度: 低
说明:
	1、移除自动处理查询开销户判断
	2、新增策略：
	    业务已退订 - 不执行销户查询，直接走模板匹配
        业务未退订，查询是否销户：
             已销户 - 走自动处理，模板匹配
             未销户 - 转人工
    3、查询开销户增加产品类型识别，仅识别视频、音频类型产品
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20250409-1
修改日期: 2025-04-09
修改人: 许正佳
脚本目录： 无
影响程度: 低
说明:
	1、自动处理-查询退订渠道增加开销户查询
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20250327-1
修改日期: 2025-03-27
修改人: 许正佳
说明:
	1、批量建单兼容：和|与汉字
	2、三级分类识别优化，兼容根据三级分类名称反推一级、二级分类。
	3、退费金额识别优化，产品金额为空时继续识别退费金额。
	4、增加脚本script_mysql_20250327.sql，修复订购渠道长度不足问题
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20250326-1
修改日期: 2025-03-26
修改人: 林佳兴
说明:
	1、工单列表日期选择器统一添加快捷键
  2、白名单功能优化
  3、前置客服工单列表隐藏
  4、工单详情添加客户标签展示
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20250314-1
修改日期: 2025-03-14
修改人: 许正佳
说明:
	1、RPA产品识别，产品ID重复时：
	    1)优先取值在用业务产品记录
        2)没有在用业务产品记录情况下，优先取值产品编码不为空的记录
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20250311-1
修改日期: 2025-03-11
修改人: 许正佳
说明:
	1、RPA捞单增加退订渠道字段入库
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20250306-1
修改日期: 2025-03-06
修改人: 许正佳
说明:
	1、rpa产品识别，产品名称重复时判断产品ID与产品编码是否有重复，重复则过滤去重
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20250303-1
修改日期: 2025-03-03
修改人: 许正佳
说明:
	1、增加查询退订渠道意图
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20250211-1
修改日期: 2025-02-11
修改人: 许正佳
说明:
	1、增加前置客服捞单能力
	2、增加前置客服工单列表、详情页面
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20241105-1
修改日期: 2024-11-05
修改人: 许正佳
说明:
	1、增加一键回复接口
	2、增加对外提供回复结果回传接口
	3、建单增加原始派单时间、处理超时时间字段
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20241129-1
修改日期: 2024-11-29
修改人: 许正佳
说明:
	1、RPA识别优化
	    意图识别、关键字识别增加“!”非的判断。
        意图增加公共命中词和公共屏蔽词判断。
        公共命中词：必须要命中才会继续匹配该意图下的关键字，为空则忽略。
        公共屏蔽词：命中公共屏蔽词时，不匹配该意图
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20241012-1
修改日期: 2024-10-12
修改人: 许正佳
说明:
	1、渠道转发抓取兼容图片抓取开发
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20241011-1
修改日期: 2024-10-11
修改人:  谭卫聪
说明:
    1.表单 - 每个节点添加保存产品调用接口
    2.表单 - 录单节点手机号码失去焦点事件添加判断，若输入号码跟派单号码不一致才刷新产品列表
    3.表单 - 坐席处理1、坐席处理2、人工确定添加保存产品失败判断
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20241011-1
修改日期: 2024-10-11
修改人:  许正佳
说明:
    1.修复 - 自动分配仅分配配置项ORDER_AUTO_ASSIGN_WORKGROUPS中的工作组坐席
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240919-1
修改日期: 2024-09-19
修改人:  许正佳
说明:
    1.修复退费等待未计算渠道转发未回传结果时流转问题
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240918-1
修改日期: 2024-09-18
修改人:  许正佳
说明:
    1.退费等待接口增加校验，退费失败时，默认转人工处理，兼容产品数为空的情况
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240903-1
修改日期: 2024-09-03
修改人:  许正佳
说明:
    1.自动回收增加离线时间判断，依赖cc-base版本 20240903
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240828-1
修改日期: 2024-08-28
修改人:  许正佳
说明:
    1.增加转人工关键字，命中时进行转人工操作
    2.增加新的模型标注，提供如下配置，并将原模块配置迁移到模型标注中：
    	1.无需联系关键字
    	2.转人工关键字
    	3.越级工单关键字
    	4.舆情工单关键字
    	5.越级工单关键字
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240826-1
修改日期: 2024-08-26
修改人:  许正佳
说明:
    1.增加渠道转发日志记录，用于跟进后续渠道回复
    2.自动渠道转发和手动渠道转发时，均生成渠道转发日志
    3.增加10分钟定时任务，扫描即将超时的渠道发送记录，根据处理时间判断是否即将超时，并发送催单消息
    4.上述定时任务中继续增加判断，已经超时的渠道记录，进行坐席内部通知
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240822-1
修改日期: 2024-08-22
修改人:  许正佳
说明:
    1、增加所有工单导出、产品维度导出增加六个字段
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240813-1
修改日期: 2024-08-14
修改人:  张杨宋
说明:
    1、增加所有工单导出、产品维度导出增加六个字段
    2、增加所有工单、省份前置、旧系统工单的所有手机号脱敏
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240813-1
修改日期: 2024-08-13
修改人:  许正佳
说明:
    1、调整自动审核、退费的金额计算，审核时计算渠道产品的金额，退费时排除渠道产品金额
    2、新所有工单修复流程图bug
    3、广新客服建单Bug修复（不再调用前置工单接口）
    4、录单添加父节点Id组件（表单js，方便创建子工单）增加所有工单导出、产品维度导出增加六个字段
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240809-1
修改日期: 2024-08-09
修改人:  许正佳、谭卫聪
说明:
    1、增加渠道转发按钮，坐席处理2节点可以点击发起渠道转发
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240807-1
修改日期: 2024-08-07
修改人:  谭卫聪
说明:
    1、更新产品类型规则，规则放在html。删除js原来的代码（人工回复、人工确定、录单、坐席处理1、坐席处理2）
    2、广信建单添加关联子工单（录单js）
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240807-1
修改日期: 2024-08-07
修改人:  许正佳
说明:
    1、修复-修复退费节点无法识别到接受任务接口问题：请求退费、退费等待、退费重试
    	1）配置项：REFUND_NODES增加配置：RECEIVETASK_ncmgs9wnh,RECEIVETASK_fb3jkz92ip,RECEIVETASK_zs6xpx7sq
    2、修复-一张单存在两条相同产品时，自动审核直接失败
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240801-2
修改日期: 2024-08-01
修改人:  张杨宋、许正佳
说明:
    1、增加群掉线短信通知，需要配置短信模版和接受号码
    2、人工处理、人工回复、录单、坐1、坐2添加退费时退赔账号必填（工单JS)
    3、修复邮件自动回复状态更新值
    4、修复RPA退费意图识别异常
    5、兼容请求退费失败时，更新退费结果字段
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240730-1
修改日期: 2024-07-30
修改人:  张杨宋
说明:
    1、优化导出
    2、增加超时、即将超时工单查询
    3、首页添加延时工单跳转
    4、创建人、处理人、工单号各个节点都禁用（工单JS)增加群掉线短信通知，需要配置短信模版和接受号码
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240726-1
修改日期: 2024-07-26
修改人:  张杨宋
说明:
    1、优化所有工单查询
    2、优化9099接口
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240725-1
修改日期: 2024-07-25
修改人:  许正佳
说明:
    1、邮件录单附件调整
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240724-1
修改日期: 2024-07-24
修改人:  许正佳、张杨宋
说明:
    1、退订接口调整
	2、开放同退功能
	3、优化操作日志描述
	4、RPA追诉流程调整
	5、优化订购记录的退订时间
	6、优化ivr业务逻辑
	7、优化所有工单查询
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240722-1
修改日期: 2024-07-22
修改人:  许正佳
说明:
    1、增加自动回复状态字段
    2、同退同订需求开发
    3、话费账单查询根据订购时间查询
    4、解决渠道转发非自动处理时，退费等待卡单问题
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240716-1
修改日期: 2024-07-16
修改人:  许正佳
说明:
    1、批量建单号码识别流程优化
    2、退费金额识别优化，相同的金额识别当成一个金额
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240712-1
修改日期: 2024-07-12
修改人:  许正佳
说明:
    1、号码匹配增加限制，号码签后存在数字时，不予匹配
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240711-1
修改日期: 2024-07-11
修改人:  许正佳
说明:
    1、更新抓取消息消息结果、结果描述字段
    2、修改固话号码识别正则
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240710-2
修改日期: 2024-07-10
修改人:  许正佳
说明:
    1、修改退费审核话单排序
    2、增加退费处理模板，增加退订时间为空时查询退订接口
    3、增加退赔原因不能为空校验
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240708-1
修改日期: 2024-07-08
修改人:  许正佳
说明:
    1、修复坐席处理1提交渠道转发卡单问题
    2、修复请求退费客服主管未审核已经执行请求退费的问题
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240704-1
修改日期: 2024-07-04
修改人:  许正佳
说明:
    1、省份增加退赔账号，接口入参，有值优先使用退赔账号，无值使用受理号码
    2、修复RPA查询在用业务时，默认当前时间为退订时间问题
    3、录单保存新增退赔账号入参
	4、所有节点开放退赔账号
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240702-1
修改日期: 2024-07-02
修改人:  许正佳
说明:
    1、录单添加渲染之后状态（initFlat），在工单类型、申诉来源、省份、1级分类、2级分类、3级分类选中事件添加判断
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240628-2
修改日期: 2024-06-28
修改人:  许正佳
说明:
    1、修复坐席处理1转退费流程时，会因为自动处理失败进入坐席处理2的问题
    2、批量建单分割符等原因异常时，或者建单剩余话术包含手机号时，不进行批量建单
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240624-1
修改日期: 2024-06-24
修改人:  许正佳
说明:
    1、增加退费等待界面，用于人工操作退费等待的工单，暂不开放菜单
    /cx-mix-imusic/pages/refund/refundWait.html
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240620-1
修改日期: 2024-06-20
修改人:  许正佳
说明:
    1、修复自动处理跟进记录问题
    2、修改坐席处理2、人工确认2表单是否退赔选中JS，取消更新自动处理状态
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240619-3
修改日期: 2024-06-19
修改人:  许正佳
说明:
    1、群工单自动处理时，包含退费意图，转坐席处理
    2、来源和群昵称增加意图处理和回复开关，分别判断各自类型
    3、自动处理增加来源意图判断
    4、自动回复增加群组意图判断
    5、自动处理整体增加跟进记录描述（之前不全）
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240617-1
修改日期: 2024-06-17
修改人:  许正佳
说明:
    1、自动处理时，生成回复模板失败后，转人工处理
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240616-1
修改日期: 2024-06-16
修改人:  许正佳
说明:
    1、增加催单提醒，提醒客服组长、客服主管、退费专员
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240615-4
修改日期: 2024-06-15
修改人:  许正佳
说明:
    1、退费增加号码类型判断，增加固话退费
    2、省份建单增加pOrderId
    3、省份和RPA增加子工单触发通知父工单处理人
    4、修复追诉内容相同时，未触发催办
    5、切换退费账号，更改为退赔账号（原使用受理号码）
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240614-1
修改日期: 2024-06-14
修改人:  张杨宋
说明:
    1、修改表单字段，更改诉求内容存储字段
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240612-1
修改日期: 2024-06-12
修改人:  许正佳
说明:
    1、重复建单查询接口增加流程实例ID
    2、渠道转发兼容渠道ID获取并渠道转发
    3、录单提交前添加关联工单或追诉工单（表单js）
    4、坐席处理2与人工节点添加渠道退赔回流工单池操作（表单js）
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240611-1
修改日期: 2024-06-11
修改人:  许正佳、谭卫聪
说明:
    1、在坐席处理2和人工确认节点，选择了渠道产品、并且处理状态为处理中的时候，弹出提示，并进行暂存和释放的操作，让工单回到工单池
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240607-1
修改日期: 2024-06-07
修改人:  许正佳
说明:
    1、处理内容增加工单类型 orderLevelType 01-本工单 02-父工单 03-子工单
    2、需求调整
		调整1：
		场景：
		RPA追诉时，原工单处于退费节点时，追诉内容为无法识别意图（直接使用：号码+测试类似话术）
		新需求：创建子工单
		原需求：追诉并通知

		调整2：
		场景：
		退订非在用业务产品时，如订购记录
		新需求：读取订购记录中的退订时间，没有再从退订列表中获取该产品的退订时间，查询到则识别到意图，可以进行自动处理（需要无需联系关键字才可自动处理）
		原需求：退订非在用业务需求，直接无法识别产品，当成识别失败处理
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240605-1
修改日期: 2024-06-05
修改人:  许正佳、张杨宋
说明:
    1、产品ID生成增加重复校验
    2、增加号码工单查询接口，判断是否需要创建子工单
    3、增加催单接口，短信通知和内部通知
    4、追诉内部通知

---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240604-1
修改日期: 2024-06-04
修改人:  许正佳、张杨宋
说明:
    1、处理内容合并，规则如下：
    	1)单产品多意图时，按产品优先级，退费>退订>查询来生成处理内容，如有屏蔽意图追加单独话术。如果单意图时，直接命中对应意图话术。
		2)多产品时，将不同产品按第1点，拼接生成回复语
	2、工单导出合并投诉内容和处理内容
	3、发送通知过滤工作组存在相同账号
	4、工信部工单发送通知
	5、退费重试工单列表增加详情、退费失败原因
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240529-1
修改日期: 2024-05-29
修改人:  张杨宋
说明:
    1、6 小时即将超时: 在工单超时前 6 个小时，系统下发超时预警短信、邮件到客服处理人客服主管和爱音乐客服组人员。
    2、优化工作组分派查询
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240527-2
修改日期: 2024-05-27
修改人:  张杨宋、许正佳、谭卫聪
说明:
    1、工信部工单分配到工作组且发送通知
    2、更新《退费专员审核》表单，增加“请选择”默认选项
    3、优化imusic调用接口省份api参数
    4、退费功能费优化查询
    5、修复退费流程追加退费意图时未创建子单问题
    6、调整退费接口入参工单ID调整为工单编号
    7、套餐查询
    8、录单退赔原因取消默认必填（解决选否后仍要提示退赔原因）
    9、短信验证码菜单移动与样式优化
    10、订购记录添加订购渠道号
    11、坐席处理1、坐席处理2、人工回复将退赔原因与退赔内容必填放进refund为N判断里面
    12、坐席处理1、坐席处理2、人工回复添加分配按钮
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240523-1
修改日期: 2024-05-23
修改人:  许正佳
说明:
    1、自动审核增加倍率限制，倍率小于1大于3时，自动审核失败
    2、自动审核增加异常拦截处理，异常后默认审核失败，流转到人工确认
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240516-1
修改日期: 2024-05-16
修改人:  张杨宋
说明:
    1、优化历史工单查询
    2、追诉权限去掉暂存
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240515-1
修改日期: 2024-05-15
修改人:  郭远平
说明:
    1、自动回收不回收锁定的工单
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240514-1
修改日期: 2024-05-14
修改人:  谭卫聪、张杨宋
说明:
    1、坐席处理节点、坐席处理2、录单节点添加本次退费数(月)||理赔倍数是否有未填判断
    2、号段优化
    3、省份工单查询归档增加强制关闭条件

---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240513-3
修改日期: 2024-05-13
修改人:  郭远平、许正佳、谭卫聪
说明:
    1、省份追诉时，判断工单节点为自动处理和退费节点时，提示用户当前工单节点名称，并让客户选择是否需要创建子工单，创建子工单将号码、追诉内容带入，并与当前工单创建级联关系。
    2、坐席处理节点，选择了客服理赔，并且状态为处理完成时，弹窗提示坐席，当前工单未进行客服退赔流程，是否需要强制归档。
		提供两个按钮
		强制归档 - 继续提交工单，归档工单
		取消 - 取消本次提交，坐席可以调整处理状态为处理中
	3、自动处理调整，查询意图增加关键字，开通渠道、订购渠道、开通方式、订购方式，识别到关键字才处理查询意图·，未匹配到时，不进行自动处理。
	4、自动分配异常问题，集中分配到了一个工号。
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240511-4
修改日期: 2024-05-11
修改人:  张杨宋、许正佳
说明:
    1、历史工单筛选条件修复
    2、省份提交增加重复提交拦截
    3、增加心意金字段，自动审核判断心意金
    4、退费等待判断是否经过人工处理，人工处理不生成RPA处理内容
    5、增加退费等待失败处理，退费等待失败后，由人工模拟转到退费确认
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240510-3
修改日期: 2024-05-10
修改人:  许正佳
说明:
    1、增加定时任务开关判断
    2、1.自动审核时重新计算话单数、已退费数
	3.查询相同号码、相同产品未退费成功的退费月数之和， 用“剩余退费话单数”比较“本次退费月数”+ “在途工单退费月数”，如果剩余退费话单数小于后两者之和，则审核失败
	4.增加自动处理时，没有处理人默认为RPA机器人
	5.自动审核时更新退费月数字段
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240508-1
修改日期: 2024-05-08
修改人:  张杨宋
说明:
    1、优化开销户记录和退订记录导出的手机号角色脱敏
    2、业务处理优化
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240507-1
修改日期: 2024-05-07
修改人:  许正佳
说明:
    1、进行关键字匹配判断前，屏蔽掉客户号码字段，防止客户号码中包含315、12315关键字导致命中越级工单
    2、历史工单增加附件列表展示，增加附件下载接口
    3、省份历史工单详情添加附件、筛选时间修改为1年
    4、省前置客服派单添加角色权限
    5、历史产品记录退订时间过滤时将空白数据排到最后
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240430-1
修改日期: 2024-04-30
修改人:  张杨宋
说明:
    1、优化历史工单接口
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240429-1
修改日期: 2024-04-29
修改人:  谭卫聪、张杨宋
说明:
    1、前置客服提醒功能优化
    2、渠道配置更改唯一限制条件
    3、省份前置菜单添加到导航栏
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240428-1
修改日期: 2024-04-28
修改人:  许正佳、谭卫聪
说明:
    1、退费处理内容类型错误修复
    2、省份历史工单去掉工单状态筛选多余选项
    3、录单附件上传不清空处理内容
    4、黑名单操作日志默认查询时间改为一周（后端限制查询一周：数据量太大）
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240427-2
修改日期: 2024-04-27
修改人:  许正佳、谭卫聪、张杨宋
说明:
    1、修复诉求内容手机号未正常加入问题
    2、修复省份被Q群自动处理拦截问题，无Q群昵称时无需经过Q群判断
    3、前置客服工单列表更新工单类型字典
    4、前置客服个人铃音设置播放模式无反应问题
    5、前置客服个人铃音设置默认铃音无反应问题
    6、优化省份追诉按钮权限
    7、改动表单渲染前后状态top为window，处理内容存储字段改动
    8、优化历史工单列表sql
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240426-1
修改日期: 2024-04-26
修改人:  许正佳、谭卫聪、张杨宋
说明:
    1、修复部分12位固话识别异常问题： 区号识别前移到截断之前、调用爱音乐接口改为原始号码
    2、省份历史工单添加工单来源字典
    3、优化历史工单字段
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240425-3
修改日期: 2024-04-25
修改人:  张杨宋、郭远平、许正佳、谭卫聪
说明:
    1、增加渠道商导入接口
    2、rpa生成工单的工单编号改成自增序号，避免工单编号重复
    3、历史工单多产品记录重复问题修复
    4、增加号段查询拓展接口
    5、添加跟进记录兼容工单编号
    6、修复工单无法查询到挂起工单情况
    7、试营销投诉处理群联系人信息添加批量删除、导出
    8、退费专员审核、坐席处理工单的诉求内容改为非必填(表单)
    9、前置客服svc:80015改为9099,与客服页面同步
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240424-2
修改日期: 2024-04-24
修改人:  张杨宋
说明:
    1、旧系统历史工单详情优化
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240424-1
修改日期: 2024-04-24
修改人:  许正佳
说明:
    1、前置省份增加同省份前置工单数据
    	申诉来源(SOURCE)字段为11 - 工单（省份工单）
		省份(PROVINCE)字段与当前登录用户所在省份一致
		来源单号(SOURCE_NUMBER) 为空 或者	省份(PROVINCE)字段不等于 广东、福建
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240423-5
修改日期: 2024-04-23
修改人:  张杨宋
说明:
    1、前置省份追诉按钮权限（录单、暂存、归档不显示）
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240423-4
修改日期: 2024-04-23
修改人:  郭远平、许正佳
说明:
	1、增加省份历史工单
	2、工单退费流转异常，流转至渠道退费等待问题修复
	3、省份录单跟进记录展示RPA机器人问题修复
	4、前置客服建单出现4008来源工单信息	更新录单表单 提交按钮（点击后）和脚本配置（渲染后）的js内容
    5、扩展HCodeExUtil工具类，兼容固话地区获取
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240423-1
修改日期: 2024-04-23
修改人:  许正佳
说明:
	1、退费参数调整，增加创建人姓名、退费原因
	2、增加退费失败接口
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240422-4
修改日期: 2024-04-22
修改人:  许正佳
说明:
	1、修复无需回复时RPA处理状态，工单状态
	2、退费请求人更改为创建人和请求退费操作人
---------------------------------------------------------------------------------------
模块名： cx-mix-imusic.war
版本号:  1.0#20240422-3
修改日期: 2024-04-22
修改人:
说明:
	1、初版，正式上线
---------------------------------------------------------------------------------------
