article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
nav,
section {
	display: block;
}

audio,
canvas,
video {
	display: inline-block;
	*display: inline;
	*zoom: 1;
}

audio:not([controls]) {
	display: none;
}

html {
	font-size: 100%;
	-webkit-text-size-adjust: 100%;
	-ms-text-size-adjust: 100%;
}

a:hover,
a:active {
	outline: 0;
}

sub,
sup {
	position: relative;
	font-size: 75%;
	line-height: 0;
	vertical-align: baseline;
}

sup {
	top: -0.5em;
}

sub {
	bottom: -0.25em;
}

img {
	max-width: 100%;
	width: auto\9;
	height: auto;
	vertical-align: middle;
	border: 0;
	-ms-interpolation-mode: bicubic;
}

#map_canvas img,
.google-maps img {
	max-width: none;
}

button,
input,
select,
textarea {
	margin: 0;
	font-size: 100%;
	vertical-align: middle;
}

button,
input {
	*overflow: visible;
	line-height: normal;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
	padding: 0;
	border: 0;
}

button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
	-webkit-appearance: button;
	cursor: pointer;
}

label,
select,
button,
input[type="button"],
input[type="reset"],
input[type="submit"],
input[type="radio"],
input[type="checkbox"] {
	cursor: pointer;
}

input[type="search"] {
	-webkit-appearance: textfield;
}

input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button {
	-webkit-appearance: none;
}

textarea {
	overflow: auto;
	vertical-align: top;
}

@media print {
	* {
		text-shadow: none !important;
		color: #000 !important;
		background: transparent !important;
		box-shadow: none !important;
	}

	a,
	a:visited {
		text-decoration: underline;
	}

	a[href]:after {
		content: " ("attr(href) ")";
	}

	abbr[title]:after {
		content: " ("attr(title) ")";
	}

	.ir a:after,
	a[href^="javascript:"]:after,
	a[href^="#"]:after {
		content: "";
	}

	pre,
	blockquote {
		border: 1px solid #999;
		page-break-inside: avoid;
	}

	thead {
		display: table-header-group;
	}

	tr,
	img {
		page-break-inside: avoid;
	}

	img {
		max-width: 100% !important;
	}

	@page {
		margin: 0.5cm;
	}

	p,
	h2,
	h3 {
		orphans: 3;
		widows: 3;
	}

	h2,
	h3 {
		page-break-after: avoid;
	}
}

.clear {
	clear: both;
}

.clear:after {
	content: "\0020";
	visibility: hidden;
	display: block;
	height: 0;
	clear: both;
}

.clearfix:before,
.clearfix:after {
	content: " ";
	display: table;
}

.clearfix:after {
	clear: both;
}

.abs-center {
	position: absolute;
	top: 50%;
	left: 50%;
	-ms-transform: translate(-50%, -50%);
	/* IE 9 */
	-webkit-transform: translate(-50%, -50%);
	/* Safari and Chrome */
	-o-transform: translate(-50%, -50%);
	/* Opera */
	-moz-transform: translate(-50%, -50%);
	/* Firefox */
	transform: translate(-50%, -50%);
}

.flex {
	width: 100%;
	height: 100%;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
	/*justify-content: space-between;*/
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}

.flex-row {
	width: 100%;
	height: 100%;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-ms-flex-direction: row;
	flex-direction: row;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}

.flex-item {
	-webkit-box-flex: 1;
	-ms-flex: 1;
	flex: 1;
	overflow: auto;
}

.monitor-table {
	width: 100%;
	border: 1px solid #66aaf1;
	border-radius: 4px;
	overflow: hidden;
	border-spacing: 0;
	font-size: 14px;
}

.monitor-table th,
.monitor-table td {
	padding: 7px 10px;
	line-height: 20px;
	border: 0;
	border-right: 1px solid #1158b9;
}

.monitor-table th a,
.monitor-table td a {
	color: #2aaffc;
}

.monitor-table th:nth-last-child(1),
.monitor-table td:nth-last-child(1) {
	border-right: 0;
}

.monitor-table thead,
.monitor-table .th {
	background: rgba(14, 84, 171, 0.16);
	color: #9fc9ff;
}

.monitor-table tbody td {
	border-bottom: 1px solid #1158b9;
	border-left: 0;
}

.monitor-table[data-layout="fixed"] {
	table-layout: fixed;
}

.monitor-table[data-align="left"] td,
.monitor-table[data-align="left"] th {
	text-align: left !important;
}

.monitor-table[data-type="full"] {
	height: 100%;
}

.monitor-table[data-type="full"] th {
	line-height: 20px;
	padding: 5px 10px;
	text-align: center;
	border-bottom: 1px solid #1158b9;
	height: 30px;
}

.monitor-table[data-type="full"] td {
	position: relative;
}

.monitor-table[data-type="full"] .th-mix {
	position: relative;
}

.monitor-table[data-type="full"] .th-mix .th1 {
	padding: 5px;
	position: absolute;
	right: 0;
	top: 0;
	display: inline-block;
	text-align: right;
}

.monitor-table[data-type="full"] .th-mix .th2 {
	padding: 5px;
	position: absolute;
	left: 0;
	bottom: 0;
	display: inline-block;
	text-align: left;
}

.monitor-table[data-border="none"] {
	border: 0;
}

.monitor-table[data-border="none"] th,
.monitor-table[data-border="none"] td {
	border: 0;
}

.monitor-table[data-zebra] thead th,
.monitor-table[data-zebra] tbody tr:nth-child(even) {
	background-color: rgba(11, 41, 107, 0.2);
}

.monitor-table[data-zebra] tbody tr:nth-child(odd) {
	/* background-color: rgba(11, 41, 107, 0.9); */
	background-color: rgba(0, 0, 0, 0.15);
}

html,
body {
	margin: 0;
	padding: 0;
	width: 100%;
	height: 100%;
	position: relative;
	overflow: hidden;
	font-size: 14px;
	color: #c3c7d9;
}

.monitor-page {
	background: url(../images/切图/bg_img.png) no-repeat center;
	background-size: cover;
	width: 100%;
	height: 100%;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
	/*justify-content: space-between;*/
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}

.monitor-page-header {
	background: url(../images/切图/nav_bg.png) no-repeat center;
	background-size: cover;
	height: 50px;
}

.monitor-page-header .title {
	text-align: center;
	line-height: 50px;
	color: #fff;
	font-size: 26px;
	letter-spacing: 2px;
}

.monitor-page-content {
	box-sizing: border-box;
	padding: 10px;
	flex: 1;
}

.monitor-page-rows>.flex-item {
	margin-bottom: 10px;
}

.monitor-title {
	padding: 10px 20px;
	position: relative;
	color: #fff;
}

.monitor-title:after {
	content: '';
	position: absolute;
	background: #4799ff;
	width: 3px;
	height: 16px;
	top: 50%;
	left: 5px;
	margin-top: -8px;
}

.monitor-box {
	border-width: 2px;
	border-color: #1a5bca;
	border-style: solid;
	background-color: rgba(8, 39, 106, 0.502);
	box-shadow: inset 0px 0px 20px 0px rgba(26, 91, 202, 0.5);
	/* border-radius: 10px; */
	overflow: hidden;
	width: 100%;
	height: 100%;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
	/*justify-content: space-between;*/
	-webkit-box-sizing: border-box;
	box-sizing: border-box;


	background: linear-gradient(#2EA7FA, #2EA7FA) left top,
		linear-gradient(#2EA7FA, #2EA7FA) left top,
		linear-gradient(#2EA7FA, #2EA7FA) right top,
		linear-gradient(#2EA7FA, #2EA7FA) right top,
		linear-gradient(#2EA7FA, #2EA7FA) left bottom,
		linear-gradient(#2EA7FA, #2EA7FA) left bottom,
		linear-gradient(#2EA7FA, #2EA7FA) right bottom,
		linear-gradient(#2EA7FA, #2EA7FA) right bottom;
	background-repeat: no-repeat;
	background-size: 1px 24px, 24px 1px;
}

.monitor-box .header {
	border-bottom: 1px #1a5bca solid;
	background-color: rgba(0, 0, 0, 0.402);
	opacity: 0.878;
	box-shadow: inset 0px 0px 10px 0px rgba(26, 91, 202, 0.4);
	line-height: 20px;
	padding: 10px 10px;
}

.monitor-box .content {
	-webkit-box-flex: 1;
	-ms-flex: 1;
	flex: 1;
	overflow: auto;
	position: relative;
	background-color: rgba(0, 0, 0, 0.402);
}

.box-m {
	margin: 0 5px;
	box-sizing: border-box;
}

/* 圆形进度条 */
.bar-box {
	position: relative;
	top: 50%;
	transform: translateY(-50%);
	text-align: center;
	display: inline-block;
	min-width: 300px;
	max-width: 450px;
	margin: 0 auto;
}

.bar-box .bar-item {
	display: inline-block;
	text-align: center;
	margin: 10px 10px;
}

.bar-box .bar-item .bar-img {
	width: 108px;
	height: 108px;
	position: relative;
	background: url(../images/box-bg.png) no-repeat center;
	background-size: 100% 100%;
	margin-bottom: 10px;
}

.bar-box .bar-item .bar-img .bar-count {
	font-size: 18px;
	color: yellow;
	position: absolute;
	top: 50%;
	left: 50%;
	-ms-transform: translate(-50%, -50%);
	/* IE 9 */
	-webkit-transform: translate(-50%, -50%);
	/* Safari and Chrome */
	-o-transform: translate(-50%, -50%);
	/* Opera */
	-moz-transform: translate(-50%, -50%);
	/* Firefox */
	transform: translate(-50%, -50%);
}

.bar-box .bar-item[data-type="mini"] .bar-img {
	width: 81px;
	height: 81px;
}

.bar-box .bar-item[data-type="mini"] .bar-img .bar-count {
	font-size: 16px;
}

.bar-box.v-t {
	transform: none;
	top: inherit;
}

/* 横向进度条 */
.line-bar-box {
	padding-right: 20px;
}

.line-bar-box-down {
	padding-top: 30px;
}

.line-bar-box img {
	margin-right: 10px;
}

.line-bar-box .count {
	font-size: 13px;
}

.line-bar-box .line {
	margin-top: 10px;
	margin-bottom: 10px;
	position: relative;
	display: block;
	height: 26px;
	border-radius: 3px;
	overflow: hidden;
	background-color: rgba(26, 91, 202, 0.3);
}

.line-bar-box .line:after,
.line-bar-box .line .line-persent {
	content: '';
	height: 100%;
	width: 0;
	/* background-color: #325cf3; */
	position: absolute;
}

.line-bar-box[data-type="mini"] .line {
	margin-top: 5px;
	margin-bottom: 5px;
	height: 10px;
}

.flex-vac {
	justify-content: center;
}

.s-line-box {
	padding: 10px;
	padding-left: 30px;
}

.s-line-box .line-bar-box {
	display: inline-block;
	vertical-align: top;
	width: 48%;
	box-sizing: border-box;
}

/* 总体话务 */
.callinfo-box {
	text-align: center;
}

.callinfo-box .b-c {
	background: url(../images/box-c.png) no-repeat center;
	background-size: 100% 100%;
	width: 100px;
	height: 100px;
	position: relative;
	position: absolute;
	top: 50%;
	left: 50%;
	-ms-transform: translate(-50%, -50%);
	/* IE 9 */
	-webkit-transform: translate(-50%, -50%);
	/* Safari and Chrome */
	-o-transform: translate(-50%, -50%);
	/* Opera */
	-moz-transform: translate(-50%, -50%);
	/* Firefox */
	transform: translate(-50%, -50%);
}

.callinfo-box .bb {
	width: 200px;
	height: 110px;
	display: inline-block;
	box-sizing: border-box;
	margin: 3px;
	position: relative;
}

.callinfo-box .count {
	position: absolute;
	top: 50%;
	left: 50%;
	-ms-transform: translate(-50%, -50%);
	/* IE 9 */
	-webkit-transform: translate(-50%, -50%);
	/* Safari and Chrome */
	-o-transform: translate(-50%, -50%);
	/* Opera */
	-moz-transform: translate(-50%, -50%);
	/* Firefox */
	transform: translate(-50%, -50%);
	color: #fff;
}

.callinfo-box .count span {
	font-size: 16px;
	display: block;
}

.callinfo-box .count label {
	font-size: 13px;
	letter-spacing: 2px;
}

.callinfo-box .b-1 {
	background: url(../images/box-1.png) no-repeat center;
	background-size: 100% 100%;
}

.callinfo-box .b-2 {
	background: url(../images/box-2.png) no-repeat center;
	background-size: 100% 100%;
}

.callinfo-box .b-3 {
	background: url(../images/box-3.png) no-repeat center;
	background-size: 100% 100%;
}

.callinfo-box .b-4 {
	background: url(../images/box-4.png) no-repeat center;
	background-size: 100% 100%;
}

.chart-area {
	position: absolute;
	top: 5px;
	right: 5px;
	bottom: 5px;
	left: 5px;
}

*::-webkit-scrollbar {
	/*滚动条整体样式*/
	width: 4px;
	/*高宽分别对应横竖滚动条的尺寸*/
	height: 4px;
}

*::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/
	border-radius: 5px;
	-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	background: rgba(0, 0, 0, 0.2);
}

*::-webkit-scrollbar-track {
	/*滚动条里面轨道*/
	-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	border-radius: 0;
	background: rgba(0, 0, 0, 0.1);
}


/* 样式修改，使对比度更鲜明 */
.titleTraffic {
	color: #FDF128 !important;
	font-size: 24px !important;
}

.title {
	color: #FDF128;
	font-size: 24px !important;
}

.line-bar-box .count {
	color: #00FFE3;
}

.bar-text {
	color: #ffffff;
}

.bar-count {
	color: #FDF128 !important;
}

.tabletitle {
	color: yellow;
}

.header {
	color: #ffffff;
}

tbody {
	color: #ffffff;
}

.dimgray {
	color: #ffffff !important;
}

.line-persent {
	display: inline-block;
	background-image: linear-gradient(to right, #FA4B28, #ECAA19);
	color: white;
	font-weight: bold;
	padding: 0px;
	text-align: right;
	border-radius: 5px;
}

/* 文字禁止选择 */
body {
	-moz-user-select: none;
	/*火狐*/
	-webkit-user-select: none;
	/*webkit浏览器*/
	-ms-user-select: none;
	/*IE10*/
	-khtml-user-select: none;
	/*早期浏览器*/
	user-select: none;
}

.sameDayTitle {
	font-size: 13px;
	color: #fff;
}

.levelCenter {
	margin: 0 auto;
}



.imgHead {
	padding: 10px 40px;
}

.imgHead img {
	width: 20px;
}

.imgHead .imgItem {
	float: left;
	margin-right: 60px;
	text-align: center;
}

.clearfix:after {
	content: '';
	height: 0;
	line-height: 0;
	display: block;
	visibility: hidden;
	clear: both;
}

.msgBody {
	overflow-y: auto;
	height: 800px;
}

.msgItem {
	margin: 10px;
	margin-right: 0;
	float: left;
	width: 18%;
}

.imgClass {
	height: 100px;
	width: 100% !important;
	display: inline-block;
	background-size: 100% 98% !important;
}

.img_img {
	float: left;
	width: 30%;
	height: 100%;
	text-align: center;
	display: flex;
	justify-content: center;
	align-items: center;
}

.img_msg {
	float: left;
	width: 65%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
}

.img_name {
	margin-bottom: 0px;
	text-align: left;
	width: 100%;
}

.img_state {
	margin-bottom: 15px;
	text-align: left;
	width: 100%;
}

.img_span {
	margin-right: 0px;
	width: 70px;
	display: inline-block;
}

.ten {
	margin-bottom: 60px;
}




.parallelogram {
	width: 60px;
	height: 30px;
	padding: 0px 5px;
	display: flex;
	justify-content: center;
	align-items: center;
	background: #58a;
	text-decoration: none;
	color: #5E6A9E;
	transform: skewX(-30deg);
	margin-right: 20px;
	background-color: #0D1021;
}

.activeMonth{
	background-color: #1660FD;
	color: #fff;
}
















