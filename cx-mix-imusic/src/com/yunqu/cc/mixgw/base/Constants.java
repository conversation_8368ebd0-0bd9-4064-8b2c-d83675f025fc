package com.yunqu.cc.mixgw.base;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.context.AppContext;

import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.ConfigUtil;
import com.yq.busi.common.util.SystemParamUtil;

/**
 * 常量
 * <AUTHOR>
 *
 */
public class Constants {

	public final static String DS_WIRTE_NAME_ONE = "yc-wirte-ds-1";     //默认数据源名称(写)

	public final static String DS_WIRTE_NAME_TWO = "yc-wirte-ds-2";     //默认数据源名称(写)

	public final static String DS_READ_NAME= "yc-read-ds";     //默认数据源名称(读)

	public final static String APP_NAME = "cx-mix-imusic";     //应用

	/** 企业呼叫中心订购ID */
	public final static String BUSI_ID_007 = "007";

	private static AppContext context = AppContext.getContext("yc-api");

	private static AppContext baseContext = AppContext.getContext("cc-base");

	private static AppContext cxApiContext = AppContext.getContext("cx-mix-api");

	/**
	 * 工单流程服务
	 */
	public static final String IM_ORDER_FLOW_SERVICE = "IM_ORDER_FLOW_SERVICE";
	/**
	 * 智能质检拓展服务
	 */
	public static final String ZN_QUALITY_EX_AYY = "ZN_QUALITY_EX_AYY";

	/**
	 * RPA相关服务
	 */
	public static final String IM_RPA_SERVICE = "IM_RPA_SERVICE";

	/**
	 * 意图：退费
	 */
	public static final String RPA_ACTION_REFUND = "01";
	/**
	 * 意图：退订
	 */
	public static final String RPA_ACTION_CANCEL = "02";
	/**
	 * 意图：屏蔽
	 */
	public static final String RPA_ACTION_SCREEN = "03";
	/**
	 * 意图：查询
	 */
	public static final String RPA_ACTION_SEARCH = "04";

	/**
	 * 意图：所有
	 */
	public  static final String RPA_ACTION_ALL = "05";

	/**
	 * 意图：查询订购渠道
	 */
	public  static final String RPA_ACTION_SEARCH_CHANNEL = "06";

	/**
	 * 退费请求状态: 未退费
	 */
	public static final String REFUND_REQ_STATUS_00 = "00";
	/**
	 * 退费请求状态: 待退费
	 */
	public static final String REFUND_REQ_STATUS_01 = "01";
	/**
	 * 退费请求状态: 退费成功
	 */
	public static final String REFUND_REQ_STATUS_02 = "02";
	/**
	 * 退费请求状态: 退费失败
	 */
	public static final String REFUND_REQ_STATUS_03 = "03";
	/**
	 * 退费请求状态: 人工失败
	 */
	public static final String REFUND_REQ_STATUS_04 = "04";

	/**
	 * 获取退费请求最大次数
	 * @return
	 */
	public static int getRefundReqMaxNum(){
		return CommonUtil.parseInt(ConfigUtil.getString(APP_NAME, "REFUND_REQ_MAX_NUM", "3"));
	}

	public static final String OP_ACC_RPA="rpa";
	/**
	 * 统计库名称
	 */
	public static String getStatSchema(){
		return context.getProperty("STAT_DB","stat");
	}

	public static String getPassword(){
		return baseContext.getProperty("SERVICE_INTERFACE_PWD", "yq_85521717");
	}

	/**
	 * 获取是否开启定时任务
	 */
	public static String getIsStartTask() {
		return AppContext.getContext(APP_NAME).getProperty("IS_START_TASK", "N");
	}

	/**
	 * 工单提参
	 */
	public static final String EXTRACT_INFO_SINGLE = "/imusic/extractInfoSingle";

	/**
	 * NLP服务地址
	 * @return
	 */
	public static String getNlpServerAddr(){
		return AppContext.getContext(Constants.APP_NAME).getProperty("NLP_SERVER_ADDR", "http://172.16.85.119:7720");
	}
	/**
	 * NLP意图识别服务地址
	 * @return
	 */
	public static String getNlpExtractInfoAddr(){
		return AppContext.getContext(Constants.APP_NAME).getProperty("NLP_EXTRACT_INFO_ADDR", "http://172.16.85.119:7720");
	}

	/**
	 * 获取NLP小结地址
	 * @return
	 */
	public static String getNlpSummaryAddr() {
		return AppContext.getContext(Constants.APP_NAME).getProperty("NLP_SUMMARY_ADDR", "http://172.16.85.119:7720");
	}

	/**
	 * 获取NLPID
	 * @return
	 */
	public static String getNlpAppId() {
		return AppContext.getContext(Constants.APP_NAME).getProperty("NLP_APP_ID", "testMsg");
	}

	public static String getAyyHandleReplyNode() {
		return AppContext.getContext(APP_NAME).getProperty("AYY_HANDLE_REPLY_NODE", "");
	}
	/**
	 * 获取NLP相似度匹配URL
	 * @return
	 */
	public static String getNlpTextMatchingAddr() {
		return AppContext.getContext(Constants.APP_NAME).getProperty("NLP_TEXT_MATCHING_ADDR", "");
	}

	/**
	 * 短信验证模板CODE
	 * @return
	 */
	public static String getSmsAuthCodeTemplate() {
		return AppContext.getContext(APP_NAME).getProperty("SMS_AUTH_CODE_TEMPLATE", "");
	}

	/**
	 * 同步的客户资料表ID
	 */
	public static String getCustTableId() {
		return ConfigUtil.getString("cx-mix-dp", "CUST_TABLE_ID");
	}

	/**
	 * 获取entId
	 * @return
	 */
	public static String getEntId() {
		return cxApiContext.getProperty("ENT_ID","");
	}
	/**
	 * 获取BusiOrderId
	 * @return
	 */
	public static String getBusiOrderId() {
		return cxApiContext.getProperty("BUSI_ORDER_ID","");
	}

	public static String getAyyFormarkAuth() {
		return AppContext.getContext(Constants.APP_NAME).getProperty("AYY_FORMARK_AUTH", "");
	}


	public static String getShuttleOprKey(String str) {
		switch (str) {
			case "退订退费" :
				return "1";
			case "退订" :
				return "2";
			case "退费" :
				return "3";
			case "前期已退订" :
				return "4";
			case "前期已退费":
				return "5";
			case "前期已退订退费":
				return "6";
			case "屏蔽":
				return "7";
			default:
				return "";
		}
	}

	public final static String REFUND_CYCLE_CACHE_KEY = "cx_refund_cycle" + Constants.getEntId() + "_" + Constants.getBusiOrderId();



	/** 工单处理跟进记录 */
	public final static String FOLLOW_TYPE_1 = "1";
	/** 工单诉求跟进记录 */
	public final static String FOLLOW_TYPE_90 = "90";

	// 手动客诉内容
	public final static String FOLLOW_TYPE_33 = "33";
	// 手动处理内容
	public final static String FOLLOW_TYPE_66 = "66";


	// 添加分派内容
	public final static String FOLLOW_TYPE_77 = "77";
	/** 模型业务类型 01 - 工单分类 */
	public final static String MODEL_BUSI_TYPE_01 = "1";
	/** 模型业务类型 02 - 意图分类 */
	public final static String MODEL_BUSI_TYPE_02 = "2";
	/** 模型业务类型 03 - 产品分类 */
	public final static String MODEL_BUSI_TYPE_03 = "3";
	/** 模型业务类型 04 - RPA处理关键字 */
	public final static String MODEL_BUSI_TYPE_04 = "4";

	/** 超时催办 */
	public final static String ORDER_HANDLE_SUPER_TIME_TEMALATE = "1091549356";
	/** 未超时催办 */
	public final static String ORDER_HANDLE_NO_TIME_TEMALATE = "1091549355";


	// 智能分配状态: 01-待分配
	public static final String DISTRIBUTER_STATUS_WAIT = "01";
	// 智能分配状态: 02-已分配
	public static final String DISTRIBUTER_STATUS_ASSIGNED = "02";
	// 智能分配状态: 03-已回收
	public static final String DISTRIBUTER_STATUS_RECYCLED = "03";
	// 智能分配状态: 04-分配异常
	public static final String DISTRIBUTER_STATUS_EXCEPTION = "04";
	// 智能分配状态: 05-已确认
	public static final String DISTRIBUTER_STATUS_ENTER = "05";

	// 锁定状态: 01-待锁定
	public static final String LOCK_STATUS_01 = "01";
	// 锁定状态: 02-已锁定
	public static final String LOCK_STATUS_02 = "02";

	// 对象类型：处理人
	public static final String OBJ_TYPE_ASSIGNEE = "assignee";
	// 对象类型：备选人
	public static final String OBJ_TYPE_CANDIDATEUSERS = "candidateUsers";
	// 对象类型：备选部门
	public static final String OBJ_TYPE_CANDIDATEDEPTS = "candidateDepts";
	// 对象类型：备选工作组
	public static final String OBJ_TYPE_CANDIDATEGROUPS = "candidateGroups";
	// 对象类型：备选角色
	public static final String OBJ_TYPE_CANDIDATEROLES = "candidateRole";

	// 分配类型 01-在线坐席
	public static final String ASSIGN_TYPE_ONLINE = "01";
	// 分配类型  02-所有坐席
	public static final String ASSIGN_TYPE_ALL = "02";

	// 分配模式 01-轮询分配
	public static final String ASSIGN_MODE_POOLING = "01";
	// 分配模式 02-平均分配
	public static final String ASSIGN_MODE_AVG = "02";
	// 分配模式 99-自定义服务
	public static final String ASSIGN_MODE_CUSTOM = "99";

	// 签收缓存key前缀
	public static final String CACHE_KEY_ASSIGN = "CLAIMING_TASK_ID_";



	// 是否开启自动处理配置
	public static final String AUTO_OPEN_SOURCES = "1";
	public static final String AUTO_OPEN_GROUPS = "2";

	/**
	 * 工单自动分配最大待办数
	 * @return
	 */
	public static int getOrderAutoAssignNums(){
		String orderAutoAssignNums = ConfigUtil.getString(APP_NAME, "ORDER_AUTO_ASSIGN_NUMS", "0");
		return CommonUtil.parseInt(orderAutoAssignNums);
	}

	/**
	 * 意图中文描述
	 * @param action
	 * @return
	 */
	public static String getActionMsg(String action) {
		if(RPA_ACTION_REFUND.equals(action)) {
			return "退费";
		} else if(RPA_ACTION_CANCEL.equals(action)) {
			return "退订";
		} else if(RPA_ACTION_SCREEN.equals(action)) {
			return "屏蔽";
		}
		return "未识别";
	}

	/**
	 * 退费节点列表
	 */
	public static String getRefundNodes() {
		return ConfigUtil.getString(APP_NAME, "REFUND_NODES");
	}

	/**
	 * 坐席处理节点列表
	 */
	public static String getAgentDealNodes() {
		return ConfigUtil.getString(APP_NAME, "AGENT_DEAL_NODES");
	}

	public static String getAgentDealAddFollow() {
		return ConfigUtil.getString(APP_NAME, "AGENT_DEAL_ADD_FOLLOW");
	}

	/**
	 * 自动处理开关
	 * @return
	 */
	public static boolean getAutoHandleSwitch() {
		return "Y".equals(ConfigUtil.getString(APP_NAME, "AUTO_HANDLE_SWITCH", "Y"));
	}

	/**
	 * 自动回复消息
	 */
	public static String getAutoReplyBaseMsg() {
		return ConfigUtil.getString(APP_NAME, "AUTO_REPLY_BASE");
	}

	public static String getAyyNofunctionProductId() {
		return ConfigUtil.getString(APP_NAME, "AYY_NOPRODUCT_IDQQQQ",
				"7360110000100955,7360110000100956,7360110000100957,7360110000100958,7360110000100911,7360110000100912,7360110000100959,7360110000100933,7360110000100934,7360110000100960,7360110000100961,7360110000100962");
	}

	/**
	 * 省份权限id
	 * @return
	 */
	public static String getUserResId() {
		return ConfigUtil.getString(APP_NAME, "USER_RES_ID");
	}

	/** 越级工单关键字 (迁移至模型标注) */
	public static String getSkipOrderKeys() {
		return ConfigUtil.getString(APP_NAME, "SKIP_ORDER_KEYS");
	}

	/**
	 * 舆情工单关键字(迁移至模型标注)
	 * @return
	 */
	public static String getOpinionOrderKeys() {
		return ConfigUtil.getString(APP_NAME, "OPINION_ORDER_KEYS");
	}

	/** 工信部工单关键字(迁移至模型标注) */
	public static String getMiitOrderKeys() {
		return ConfigUtil.getString(APP_NAME, "MIIT_ORDER_KEYS");
	}

	public static String getDefaultWorkId() {
		return ConfigUtil.getString(APP_NAME, "DEFAULT_WORK_ID");
	}

	/** 无需回复关键字(迁移至模型标注) */
	public static String getNoNeedReplyKeycode() {
		return ConfigUtil.getString(APP_NAME, "NO_NEED_REPLY_KEYCODE");
	}

	public static String getOrderNoSuffixCount(){
		return AppContext.getContext("cc-formmarking").getProperty("ORDER_NO_SUFFIX_COUNT", "");
	}

	/** 理赔方-渠道退赔 */
	public static final String CLAIMANT_01 = "01";
	/** 理赔方-客服退赔 */
	public static final String CLAIMANT_02 = "02";

	/** 历史工单FTP服务地址 */
	public static String getFtpServer() {
		return ConfigUtil.getString(APP_NAME, "AYY_ORDER_FTP_SERVER");
	}

	/** 历史工单FTP账号 */
	public static String getFtpServerAcc() {
		return ConfigUtil.getString(APP_NAME, "AYY_ORDER_FTP_SERVER_ACC");
	}

	/** 历史工单FTP密码 */
	public static String getFtpServerPwd() {
		return ConfigUtil.getString(APP_NAME, "AYY_ORDER_FTP_SERVER_PWD");
	}

	public static boolean getRunJob() {
		return "Y".equals(baseContext.getProperty("RUN_JOB", "N"));
	}

	public static String getOrderWorkGroup() {
		return ConfigUtil.getString(APP_NAME, "AYY_WOEKGROUP_SEND_CONFIG");
	}
	/** 工单自动回收的工作组 */
	public static String getOrderAutoRecoveryWorkgroups() {
		return ConfigUtil.getString(APP_NAME, "ORDER_AUTO_RECOVERY_WORKGROUPS", "W(初级客服),W(中级客服),W(高级客服)");
	}

	/** 工单自动分派的工作组 */
	public static String getOrderAutoAssignWorkgroups() {
		return ConfigUtil.getString(APP_NAME, "ORDER_AUTO_ASSIGN_WORKGROUPS", "初级客服,中级客服,高级客服");
	}

	public static String getAyyComplaintnEOtype() {
		return ConfigUtil.getString(APP_NAME, "AYY_COMPLAINTN_EO_TYPE", "USER_CREATE_COMPLAINT,USER_CONTINUE_COMPLAINT");
	}


	public static String getAyyOrderSuperTime() {
		return ConfigUtil.getString(APP_NAME, "AYY_ORDER_SUPER_TIME", "");
	}

	public static String getAyyOrderTimeoutNoticeWorkgroup() {
		return ConfigUtil.getString(APP_NAME, "AYY_ORDER_TIMEOUT_NOTICE_WORKGROUP", "");
	}

	public static String getAyyOrderTimeoutTime() {
		return ConfigUtil.getString(APP_NAME, "AYY_ORDER_TIMEOUT_TIME", "");
	}

	public static String getAyyOrderMinistryWorkgroup() {
		return ConfigUtil.getString(APP_NAME, "AYY_ORDER_MINISTRY_WORKGROUP", "");
	}

	public static String getAyyOrderReminderWorkgroup() {
		return ConfigUtil.getString(APP_NAME, "AYY_ORDER_REMINDER_WORKGROUP", "");
	}

	public static String getAyyOrderMinistryWorkgroupOpen() {
		return AppContext.getContext(APP_NAME).getProperty("AYY_ORDER_MINISTRY_WORKGROUP_OPEN", "N");
	}

	public static String getWxContentMids() {
		return AppContext.getContext(APP_NAME).getProperty("WX_CONTENT_MIDS", "1220973601");
	}


	public static String getAyyOrderNotLoginTime() {
		return AppContext.getContext(APP_NAME).getProperty("AYY_ORDER_NOTLOGIN_TIME", "N");
	}
	public static boolean  getShowPreview(String schema, String entId, String busiOrderId){
		String status = SystemParamUtil.getEntParam(schema,entId,busiOrderId, "cc-base", "SHOW_PREVIEW");
		//开启
		if("1".equals(status)) {
			return true;
		}
		return false;
	}
	public static String getLoginDownLoadAttr(){
		return AppContext.getContext("cc-base").getProperty("LOGIN_DOWNLOAD_ATTR", "Y");
	}
	public static String getServerWhiteHost(){
		return AppContext.getContext("cc-base").getProperty("SERVER_WHITE_HOST", "");
	}
	public static String getVoiceBucket(){
		return AppContext.getContext("cc-base").getProperty("VOICE_BUCKET", "");
	}
	public static long getRequestTimestamp(){
		return ConfigUtil.getInt("cc-base", "REQUSET_TIMESTAMP")*1000;
	}
	public static boolean isOutinfEnable3DS() {
		String OUTINF_ENABLE_3DS = AppContext.getContext("cc-base").getProperty("OUTINF_ENABLE_3DS", "2");
		return "1".equals(OUTINF_ENABLE_3DS);
	}
	public static String getOutinf3DSKey() {
		String outinf3DSKey = AppContext.getContext("cc-base").getProperty("OUTINF_3DS_KEY", "PAMP_ENCODE_KEY_12345678");
		return outinf3DSKey;
	}
	public static boolean getDelUploadOldFile() {
		String delUploadOldFile = AppContext.getContext("cc-base").getProperty("DEL_UPLOAD_OLD_FILE", "N");
		return "Y".equalsIgnoreCase(delUploadOldFile);
	}


	public static boolean getAyyOrderUpdateExport() {
		String delUploadOldFile = AppContext.getContext(APP_NAME).getProperty("AYY_ORDER_UPDATE_EXPORT", "N");
		return "Y".equalsIgnoreCase(delUploadOldFile);
	}

	public static int getAyyOrderUpdateFieldTime() {
		String delUploadOldFile = AppContext.getContext(APP_NAME).getProperty("AYY_ORDER_UPDATE_FIELD_TIME", "7");
		return Integer.parseInt(delUploadOldFile);
	}
	public static String getAyyOrderRpaNoticePhone() {
		return AppContext.getContext(APP_NAME).getProperty("AYY_ORDER_RPA_NOTICE_PHONE", "");
	}

	public static String getAyyOrderMinistryExportWorkgroup() {
		return AppContext.getContext(APP_NAME).getProperty("AYY_ORDER_MINISTRY_EXPORT_WORKGROUP", "");
	}
	/** 抓取消息结果类型 01-录单 */
	public static final String GRAB_MSG_RESULT_TYPE_01 = "01";
	/** 抓取消息结果类型 02-追诉 */
	public static final String GRAB_MSG_RESULT_TYPE_02 = "02";
	/** 抓取消息结果类型 03-批量建单 */
	public static final String GRAB_MSG_RESULT_TYPE_03 = "03";
	/** 抓取消息结果类型 04-催单 */
	public static final String GRAB_MSG_RESULT_TYPE_04 = "04";

	/** 异常通知手机号 */
	public static String getErrorNoticePhone() {
		return ConfigUtil.getString(APP_NAME, "ERROR_NOTICE_PHONE");
	}

	/** 异常通知邮箱 */
	public static String getErrorNoticeEmail() {
		return ConfigUtil.getString(APP_NAME, "ERROR_NOTICE_EMAIL");
	}

	/** 退费等待节点ID */
	public static String getRefundWaitNodeId() {
		return AppContext.getContext(APP_NAME).getProperty("REFUND_WAIT_NODE_ID", "RECEIVETASK_ncmgs9wnh");
	}

	public static String getAutoHandleNodeId() {
		return AppContext.getContext(APP_NAME).getProperty("AUTO_HANDLE_NODE_ID", "RECEIVETASK_80kx403twn,RECEIVETASK_tkcozd4g86");
	}

	/** 渠道转发默认处理时限(min) */
	public static String getChannelForwordProcTimes() {
		return AppContext.getContext(APP_NAME).getProperty("CHANNEL_FORWORD_PROC_TIMES", "120");
	}

	public static String getAyyContactKeyWords() {
		return AppContext.getContext(APP_NAME).getProperty("AYY_CONTACT_KEY_WORDS", "联系号码,请联系,联系电话,联系方式,拨打");
	}

	public static String getAyyRefundKeyWords() {
		return AppContext.getContext(APP_NAME).getProperty("AYY_REFUND_KEY_WORDS", "退至,退到,退费至,退费到,退费号码");
	}

	public static String getAyySsoAccDept() {
		return AppContext.getContext(APP_NAME).getProperty("AYY_SSO_ACC_DEPT", "");
	}

	public static String getOrderVersion() {
		return AppContext.getContext(APP_NAME).getProperty("ORDER_VERSION", "N");
	}
	public static String getAyyCrmWarningConfig() {
		return AppContext.getContext(APP_NAME).getProperty("AYY_CRM_WARNING_CONFIG", "");
	}

	/** 渠道转发流程状态 01 - 已转发 */
	public static final String CHANNEL_FORWARD_PROC_STATUS_01 = "01";
	/** 渠道转发流程状态 02 - 已回复 */
	public static final String CHANNEL_FORWARD_PROC_STATUS_02 = "02";
	/** 渠道转发流程状态 03 - 超时未回复 */
	public static final String CHANNEL_FORWARD_PROC_STATUS_03 = "03";

	/**
	 * 根据工单类型获取产品标签
	 * @param orderType
	 * @return
	 */
	public static String getProductTag(String orderType) {
		String productTags = AppContext.getContext(APP_NAME).getProperty("PRODUCT_TAG_MAP", "");
		if(StringUtils.isBlank(productTags)) {
			return "";
		}

		for(String str : productTags.split(",")) {
			if(str.startsWith(orderType)) {
				return str.split(":")[1];
			}

			if(str.startsWith("00")) {
				return str.split(":")[1];
			}
		}
		return "";

	}

	/** rpa处理关键字 01 - 无需回复关键字 */
	public static final String RPA_HANDLE_KEYCODE_01 = "01";
	/** rpa处理关键字 02 - 转人工关键字 */
	public static final String RPA_HANDLE_KEYCODE_02 = "02";
	/** rpa处理关键字 03 - 越级工单关键字 */
	public static final String RPA_HANDLE_KEYCODE_03 = "03";
	/** rpa处理关键字 04 - 舆情工单关键字 */
	public static final String RPA_HANDLE_KEYCODE_04 = "04";
	/** rpa处理关键字 05 - 工信部关键字 */
	public static final String RPA_HANDLE_KEYCODE_05 = "05";
	/** rpa处理关键字 06 - 产品信息错误反馈 */
	public static final String RPA_HANDLE_KEYCODE_06 = "06";


	/** 处理结果 01 - 处理成功 */
	public static final String DEAL_RESULT_01 = "01";
	/** 处理结果 02 - 处理失败 */
	public static final String DEAL_RESULT_02 = "02";
	/** 处理结果 03 - 渠道转发失败 */
	public static final String DEAL_RESULT_03 = "03";
	/** 处理结果 04 - 渠道转发待回复 */
	public static final String DEAL_RESULT_04 = "04";

	/** 自动回复状态 01-需要回复 */
	public static final String AUTO_REPLY_STATUS_01 = "01";
	/** 自动回复状态 02-无需回复 */
	public static final String AUTO_REPLY_STATUS_02 = "02";


	/** 回复结果 02-回复成功 */
	public static final String REPLY_RESULT_02 = "02";
	/** 回复结果 03-回复失败 */
	public static final String REPLY_RESULT_03 = "03";

	public static final String WARN_TEMPLATE = "1091550017";


	public static final String SHUTTLE_OPR_CLOSE_RES = "closeRingRes"; // 查询销户结果
	public static final String SHUTTLE_OPR_CLOSE = "closeRing"; // 销户
	public static final String SHUTTLE_OPR_CANCEL = "cancelRing"; // 退订退费
	public static final String SHUTTLE_OPR_BLACK = "cancelUnorder"; // 屏蔽

	public static final String SHUTTLE_OPR_ALL = "all";

	/** 工单状态 - 处理中 */
	public static final String ORDER_STATUS_PROCESSING = "01";
			
	/** 工单状态 - 已归档 */
	public static final String ORDER_STATUS_ARCHIVED = "02";

	/** 产品脏数据 */
	public static final String PRODUCT_DIRTY_DATA = "null,NULL,-";

	/** 企微通知账户-客服主管 */
	public static String getQwNoticeUserByZg() {
		return AppContext.getContext(APP_NAME).getProperty("QW_NOTICE_USER_BY_ZG", "");
	}

	/** 企微通知账户-客服经理 */
	public static String getQwNoticeUserByJl() {
		return AppContext.getContext(APP_NAME).getProperty("QW_NOTICE_USER_BY_JL", "");
	}


	/** 企微通知-企业微信ID */
	public static String getQwNoticeCorpId() {
		return AppContext.getContext(APP_NAME).getProperty("QW_NOTICE_CORP_ID", "");
	}


	/** 企微通知-企业AgentId */
	public static String getQwNoticeAgentId() {
		return AppContext.getContext(APP_NAME).getProperty("QW_NOTICE_AGENT_ID", "");
	}

	public static String getImusicAgentKeys() {
		return ConfigUtil.getString("cc-rpa", "IMUSIC_AGENT_KEYS");
	}


	/** 上传CRM集团FTP地址 */
	public static String getCrmUplaodFtpIpPort() {
		return AppContext.getContext(APP_NAME).getProperty("CRM_UPLAOD_FTP_IPPORT", "*************:12600");
	}

	/** 上传CRM集团FTP用户名 */
	public static String getCrmUplaodFtpUsername() {
		return AppContext.getContext(APP_NAME).getProperty("CRM_UPLAOD_FTP_USERNAME", "");
	}
	/** 上传CRM集团FTP密码 */
	public static String getCrmUplaodFtpPwd() {
		return AppContext.getContext(APP_NAME).getProperty("CRM_UPLAOD_FTP_PWD", "");
	}

	/** 下载CRM集团FTP地址 */
	public static String getCrmDownlaodFtpIpPort() {
		return AppContext.getContext(APP_NAME).getProperty("CRM_DOWNLAOD_FTP_IPPORT", "");
	}

	/** 下载CRM集团FTP用户名 */
	public static String getCrmDownlaodFtpUsername() {
		return AppContext.getContext(APP_NAME).getProperty("CRM_DOWNLAOD_FTP_USERNAME", "");
	}
	/** 下载CRM集团FTP密码 */
	public static String getCrmDownlaodFtpPwd() {
		return AppContext.getContext(APP_NAME).getProperty("CRM_DOWNLAOD_FTP_PWD", "");
	}

	/** 下载CRM集团FTP文件路径前缀 */
	public static String getCrmDownlaodFilePathPrefix() {
		return AppContext.getContext(APP_NAME).getProperty("CRM_DOWNLAOD_FILE_PATH_PREFIX", "");
	}

	/** CRM发起方编码 */
	public static String getCrmSrcOrgId() {
		return cxApiContext.getProperty("CRM_SRC_ORG_ID", "");
	}

	/** CRM发起方(系统/平台)编码 */
	public static String getCrmSrcSysId() {
		return cxApiContext.getProperty("CRM_SRC_SYS_ID", "");
	}

	/** CRM发起方(系统/平台)签名 */
	public static String getCrmSrcSysSignature() {
		return cxApiContext.getProperty("CRM_SRC_SYS_SIGNATURE", "");
	}

	/** CRM接收方编码 */
	public static String getCrmDstOrgId() {
		return cxApiContext.getProperty("CRM_DST_ORG_ID", "");
	}

	/** CRM接收方(系统/平台)编码 */
	public static String getCrmDstSysId() {
		return cxApiContext.getProperty("CRM_DST_SYS_ID", "");
	}

	/** CRM接收方(系统/平台)URL */
	public static String getCrmUrl() {
		return cxApiContext.getProperty("CRM_URL", "");
	}

	public static String getCrmXappId() {
		return cxApiContext.getProperty("CRM_XAPPID", "");
	}

	public static String getCrmXappKey() {
		return cxApiContext.getProperty("CRM_XAPPKey", "");
	}


}
