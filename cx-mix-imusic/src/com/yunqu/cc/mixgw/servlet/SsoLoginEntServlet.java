package com.yunqu.cc.mixgw.servlet;

import java.io.IOException;
import java.net.URLEncoder;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.security.SecurityUtil;
import com.yunqu.cc.mixgw.base.AppBaseServlet;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.base.QueryFactory;

@WebServlet("/rest/ssoLoginEnt")
public class SsoLoginEntServlet extends AppBaseServlet {
	public static Logger logger =  CommonLogger.getLogger("ticket");
//	private static String  = "405ccead97f771009c33f3b1644b3f88";
	
	private static final long serialVersionUID = 1L;
	//acc=xxx&pwd=xxx&op=xxx&v=123
	public void actionForIndex() {
		HttpServletRequest req = this.getRequest();
		HttpServletResponse resp = this.getResponse();
		try {
			String acc = req.getParameter("acc");
			String pwd = req.getParameter("pwd");
			String v = req.getParameter("v");
			String op = req.getParameter("op");
			String busiOrderId = Constants.getBusiOrderId();
			String entId = Constants.getEntId();
			//获取门户重定向的登录URL
			String loginUrl = AppContext.getContext("cc-portal").getProperty("LOGIN_URL", "");
			if (StringUtils.isAnyBlank(acc,pwd,v,op)) {
				//ticket为空 跳转至登录界面
				logger.error("[SsoLoginEntServlet] method->login 必填参数为空，跳转门户重定向的登录URL:"+loginUrl);
				if (StringUtils.isBlank(loginUrl)) {
					return;
				}
				try {
					resp.sendRedirect(loginUrl);
				} catch (IOException e) {
					logger.error("[SsoLoginEntServlet] method->login 重定向失败,"+e.getMessage(),e);
				}
				return;
			}
			String securityKey = getSecurityKey(acc, entId);
			if (StringUtils.isBlank(securityKey)) {
				logger.error("当前账号未配置加密key【securityKey】,需要在用户中外部账号字段添加");
				return;
			}
			
			EasyCalendar calendar = EasyCalendar.newInstance();
			String dateId = calendar.getDateInt()+"";
			
			String checkKey = SecurityUtil.encryptMsgByMD5(acc+pwd+op+securityKey+dateId);
			if (!StringUtils.equals(checkKey.toLowerCase(), v.toLowerCase())) {
				logger.error("checkKey:" + checkKey +  "[SsoLoginEntServlet] method->login checkKey与参数v不同，跳转门户重定向的登录URL:"+loginUrl);
				if (StringUtils.isBlank(loginUrl)) {
					return;
				}
				try {
					resp.sendRedirect(loginUrl);
				} catch (IOException e) {
					logger.error("[SsoLoginEntServlet] method->login 重定向失败,"+e.getMessage(),e);
				}
				return;
			}
			
			CacheManager.getCache().put("cx_assertion" + checkKey,checkKey);
			
			HttpSession session = req.getSession();
			
			session.setAttribute("_const_cas_assertion_", true);
			session.setAttribute("oper", op);
			
			JSONObject userInfo = getUserInfo(acc, entId);
			if (userInfo!=null) {
				String password = userInfo.getString("USER_PWD");
				String successUrl = "/cx-mix-imusic/servlet/index?oper="+op;
				JSONObject params = new JSONObject();
				params.put("busiId", "007");
				params.put("returnUrl", successUrl);
				params.put("un", acc);
				params.put("pw", password);
				params.put("time", System.currentTimeMillis());
				
				logger.info("单点登录Mars平台:"+ params.toJSONString());
				String jsonStr = URLEncoder.encode(params.toJSONString(), "UTF-8");
				String redirectUrl = "/yc-login/login?action=sso&token="+jsonStr + "&busiId=" + 007;
				logger.info("marsToken:"+jsonStr + ", redirectUrl:" + redirectUrl);
				redirect(redirectUrl);
			}
			
			
			
		} catch (Exception e) {
			logger.error("[SsoLoginEntServlet] method->login "+e.getMessage(),e);
		}
	}
	
	private JSONObject getUserInfo(String acc, String entId) {
		EasyQuery query = QueryFactory.getWriteQuery();
		try {
			EasySQL sql = new EasySQL();
			sql.append("SELECT t1.USER_ACCT, t1.USER_PWD");
			sql.append("FROM CC_USER t1");
			sql.append("WHERE 1=1");
			sql.append(acc, "AND (t1.USER_ACCT=?", false);
			sql.append(acc, "or t1.SSO_ACCT=?)", false);
			sql.append(entId, "AND t1.ENT_ID=?", false);
			sql.append(" AND T1.USER_STATE=0 ");
			return query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return null;
		}
	}

	private String getSecurityKey(String acc, String entId) {
		EasyQuery query = QueryFactory.getWriteQuery();
		try {
			EasySQL sql = new EasySQL();
			sql.append("select SSO_ACCT  from  cc_user where 1 = 1 ");
			sql.append(entId, "AND ENT_ID = ? ");
			sql.append(acc, " AND USER_ACCT = ? ");
			logger.info("查询sso_acct : " + query.queryForString(sql.getSQL(), sql.getParams()));
			return query.queryForString(sql.getSQL(), sql.getParams());
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return "";
		}
	}
}
