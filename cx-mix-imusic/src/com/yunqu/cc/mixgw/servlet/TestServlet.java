/**
 * 
 */
package com.yunqu.cc.mixgw.servlet;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import com.yq.busi.common.model.NoticeMessage;
import com.yq.busi.common.util.NoticeHelper;
import com.yunqu.cc.mixgw.service.notice.CxEorderNoticeService;
import com.yunqu.cc.mixgw.util.FTPUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.mixgw.base.AppBaseServlet;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.base.QueryFactory;
import com.yunqu.cc.mixgw.util.ApiUtils;
import com.yunqu.cc.mixgw.util.OrderUtils;
import com.yunqu.cc.oss.util.CommonUtil;

/**
 * <AUTHOR>
 * @date 2024-05-11 19:29:12
 */
@WebServlet("/servlet/test")
public class TestServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;

	private Logger logger = CommonLogger.getLogger("data-xf");


	public EasyResult actionForFileTest() {
		try {
			FTPUtils.initUpload();
			FTPUtils.initDownload();
			// --- 文件路径定义 ---
			String remoteBaseDir = "";
			String remoteFileName = "10000000917001020007BUS4300120250701A001.jpg";
			String remoteFilePath = remoteBaseDir + "/" + remoteFileName;
			logger.info("--- 1. 上传测试 (从内存字符串) ---");
			String fileContent = "This is a test file uploaded by FTPUtils.\nLine 2.";
			try (InputStream inputStream = new ByteArrayInputStream(fileContent.getBytes(StandardCharsets.UTF_8))) {
				File file = new File("/temp/file");
				if(!file.exists()) {
					file.mkdirs();
				}
				String localFilePath = file.getPath() + File.separator + remoteFileName;
				FTPUtils.downloadFile("10000000917001020007BUS4300120250701A001.jpg", localFilePath);
				file =  new File(localFilePath);
				if(file.exists()) {
					logger.info("下载成功1");
//					FTPUtils.downloadFile("10000000917001020007BUS4300120250627A001.docx", localFilePath);
//					file =  new File(localFilePath);
//					if (file.exists()) {
//						logger.info("下载成功2");
//					}
//						file.delete();
				} else {
					logger.info("下载失败");
				}
//				boolean uploadSuccess = FTPUtils.uploadFile(inputStream, remoteFilePath);
//				logger.info("Upload success: " + uploadSuccess);
//				if (uploadSuccess) {
//					logger.info("--- 2. 下载测试 (到内存字符串) ---");
//					File file = new File("/temp/file");
//					if(!file.exists()) {
//						file.mkdirs();
//					}
//					String localFilePath = file.getPath() + File.separator + remoteFileName;
//					FTPUtils.downloadFile("10000000917001020007BUS4300120250627A001.docx", localFilePath);
//					file =  new File(localFilePath);
//					if(file.exists()) {
//						logger.info("下载成功1");
//						FTPUtils.downloadFile("10000000917001020007BUS4300120250627A001.docx", localFilePath);
//						file =  new File(localFilePath);
//						if (file.exists()) {
//							logger.info("下载成功2");
//						}
////						file.delete();
//					} else {
//						logger.info("下载失败");
//					}
//				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			logger.info("----------------------------------------\n");
			logger.info("--- 测试完成 ---");
		} catch (Exception e) {
			logger.error("error:" + e.getMessage(), e);
		}
		return EasyResult.ok();
	}

	public EasyResult actionForFileDownloadTest() {
		try {
			FTPUtils.initUpload();
			FTPUtils.initDownload();
			// --- 文件路径定义 ---
			String remoteBaseDir = "/1937717629172572160/download";
			String remoteFileName = "10000000916001010007BUS4300120250626A587.xlsx";
			String remoteFilePath = remoteBaseDir + "/" + remoteFileName;
			logger.info("--- 1. 上传测试 (从内存字符串) ---");
			String fileContent = "This is a test file uploaded by FTPUtils.\nLine 2.";
			File file = new File("/temp/file");
			if(!file.exists()) {
				file.mkdirs();
			}
			String localFilePath = file.getPath() + File.separator + remoteFileName;
			FTPUtils.downloadFile(remoteFilePath, localFilePath);
			file =  new File(localFilePath);
			if(file.exists()) {
				logger.info("下载成功");
				file.delete();
			}
			logger.info("----------------------------------------\n");
			logger.info("--- 测试完成 ---");
		} catch (Exception e) {
			logger.error("error:" + e.getMessage(), e);
		}
		return EasyResult.ok();
	}

	/**
	 * 转发通知测试
	 * @return
	 */
	public EasyResult actionForTransferNoticeTest() {
		try {
			UserModel userModel = UserUtil.getUser(getRequest());
			NoticeMessage message = new NoticeMessage();
			message.setNoticeModuleCode(Constants.APP_NAME);
			message.setNoticeEntId(userModel.getEpCode());
			message.setNoticeBusiOrderId(userModel.getBusiOrderId());
			message.setNoticeSchema(userModel.getSchemaName());
			message.setNoticeCode(CxEorderNoticeService.CX_EORDER_TRANSFER_NOTICE);
			message.setExParamValue("orderNo", "123");
			message.setExParamValue("transferUser", "zst_xuzj");

			NoticeHelper.notice(message);
			return EasyResult.ok();
		} catch (Exception e) {
			logger.error("error:" + e.getMessage(), e);
		}
		return EasyResult.fail();
	}

	

	/**
	 * 订单创建通知测试
	 * @return
	 */
	public EasyResult actionForCreateNoticeTest() {
		try {
			UserModel userModel = UserUtil.getUser(getRequest());
			NoticeMessage message = new NoticeMessage();
			message.setNoticeModuleCode(Constants.APP_NAME);
			message.setNoticeEntId(userModel.getEpCode());
			message.setNoticeBusiOrderId(userModel.getBusiOrderId());
			message.setNoticeSchema(userModel.getSchemaName());
			message.setNoticeCode(CxEorderNoticeService.CX_EORDER_XTY_NOTICE);
			message.setExParamValue("orderNo", "456");
			message.setExParamValue("dealUser", "zst_xuzj");
			message.setExParamValue("dealWorkGroupId", "1111");
			NoticeHelper.notice(message);
			return EasyResult.ok();
		} catch (Exception e) {
			logger.error("error:" + e.getMessage(), e);
		}
		return EasyResult.fail();
	}

	/**
	 * 订单取消通知测试
	 * @return
	 */
	public EasyResult actionForCancelNoticeTest() {
		try {
			UserModel userModel = UserUtil.getUser(getRequest());
			NoticeMessage message = new NoticeMessage();
			message.setNoticeModuleCode(Constants.APP_NAME);
			message.setNoticeEntId(userModel.getEpCode());
			message.setNoticeBusiOrderId(userModel.getBusiOrderId());
			message.setNoticeSchema(userModel.getSchemaName());
			message.setNoticeCode(CxEorderNoticeService.CX_EORDER_URGING_NOTICE);
			message.setExParamValue("orderNo", "789");
			message.setExParamValue("dealUser", "zst_xuzj");
			message.setExParamValue("dealWorkGroupId", "1111");

			NoticeHelper.notice(message);
			return EasyResult.ok();
		} catch (Exception e) {
			logger.error("error:" + e.getMessage(), e);
		}
		return EasyResult.fail();
	}

	/**
	 * 订单完成通知测试
	 * @return
	 */
	public EasyResult actionForCompleteNoticeTest() {
		try {
			UserModel userModel = UserUtil.getUser(getRequest());
			NoticeMessage message = new NoticeMessage();
			message.setNoticeModuleCode(Constants.APP_NAME);
			message.setNoticeEntId(userModel.getEpCode());
			message.setNoticeBusiOrderId(userModel.getBusiOrderId());
			message.setNoticeSchema(userModel.getSchemaName());
			message.setNoticeCode(CxEorderNoticeService.CX_EORDER_TIMEOUT_NOTICE);
			message.setExParamValue("orderNo", "101112");
			message.setExParamValue("dealUser", "zst_xuzj");
			message.setExParamValue("dealWorkGroupId", "1111");

			NoticeHelper.notice(message);
			return EasyResult.ok();
		} catch (Exception e) {
			logger.error("error:" + e.getMessage(), e);
		}
		return EasyResult.fail();
	}
	

	public EasyResult actionForTest() {
		try {
			String orderNo = getPara("orderNo");
			
			UserModel user = UserUtil.getUser(getRequest());
			
			String entId = user.getEpCode();
			String schema = user.getSchemaName();
			String busiOrderId = user.getBusiOrderId();
		
			EasyQuery query = QueryFactory.getQuery(entId);
		
		
			EasySQL sql = new EasySQL("select t2.ID ORDER_ID,t1.AUTO_REPLAY_MSG,t2.ORDER_NO,t2.BUSI_ORDER_ID,t3.REFUND_ACC,t1.AGENT_ENTER_RESULT,t4.CREATE_ACC,t2.CALLER");
			sql.append("from " + schema + ".C_BOX_MUSIC_STANDARD_EX t1");
			sql.append("left join " + schema + ".C_BO_BASE_ORDER t2 on t1.M_ID = t2.ID");
			sql.append("left join " + schema + ".C_BOX_MUSIC_STANDARD_BASE t3 on t3.M_ID = t2.ID");
			sql.append("left join " + schema + ".C_BO_BASE_ORDER t4 on t1.M_ID = t4.ID");
			sql.append(orderNo, "where t2.ORDER_NO = ?", false);
			sql.append(orderNo, "or t2.ID = ?", false);
			JSONObject data = query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			if(data == null) {
				return EasyResult.fail();
			}
		
			// 更新客服产品处理状态
			orderNo = data.getString("ORDER_NO");
			String orderId = data.getString("ORDER_ID");
			String custPhone = data.getString("CALLER");
		
		
			List<JSONObject> productList = query.queryForList("select * from " + schema + ".C_BOX_MUSIC_PRODUCT where ORDER_NO = ? and ACTION = ? and CLAIMANT = ?", new Object[] {orderNo, Constants.RPA_ACTION_REFUND, Constants.CLAIMANT_02}, new JSONMapperImpl());
			for (JSONObject productInfo : productList) {
				// 退订时间为空时，从退订记录中获取退订时间
				String refundTime = productInfo.getString("REFUND_TIME");
				if(StringUtils.isBlank(refundTime)) {
					String productId = productInfo.getString("PRODUCT_ID");
					String productCode = productInfo.getString("PRODUCT_CODE");
					String orderTime = productInfo.getString("ORDER_TIME");
					if(StringUtils.isNotBlank(orderTime)) {
						orderTime = orderTime.substring(0, 10);
					}
					List<JSONObject> ununOrderList = ApiUtils.getTDProductList(custPhone, orderTime, DateUtil.getCurrentDateStr("yyyy-MM-dd"), Constants.OP_ACC_RPA);
					for (JSONObject unOrderInfo : ununOrderList) {
						if(StringUtils.equalsAny(unOrderInfo.getString("productId"), productId, productCode) || StringUtils.equalsAny(unOrderInfo.getString("feeProductId"), productId, productCode)) {
							refundTime = unOrderInfo.getString("ununTime");
							productInfo.put("REFUND_TIME", refundTime);
							break;
						}
					}
				}
				String handleMsg = OrderUtils.getOrderReplayMsg(orderId, productInfo, entId, busiOrderId, schema);
				return EasyResult.ok().put("handleMsg", handleMsg);
			}
			return EasyResult.ok();
		} catch(Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
			return EasyResult.fail();
		}
	}
}
