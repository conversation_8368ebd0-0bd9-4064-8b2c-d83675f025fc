package com.yunqu.cc.mixgw.servlet;


import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.mixgw.base.AppBaseServlet;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.util.MinistryNoticeUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.slf4j.LoggerFactory;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.sql.SQLException;
import java.util.Calendar;
import java.util.Date;
import java.util.Enumeration;

@MultipartConfig
@WebServlet("/rpa/notice")
public class AyyUrlBusiInterServlet extends AppBaseServlet {

    private Logger logger = CommonLogger.getLogger();
    private final String TEMPLATE_CODE = "1091549368";
    public JSONObject actionForIndex() {
        JSONObject jsonObject = getJSONObject();
//        JSONObject jsonObject = requestToJsonObject(getRequest());

        logger.info("RPA掉线预警参数：" + jsonObject);
        String content = jsonObject.getString("content");
        if (StringUtils.isBlank(content)) {
            return EasyResult.fail("内容为空");
        }
        String ayyOrderRpaNoticePhone = Constants.getAyyOrderRpaNoticePhone();
        String[] phones = ayyOrderRpaNoticePhone.split(",");
        JSONObject exJson = new JSONObject();
        exJson.put("CONTEN", content);

        UserModel userModel = new UserModel();
        userModel.setUserAcc("system");
        userModel.setEpCode(Constants.getEntId());
        userModel.setBusiOrderId(Constants.getBusiOrderId());
        for (String phone : phones) {
            MinistryNoticeUtil.sendMsg(userModel, phone, TEMPLATE_CODE, exJson);
        }
        return EasyResult.ok();
    }

    public static JSONObject requestToJsonObject(HttpServletRequest request) {
        JSONObject requestJson = new JSONObject();
        Enumeration<String> paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String paramName = paramNames.nextElement();
            String[] pv = request.getParameterValues(paramName);
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < pv.length; i++) {
                if (pv[i].length() > 0) {
                    if (i > 0) {
                        sb.append(",");
                    }
                    try {
                        sb.append(URLDecoder.decode(pv[i],"utf-8"));
                    }catch(Exception e){
                        CommonLogger.logger.error(e.getMessage(),e);
                    }
                }
            }
            requestJson.put(paramName, sb.toString());
        }
        return requestJson;
    }

}