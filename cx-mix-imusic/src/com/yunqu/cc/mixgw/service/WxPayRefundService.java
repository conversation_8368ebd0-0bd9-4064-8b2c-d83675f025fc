package com.yunqu.cc.mixgw.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sun.star.script.provider.ScriptFrameworkErrorException;
import com.yq.busi.common.base.IBaseService;
import com.yq.busi.common.service.SchemaService;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.base.QueryFactory;
import com.yunqu.cc.mixgw.util.FileUtils;
import com.yunqu.cc.mixgw.util.OrderUtils;
import com.yunqu.cc.mixgw.util.WxPayRefundApiUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.apache.tomcat.util.bcel.Const;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.kit.RandomKit;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.SQLException;
import java.util.*;

public class WxPayRefundService extends IBaseService {


    private Logger logger = CommonLogger.getLogger("wx-order");


    @Override
    public String getServiceId() {
        return "IM_WX_ORDER_SERVICE";
    }

    @Override
    public String getName() {
        return "微信捞单服务";
    }



    @Override
    public JSONObject invokeMethod(JSONObject params) throws ServiceException {
        String key = "cx_wx_create_order-" + Constants.getEntId();

        try {
            Object o = CacheUtil.get(key);
            if (o != null) {
                logger.info("正在捞单中，稍后处理！！！");
                return EasyResult.fail("正在捞单中，稍后处理！！！");
            } else {
                CacheUtil.put(key, "1");
            }
            String entId = Constants.getEntId();
            String busiOrderId = Constants.getBusiOrderId();
            String currentDateStr = DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD);
            String startTime = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, currentDateStr, -7);
            String schema = SchemaService.findSchemaByEntId(entId);
            EasyQuery query = QueryFactory.getWriteQuery();
            String wxContentMids = Constants.getWxContentMids();
            String[] wxMids = wxContentMids.split(",");
            logger.info("开始捞单。。。");
            // 每三分钟去获取一次列表，查最近一年的
            List<JSONObject> refundList = new ArrayList<>();
            for (String mid : wxMids) { // 查询所有商户
                refundList.addAll(WxPayRefundApiUtils.getRefundList(startTime, currentDateStr, mid));
            }
            for (JSONObject jsonObject : refundList) {
                String complaintState = jsonObject.getString("complaintState"); // 订单处理状态
                String transactionId = jsonObject.getString("transactionId"); // 微信退款订单号
                String complaintId = jsonObject.getString("complaintId"); // 投诉订单号
//               Boolean incomingUserResponse = jsonObject.getBoolean("incomingUserResponse"); // 是否有待回复的用户留言
                String problemType = jsonObject.getString("problemType"); // 投诉类型
                JSONObject orderData = jsonObject.getJSONObject("order");
                Integer amount = jsonObject.getInteger("amount"); // 订单要求退款金额
                String mid = jsonObject.getString("mid");
                String complaintDetail = jsonObject.getString("complaintDetail"); // 投诉详情
                String userComplaintTimes = jsonObject.getString("userComplaintTimes"); // 用户投诉次数
                JSONArray complaintMediaList = jsonObject.getJSONArray("complaintMediaList"); // 投诉图片列表
                String complaintTime = jsonObject.getString("complaintTime");
                String phone = "";
                int userComplaintTimesInt = Integer.parseInt(userComplaintTimes);

                // TODO 测试
//                if (!StringUtils.containsAny(complaintDetail, "测试", "19911218159不认可")) {
//                    logger.info("测试");
//                    continue;
//                };

                boolean autoHandleResult = false;
                String autoHandleMsg = "";

                JSONObject existOrder = getExistOrder(complaintId, schema, query, userComplaintTimesInt, complaintTime);
                logger.info("existOrder:" + existOrder);
                if (existOrder !=null && StringUtils.equals(existOrder.getString("IS_EXIST"), "1"))  { // 已经建单，再次投诉
                    String porderId = existOrder.getString("ORDER_ID");

                    logger.info("微信订单号："+transactionId+" 投诉订单号："+complaintId+" 第"+userComplaintTimes+"次投诉， 待处理，开始建单" + "，父工单号:" + existOrder.getString("ORDER_NO"));

                    try {
                        JSONObject payData = WxPayRefundApiUtils.getOrderStatus(transactionId, mid); // 判断是否是否成功，不成功转人工
                        String successTime = payData.getString("successTime"); // 成功支付时间

                        String sTime = successTime.substring(0, 10).replace("T", " ");
                        String eTime = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, sTime, 2);

                        boolean isRefund = false; // 判断投诉是否未退款
                        if (StringUtils.equalsAny(problemType, "REFUND")) isRefund = true;
                        boolean isPaySuccess = false; // 判断支付是否成功
                        if (StringUtils.equals(payData.getString("tradeState"), "SUCCESS")) isPaySuccess = true;
                        boolean isOrderSuccess = false; // 判断是否订购成功

                        boolean isExistUnorder = false; // 存在未退订的产品

                        List<JSONObject> rpaActionData = new ArrayList<>();

                        // 取第三方流水数据
                        JSONObject otherPayResult = WxPayRefundApiUtils.getOtherPayResult(sTime, eTime, transactionId);

                        if (otherPayResult == null) {
                            isOrderSuccess = false;
                        } else {
                            if (StringUtils.equals(otherPayResult.getString("trade_status"), "SUCCESS")) isOrderSuccess = true;
                            phone = otherPayResult.getString("phonenumber");

                            String h5orderno = otherPayResult.getString("h5orderno");
                            List<JSONObject> oneProductByh5OrderNo = WxPayRefundApiUtils.getOneProductByh5OrderNo(h5orderno, phone); // 工单产品
                            logger.info("ismp:"+oneProductByh5OrderNo);
                            if (oneProductByh5OrderNo == null || oneProductByh5OrderNo.size() == 0) { // 没有返回也是ismp
                                isExistUnorder = false; // 没有查到表示已经退订
                            } else {
                                isExistUnorder = oneProductByh5OrderNo.stream().anyMatch(item ->
                                        (!StringUtils.equals(item.getString("state"), "已退订") || !StringUtils.equals(item.getString("packageType"), "包月")));
                            }

                            rpaActionData = imspToProduct(oneProductByh5OrderNo, amount);

                        }
                        if (isRefund // 判断投诉是否未退款
                                && isPaySuccess  // 判断支付是否成功
                                && isOrderSuccess // 判断是否订购成功
                                && !isExistUnorder  // 没有存在未退订的产品
                        ) { // 建自动处理工单
                            // 三级分类
                            orderData.put("class1","1005");
                            orderData.put("className1","投诉");
                            orderData.put("class2","10051009");
                            orderData.put("className2","业务退订退费");
                            orderData.put("class3","100510091001");
                            orderData.put("className3","用户否认开通业务");

                            orderData.put("isRefund", "Y");
                            // 是否自动处理
                            orderData.put("isAutoHandle","N");

                            autoHandleResult = true;
                            autoHandleMsg = "自动处理成功";

                        } else { // 转人工
                            // 设置分配工作组
                            // 三级分类
                            orderData.put("class1","1005");
                            orderData.put("className1","投诉");
                            orderData.put("class2","10051002");
                            orderData.put("className2","业务使用异常");
                            orderData.put("class3","100510021001");
                            orderData.put("className3","业务已开通权益未生效");

                            orderData.put("isRefund", "Y");
                            orderData.put("isAutoHandle","N");

                            autoHandleResult = false;

                            StringBuilder reasons = new StringBuilder();
                            boolean hasReason = false;
                            reasons.append("投诉订单[").append(complaintId).append("]");
                            reasons.append("微信支付服务订单[").append(transactionId).append("]");
                            reasons.append("自动处理处理结果：处理失败，原因：");
                            if (!isRefund) {
                                reasons.append("投诉类型为非退费");
                                hasReason = true;
                            }
                            if (!isPaySuccess) {
                                if (hasReason) reasons.append("；");
                                reasons.append("未支付成功");
                                hasReason = true;
                            }
                            if (!isOrderSuccess) {
                                if (hasReason) reasons.append("；");
                                reasons.append("未订购成功");
                                hasReason = true;
                            }
                            if (!isExistUnorder) {
                                if (hasReason) reasons.append("；");
                                reasons.append("存在未退订产品");
                            }

                            autoHandleMsg = reasons.toString();
                        }



                        orderData.put("rpaActionData", rpaActionData);
                        orderData.put("productData", rpaActionData);

                        orderData.put("pOrderId", porderId);
                        orderData.put("class1","1005");
                        orderData.put("className1","投诉");
                        orderData.put("class2","10051002");
                        orderData.put("className2","业务使用异常");
                        orderData.put("class3","100510021001");
                        orderData.put("className3","业务已开通权益未生效");

                        orderData.put("isRefund", "N");
                        orderData.put("isAutoHandle","N");
                        orderData.put("agentHandleGroup", handleAgentHandleGroup(entId, schema, orderData.getString("appealSource"), orderData.getString("urgency"), orderData, orderData.getString("provinceCode")));

                        orderData.put("ex_3", "01");
                        orderData.put("handleContent", "【重复投诉】第" + userComplaintTimes + "次发起投诉");
                        autoHandleResult = false;
                        IService service = ServiceContext.getService("IM_ORDER_FLOW_SERVICE");
                        if (service!=null) {
                            orderData.put("command", "addOrder");
                            logger.info	("[调用工单服务IM_ORDER_FLOW_SERVICE]"+",[请求]"+orderData);
                            JSONObject resultObj = service.invoke(orderData);
                            logger.info("[调用工单服务IM_ORDER_FLOW_SERVICE]"+",[返回]"+resultObj);

                            // 解析返回结果
                            String state = resultObj.getString("state");
                            JSONObject data = resultObj.getJSONObject("data");
                            if (StringUtils.equals(state, "1")) {
                                JSONObject dataIn = data.getJSONObject("data");
                                JSONObject dataJson = dataIn.getJSONObject("data");
                                JSONObject dbOrderMain = dataJson.getJSONObject("db_order_main");
                                String orderId = dbOrderMain.getString("ID");
                                String orderNo = dbOrderMain.getString("ORDER_NO");
//                                saveContent(complaintDetail, complaintMediaList, complaintTime, orderNo, orderId, schema, entId);

                                saveComplaintContent(orderId, orderNo, complaintId, schema, entId, query, false);

                                saveComplaint(jsonObject, orderNo, orderId, autoHandleResult, autoHandleMsg, porderId, schema, entId, busiOrderId, query, true);
                            } else {
                                logger.info("创建工单失败" );
                            }
                        } else {
                            logger.info("工单服务ID:IM_ORDER_FLOW_SERVICE不存在");
                        }
                    } catch (Exception e) {
                        logger.error(e.getMessage(), e);
                    }

                }
                else if(existOrder !=null && StringUtils.equals(existOrder.getString("IS_EXIST"), "2")) {  // 已经建单, 进行追诉
                     logger.info("[ivr追诉],订单已存在，进行追诉");
                     String orderNo = existOrder.getString("ORDER_NO");
                     String orderId = existOrder.getString("ORDER_ID");
                    saveComplaintContent(orderId, orderNo, complaintId, schema, entId, query, false);

//                     if (incomingUserResponse) { // 有用户留言才进行追诉
//
//                     } else {
//                         logger.info("投诉订单【"+complaintId+"】微信订单号："+transactionId+" 已建单["+orderNo+"]， 没有待回复用户留言，结束处理");
//                     }

                 }
                else if (existOrder == null && StringUtils.equalsAny(complaintState, "PENDING", "PROCESSING")) { // 未建单，进行建单
                    logger.info("微信订单号："+transactionId+" 投诉订单号："+complaintId+" 待处理，开始建单");
                    JSONObject payData = WxPayRefundApiUtils.getOrderStatus(transactionId, mid); // 判断是否是否成功，不成功转人工
                    String successTime = payData.getString("successTime"); // 成功支付时间

                    String sTime = successTime.substring(0, 10).replace("T", " ");
                    String eTime = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, sTime, 2);

                    boolean isRefund = false; // 判断投诉是否未退款
                    if (StringUtils.equalsAny(problemType, "REFUND")) isRefund = true;
                    boolean isPaySuccess = false; // 判断支付是否成功
                    if (StringUtils.equals(payData.getString("tradeState"), "SUCCESS")) isPaySuccess = true;
                    boolean isOrderSuccess = false; // 判断是否订购成功

                    boolean isExistUnorder = false; // 存在未退订的产品

                    List<JSONObject> rpaActionData = new ArrayList<>();

                    // 取第三方流水数据
                    JSONObject otherPayResult = WxPayRefundApiUtils.getOtherPayResult(sTime, eTime, transactionId);

                    if (otherPayResult == null) {
                        isOrderSuccess = false;
                    } else {
                        if (StringUtils.equals(otherPayResult.getString("trade_status"), "SUCCESS")) isOrderSuccess = true;
                        phone = otherPayResult.getString("phonenumber");

                        String h5orderno = otherPayResult.getString("h5orderno");
                        List<JSONObject> oneProductByh5OrderNo = WxPayRefundApiUtils.getOneProductByh5OrderNo(h5orderno, phone); // 工单产品
                        logger.info("ismp:"+oneProductByh5OrderNo);
                        if (oneProductByh5OrderNo == null || oneProductByh5OrderNo.size() == 0) { // 没有返回也是ismp
                            isExistUnorder = false; // 没有查到表示已经退订
                        } else {
                            isExistUnorder = oneProductByh5OrderNo.stream().anyMatch(item ->
                                    (!StringUtils.equals(item.getString("state"), "已退订") || !StringUtils.equals(item.getString("packageType"), "包月")));
                        }
                        rpaActionData = imspToProduct(oneProductByh5OrderNo, amount);

                    }

                    // 用stream判断是否存在未退订state!=2或者不是包月的产品，存在转人工
                    // 处理产品
                    orderData.put("ex_3", "01");
                    orderData.put("rpaActionData", rpaActionData);
                    orderData.put("productData", rpaActionData);
//                    orderData.put("caller", phone);
//                    orderData.put("refundAcc", phone);
//                    orderData.put("contactPhone", phone);
                    orderData.put("agentHandleGroup", handleAgentHandleGroup(entId, schema, orderData.getString("appealSource"), orderData.getString("urgency"), orderData, orderData.getString("provinceCode")));

                    if (rpaActionData.size() == 0) {
                        orderData.put("allExpenses", amount);
                    }

                    logger.info("[ivr建单],订单产品payData："+payData + "otherPayResult:" + otherPayResult);
                    logger.info("isRefund:" + isRefund + " isPaySuccess:" + isPaySuccess + " isOrderSuccess:" + isOrderSuccess + " isExistUnorder:" + isExistUnorder);
                    if ( isRefund // 判断投诉是否未退款
                        && isPaySuccess  // 判断支付是否成功
                        && isOrderSuccess // 判断是否订购成功
                        && !isExistUnorder  // 没有存在未退订的产品
                    ) { // 建自动处理工单
                        // 三级分类
                        orderData.put("class1","1005");
                        orderData.put("className1","投诉");
                        orderData.put("class2","10051009");
                        orderData.put("className2","业务退订退费");
                        orderData.put("class3","100510091001");
                        orderData.put("className3","用户否认开通业务");

                        orderData.put("isRefund", "Y");
                        // 是否自动处理
                        orderData.put("isAutoHandle","N");

                        autoHandleResult = true;
                        autoHandleMsg = "自动处理成功";

                    } else { // 转人工
                        // 设置分配工作组
                        // 三级分类
                        orderData.put("class1","1005");
                        orderData.put("className1","投诉");
                        orderData.put("class2","10051002");
                        orderData.put("className2","业务使用异常");
                        orderData.put("class3","100510021001");
                        orderData.put("className3","业务已开通权益未生效");

                        orderData.put("isRefund", "Y");
                        orderData.put("isAutoHandle","N");

                        autoHandleResult = false;

                        StringBuilder reasons = new StringBuilder();
                        boolean hasReason = false;
                        reasons.append("投诉订单[").append(complaintId).append("]");
                        reasons.append("微信支付服务订单[").append(transactionId).append("]");
                        reasons.append("自动处理处理结果：处理失败，原因：");
                        if (!isRefund) {
                            reasons.append("投诉类型为非退费");
                            hasReason = true;
                        }
                        if (!isPaySuccess) {
                            if (hasReason) reasons.append("；");
                            reasons.append("未支付成功");
                            hasReason = true;
                        }
                        if (!isOrderSuccess) {
                            if (hasReason) reasons.append("；");
                            reasons.append("未订购成功");
                            hasReason = true;
                        }
                        if (!isExistUnorder) {
                            if (hasReason) reasons.append("；");
                            reasons.append("存在未退订产品");
                        }

                        autoHandleMsg = reasons.toString();
                    }


                    IService service = ServiceContext.getService("IM_ORDER_FLOW_SERVICE");
                    if (service!=null) {
                        orderData.put("command", "addOrder");
                        logger.info	("[调用工单服务IM_ORDER_FLOW_SERVICE]"+",[请求]"+orderData);
                        JSONObject resultObj = service.invoke(orderData);
                        logger.info("[调用工单服务IM_ORDER_FLOW_SERVICE]"+",[返回]"+resultObj);

                        // 解析返回结果
                        String state = resultObj.getString("state");
                        JSONObject data = resultObj.getJSONObject("data");
                        if (StringUtils.equals(state, "1")) {
                            JSONObject dataIn = data.getJSONObject("data");
                            JSONObject dataJson = dataIn.getJSONObject("data");
                            JSONObject dbOrderMain = dataJson.getJSONObject("db_order_main");
                            String orderId = dbOrderMain.getString("ID");
                            String orderNo = dbOrderMain.getString("ORDER_NO");
//                            saveContent(complaintDetail, complaintMediaList, complaintTime, orderNo, orderId, schema, entId);

                            // 保存协商历史
                            saveComplaintContent(orderId, orderNo, complaintId, schema, entId, query, true);

//                            if (incomingUserResponse) { // 有用户留言才进行追诉
//
//                            } else {
//                                logger.info("【建单】投诉订单【"+complaintId+"】微信订单号："+transactionId+" 已建单["+orderNo+"]， 没有待回复用户留言，结束处理");
//                            }

                            // 保存工单投诉
                            saveComplaint(jsonObject, orderNo, orderId, autoHandleResult, autoHandleMsg, "", schema, entId, busiOrderId, query, true);
                        } else {
                            logger.info("创建工单失败" );
                        }
                    } else {
                        logger.info("工单服务ID:IM_ORDER_FLOW_SERVICE不存在");
                    }
                } else {
                    logger.info("微信订单号："+transactionId+" 投诉订单【"+complaintId+"】不处理，原因："+ complaintState + "," + userComplaintTimes + "," + existOrder );
                }
            }
            return null;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return error();
        } finally {
            logger.info("捞单结束");
            CacheUtil.delete(key);
        }
    }

    private String handleAgentHandleGroup(String entId, String schema, String source, String urgency, JSONObject infoJson, String provinceCode) throws SQLException {
        EasySQL sql = new EasySQL();
        sql.append("select t2.ID, t2.NAME, t2.TYPE");
        sql.append("from CC_RPA_AUTO_ALLCATION t1");
        sql.append("left join "+ schema +".C_CF_WORKGROUP t2 on t1.WORKGROUP_ID=t2.ID");
        sql.append("where 1=1");
        sql.append(provinceCode, "and (t1.PROVINCE_CODE = ? or t1.PROVINCE_CODE = '')");
        sql.append(source, "and (t1.APPEAL_SOURCE = ? or t1.APPEAL_SOURCE = '')");
        sql.append(urgency, "and (t1.ORDER_TYPE = ? or t1.ORDER_TYPE = '')");
        sql.append("order by PRIORITY");
        JSONObject row = QueryFactory.getQuery(entId).queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

        // 默认处理工作组
        String agentHandleGroup = Constants.getDefaultWorkId();
        if (row != null) {
            String name = row.getString("NAME");
            if (StringUtils.isNotBlank(name)) {
                agentHandleGroup = "W("+ name +")";
            }
        }
        return agentHandleGroup;
    }


    public void saveContent(String operateDetails,JSONArray complaintMediaList, String complaintTime, String orderNo,String orderId,
                               String schema, String entId) throws Exception
    {
        String busiId = RandomKit.randomStr();
        for (int i = 0; i < complaintMediaList.size(); i++) {
            JSONObject complaintMedia = (JSONObject) complaintMediaList.get(i);
            if (complaintMedia != null) {
                JSONArray mediaUrl = complaintMedia.getJSONArray("media_url"); // 文件路径
                uploadFile(mediaUrl, orderId, schema, entId, Constants.getBusiOrderId(), busiId);
            }
        }
        complaintTime = complaintTime.substring(0, 19).replace("T", " ");

        OrderUtils.addOrderFollow(busiId, "追加诉求", operateDetails, Constants.FOLLOW_TYPE_33, orderId, orderNo, schema, "");

    }

    public void saveComplaintContent(String orderId, String orderNo, String complaintId, String schema, String entId,  EasyQuery query, boolean isCreate) throws Exception {
        // 查询投诉单协商历史
        //// 查询最新的诉求内容时间
        List<JSONObject> oneOrderStatus = WxPayRefundApiUtils.getOneOrderStatus(complaintId, isCreate ? "" : getLastcontentTime(complaintId, schema, query));
        logger.info("查询投诉单协商历史:{}"+oneOrderStatus);
        if(oneOrderStatus != null && oneOrderStatus.size() > 0){
            for (JSONObject oneOrderContent : oneOrderStatus) {
                String createTime = oneOrderContent.getString("operate_time"); // 协商时间
                String operateType = oneOrderContent.getString("operate_type");
                String operator = oneOrderContent.getString("operator");
                String busiId = RandomKit.randomStr();
                // 上传文件
                JSONObject complaintMediaList = oneOrderContent.getJSONObject("complaint_media_list");
                if (complaintMediaList != null) {
                    JSONArray mediaUrl = complaintMediaList.getJSONArray("media_url"); // 文件路径
                    uploadFile(mediaUrl, orderId, schema, entId, Constants.getBusiOrderId(), busiId);
                }
                createTime = createTime.substring(0, 19).replace("T", " ");
                String operateDetails = "【协商历史】\n" +
                                        "协商时间：" + createTime + "\n" +
                                        "角色：" + operator + "\n" +
                                        "动作类型：" +WxPayRefundApiUtils.getOperateTypeDescription(operateType) + "\n" +
                                        "协商内容：" + oneOrderContent.getString("operate_details");

                OrderUtils.addOrderFollow(busiId, "追加诉求", operateDetails, Constants.FOLLOW_TYPE_33, orderId, orderNo, schema, createTime);

            }

        }
    }


    private void saveComplaint(JSONObject json,String orderNo,String orderId,boolean authHandleResult, String autoHandleMsg, String pOrder,
                               String schema, String entId, String busiOrderId, EasyQuery query, boolean isCreate) throws SQLException {
        EasyRecord easyRecord = new EasyRecord(schema + ".wx_complaint_order", "ID");
        easyRecord.put("ID", orderId);
        easyRecord.put("COMPLAINT_ID", json.getString("complaintId"));

        // 投诉基本信息
        easyRecord.put("COMPLAINT_TIME", json.getString("complaintTime"));
        easyRecord.put("COMPLAINT_DETAIL", json.getString("complaintDetail"));
        easyRecord.put("COMPLAINT_STATE", json.getString("complaintState"));
        easyRecord.put("PAYER_PHONE", json.getString("payerPhone"));
        easyRecord.put("TRANSACTION_ID", json.getString("transactionId"));
        easyRecord.put("OUT_TRADE_NO", json.getString("outTradeNo"));
        easyRecord.put("AMOUNT", json.getString("amount"));

        // 投诉状态
        easyRecord.put("COMPLAINT_FULL_REFUNDED", json.getBoolean("complaintFullRefunded") ? "Y" : "N");
        easyRecord.put("INCOMING_USER_RESPONSE", json.getBoolean("incomingUserResponse") ? "Y" : "N");
        easyRecord.put("USER_COMPLAINT_TIMES", json.getString("userComplaintTimes"));

        // 媒体信息
        easyRecord.put("MEDIA_TYPE", json.getString("mediaType"));
        easyRecord.put("MEDIA_URLS", json.getString("mediaUrls"));

        // 问题信息
        easyRecord.put("PROBLEM_TYPE", json.getString("problemType"));
        easyRecord.put("PROBLEM_DESCRIPTION", json.getString("problemDescription"));
        easyRecord.put("APPLY_REFUND_AMOUNT", json.getString("applyRefundAmount"));
        easyRecord.put("USER_TAGS", json.getString("userTags"));

        // 服务订单信息
        easyRecord.put("SERVICE_ORDER_ID", json.getString("serviceOrderId"));
        easyRecord.put("SERVICE_OUT_ORDER_NO", json.getString("serviceOutOrderNo"));
        easyRecord.put("SERVICE_STATE", json.getString("serviceState"));

        // 补充信息
        easyRecord.put("ADDITIONAL_TYPE", json.getString("additionalType"));
        easyRecord.put("RETURN_TIME", json.getString("returnTime"));
        easyRecord.put("RETURN_ADDRESS", json.getString("returnAddress"));
        easyRecord.put("LONGITUDE", json.getString("longitude"));
        easyRecord.put("LATITUDE", json.getString("latitude"));
//        easyRecord.put("IS_RETURNED_TO_SAME_MACHINE", json.getBoolean("isReturnedToSameMachine") ? "Y" : "N");
//        easyRecord.put("IN_PLATFORM_SERVICE", json.getBoolean("inPlatformService") ? "Y" : "N");
//        easyRecord.put("NEED_IMMEDIATE_SERVICE", json.getBoolean("needImmediateService") ? "Y" : "N");

        // 工单信息
        easyRecord.put("ENT_ID", entId);
        easyRecord.put("BUSI_ORDER_ID", busiOrderId);
        easyRecord.put("ORDER_ID", orderId);
        easyRecord.put("ORDER_NO", orderNo);
        easyRecord.put("PORDER_NO", pOrder);
        easyRecord.put("AUTO_HANDLE_RESULT", authHandleResult? "0": "1");
        easyRecord.put("AUTO_HANDLE_MSG", autoHandleMsg);

        // 时间信息
        String now = DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT);
        easyRecord.put("CREATE_TIME", now);
        easyRecord.put("UPDATE_TIME", now);

        easyRecord.put("OUT_REFUND_NO", json.getString("transactionId"));

        easyRecord.put("MID", json.getString("mid"));
        // 保存记录
        if (isCreate) {
            query.save(easyRecord);
        } else {
            query.update(easyRecord);
        }
    }

    private List<JSONObject> imspToProduct(List<JSONObject> list, Integer amount) {
        List<JSONObject> result = new ArrayList<>();
        boolean hasSet = false;
        for (JSONObject json : list) {
            String feeProductId = json.getString("feeProductId");
            String mobile = json.getString("mobile");
            String orderTime = json.getString("orderTime");
            String productName = json.getString("productName");
            String unUseTime = json.getString("unUseTime");
            String productid = json.getString("productid");
            String price = json.getString("price");


            JSONObject jsonObject = new JSONObject();
            jsonObject.put("refundBills", !hasSet ? amount / 100 : "0"); // 退赔话费 单产品总价格,只能有一个产品有价格
            jsonObject.put("expenses", price); // 资费 单产品价格
            jsonObject.put("claimant", "02"); // 理赔方
            jsonObject.put("orderTime", orderTime); // 订购时间
            jsonObject.put("productId", productid); // 产品id
            jsonObject.put("feeProductId", feeProductId); // 产品编码
            jsonObject.put("productName", productName); // 产品名称
            jsonObject.put("refundTime", unUseTime); // 退订时间
            jsonObject.put("orderChannel", ""); // 订购渠道
            jsonObject.put("action", "01"); // 意图
            jsonObject.put("number", mobile); // 手机号
            jsonObject.put("channelCode", ""); // 渠道编码

            hasSet = true;
            result.add(jsonObject);
        }
        return result;
    }


    /**
     * 上传文件
     * @param fileList 文件路径列表
     * @param orderId
     * @param schema
     * @param entId
     * @param busiOrderId
     * @throws Exception
     */
    private void uploadFile(JSONArray fileList, String orderId, String schema, String entId, String busiOrderId, String busiId) throws Exception {
        for (Object o : fileList) {
            String fileUrl = (String) o;
            // https://api.mch.weixin.qq.com/v3/merchant-service/images/ChsyMDAwMDAwMjAyNTAzMTgxMDAyNTk4NDIzNzAYACCmu%2BO%2BBigBMAE4AQ%3D%3D
            // 截取最后的id
            String mediaId = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);

            // 外部文件路径转为FileInputStream
            try {
                String imageBase64 = WxPayRefundApiUtils.getImage(mediaId);

                if (imageBase64.startsWith("data:image/png;base64,")) {
                    imageBase64 = imageBase64.substring("data:image/png;base64,".length());
                } else if (imageBase64.startsWith("data:image/jpeg;base64,")) {
                    imageBase64 = imageBase64.substring("data:image/jpeg;base64,".length());
                } else if (imageBase64.startsWith("data:image/gif;base64,")) {
                    imageBase64 = imageBase64.substring("data:image/gif;base64,".length());
                }

                // 把imageBase64转成文件流FileInputStream
                byte[] imageBytes = Base64.getDecoder().decode(imageBase64);

                // 创建临时文件
                File tempFile = File.createTempFile("temp", ".tmp");
                try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                    fos.write(imageBytes);
                }
                // 使用FileInputStream读取临时文件
                try (FileInputStream fileInputStream = new FileInputStream(tempFile)) {
                    JSONObject jsonObject = FileUtils.fileUpload(fileInputStream, "zdybd", schema, entId, busiOrderId, busiId, RandomKit.randomStr() + ".png");
                    logger.info("成功获取文件流，文件URL: " + fileUrl);
                } finally {
                    // 删除临时文件
                    tempFile.delete();
                }
            } catch (Exception e) {
                logger.error("处理文件时发生错误，URL: " + fileUrl, e);
                throw e;
            }
        }
    }

    /**
     * 判断此订单号是否存在工单了
     * @param complaintId 微信订单号
     * @param schema
     * @return 存在返回true
     */
    private JSONObject getExistOrder(String complaintId, String schema, EasyQuery query, Integer userComplaintTimes, String complaintDate) throws SQLException {
        EasySQL sql = new EasySQL();
        // 改成查询USER_COMPLAINT_TIMES最大的那一条数据
        sql.append("select * from " + schema + ".wx_complaint_order");
        sql.append(complaintId, "where COMPLAINT_ID = ?" );
        sql.append(" order by USER_COMPLAINT_TIMES desc");
        JSONObject jsonObject = query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
        if (jsonObject == null) {
            return null;
        }
        Integer userComplaintTimes1 = jsonObject.getInteger("USER_COMPLAINT_TIMES");
        String orderNo = jsonObject.getString("ORDER_NO");

        logger.info("此投诉存在工单" + userComplaintTimes+ ":" + userComplaintTimes1);
        // 此投诉存在工单
        if (userComplaintTimes > userComplaintTimes1) { // 升级投诉，建下级工单
            jsonObject.put("IS_EXIST", "1"); // 建立下级工单
        } else {
            String systemOrderEndTime = isSystemOrder(query, schema, orderNo);
            if (StringUtils.isNotBlank(systemOrderEndTime)) { // 已归档
                List<JSONObject> oneOrderStatus = WxPayRefundApiUtils.getOneOrderStatus(complaintId, getLastcontentTime(complaintId, schema, query));
                if (oneOrderStatus != null && oneOrderStatus.size() > 0) {
                    jsonObject.put("IS_EXIST", "1"); // 工单归档，用户发消息，建下级工单
                } else {
                    jsonObject.put("IS_EXIST", "0"); // 无需处理
                }
            } else {
                jsonObject.put("IS_EXIST", "2"); // 加协商历史
            }
        }
//        if (jsonObject != null) {
//            ////  处理已归档工单
//            String systemOrderEndTime = isSystemOrder(query, schema, orderNo);
//            if (StringUtils.isNotBlank(systemOrderEndTime)) { // 已归档
//                if (userComplaintTimes > userComplaintTimes1) {
//                    jsonObject.put("IS_EXIST", "1"); // 建立下级工单
////                    if (DateUtil.bwSeconds(complaintDate, systemOrderEndTime) > 0) { //// 是否升级投诉，且不在升级投诉时间在归档后
////                        logger.info(complaintId + "：升级投诉，且不在升级投诉时间在归档后");
////                        jsonObject.put("IS_EXIST", "1"); // 建立下级工单
////                    } else {
////                        logger.info(complaintId + "：说明未归档前已添加了此升级投诉为协商历史");
////                        jsonObject.put("IS_EXIST", "0"); // 说明未归档前已添加了此升级投诉为协商历史
////                    }
//                } else { // 用户没有升级投诉，但是可能发消息
////                    List<JSONObject> oneOrderStatus = WxPayRefundApiUtils.getOneOrderStatus(complaintId, getLastcontentTime(complaintId, schema, query));
////                    if (oneOrderStatus != null && oneOrderStatus.size() > 0) { // 用户有发消息
////                        logger.info(complaintId + "：此投诉存在工单，且未归档，判断是否需要建立下级工单");
////                        jsonObject.put("IS_EXIST", "1"); // 建立下级工单
////                    } else {
////                        logger.info(complaintId + "：无需处理");
////                        jsonObject.put("IS_EXIST", "0"); // 无需处理
////                    }
//                }
//            } else { // 未归档, 查一次协商历史看是否要添加，包含了升级投诉
//                logger.info(complaintId + "：未归档");
//                jsonObject.put("IS_EXIST", "2");
//            }
//        }

//        if (userComplaintTimes1 == userComplaintTimes) { // 当前支付订单已经处理完所有投诉
//            jsonObject.put("IS_EXIST", true);
//        } else if (userComplaintTimes > userComplaintTimes1) { // 当前支付订单还有没处理完所有投诉
//            // 判断工单是否归档
//
//            JSONObject b = query.queryForRow(sql.getSQL(), sql.getParams(), new  JSONMapperImpl());
//            if (b == null) { // 没有归档就追诉
//                jsonObject.put("IS_EXIST", true);
//            } else { // 归档就建子工单
//                String endTime = b.getString("END_TIME");
//                if (DateUtil.bwSeconds(complaintDate, endTime) > 0) { // 归档后加的投诉才建子工单
//                    jsonObject.put("IS_EXIST", false);
//                } else {
//                    jsonObject.put("IS_EXIST", true);
//                }
//            }
//        }

        return jsonObject;
    }


    /**
     * 是否归档
     * @param query
     * @param schema
     * @param orderNo
     * @return
     * @throws SQLException
     */
    private String isSystemOrder(EasyQuery query, String schema,  String orderNo) throws SQLException {
        EasySQL sql = new EasySQL();
        sql.append("select END_TIME from " + schema + ".c_bo_base_order ");
        sql.append("where 1 = 1 ");
        sql.append(orderNo, "and ORDER_NO = ? ", false);
        sql.appendIn(new String[]{"2", "3"}, "and STATUS ");
        return query.queryForString(sql.getSQL(), sql.getParams());
    }


    /**
     * 判断此投诉订单是否处理过
     * @param complaintId 投诉订单号
     * @param schema
     * @return 存在返回true
     */
    private boolean hasHandle(String complaintId, String schema, EasyQuery query) throws SQLException {
        EasySQL sql = new EasySQL();
        sql.append("select count(1) from " + schema + ".wx_complaint_order");
        sql.append(complaintId, "where COMPLAINT_ID = ?" );
        return query.queryForExist(sql.getSQL(), sql.getParams());
    }


    /**
     * 获取最后一次诉求内容时间
     * @param orderId
     * @param schema
     * @param query
     * @return
     * @throws SQLException
     */
    private String getLastcontentTime(String complaintId, String schema, EasyQuery query) throws SQLException {
        EasySQL sql = new EasySQL();
        sql.append("select MAX(ARRIVAL_TIME) from " + schema + ".c_bo_order_follow");
        sql.append("where ORDER_ID in (");
        sql.append(complaintId, "select ID from " + schema + ".wx_complaint_order where COMPLAINT_ID = ? )", false);
        sql.append(Constants.FOLLOW_TYPE_33 ,"and TYPE = ? ");
        logger.info("sql:" + sql.getSQL()+ Arrays.toString(sql.getParams()));
        return query.queryForString(sql.getSQL(), sql.getParams());
    }
}
