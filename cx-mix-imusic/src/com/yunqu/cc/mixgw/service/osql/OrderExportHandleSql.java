package com.yunqu.cc.mixgw.service.osql;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.CCEntModuleContext;
import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.model.DeptModel;
import com.yq.busi.common.model.RoleModel;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.model.WorkGroupModel;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.QueryFactory;
import com.yunqu.cc.mixgw.porder.base.EorderConstants;
import com.yunqu.cc.mixgw.porder.base.OrderextConstants;
import com.yunqu.cc.mixgw.porder.cache.OrderAuthCache;
import com.yunqu.cc.mixgw.porder.cache.ProcAuthCache;
import com.yunqu.cc.mixgw.porder.config.OrderGlobalCfgService;
import com.yunqu.cc.mixgw.porder.model.OrderFollowModel;
import com.yunqu.cc.mixgw.porder.model.OrderNoticeCfg;
import com.yunqu.cc.mixgw.porder.util.OrderBusiUtil;
import com.yunqu.cc.mixgw.porder.util.QueryProcessUtil;
import com.yunqu.yc.sso.impl.YCUserPrincipal;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.kit.JsonKit;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;

public class OrderExportHandleSql {

    private static Logger logger = CommonLogger.logger;

    /**
     * 代办工单SQL
     * @param param
     * @param user
     * @return
     */
    public static EasySQL getDoOrderSql(JSONObject param, UserModel user){
        String proId = param.getString("processId");
        String tagSelect = param.getString("tagSelect");
        String schema = user.getSchemaName();
        List<EasyRow> records = new ArrayList<EasyRow>();
        EasySQL sql = new EasySQL();
        Map<String, String> relationMap = new HashMap<String, String>();
        try {
            if (StringUtils.isNotBlank(proId)) {
                records = QueryProcessUtil.getDbField(user, proId);
            }
            //初始字段
            sql = initField(sql,user,records,relationMap, proId);
            sql.append(",C2.TAG_NAME,C1.ID_ AS TASK_ID ");
            
            PorderBusiSql.getBusiField(sql);
            
            sql.append(" from " + schema + ".C_BO_BASE_ORDER c_bo_base_order ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
            sql.append(user.getUserAcc(), " INNER JOIN ACT_RU_TASK C1 on c_bo_base_order.PROC_INST_ID=C1.PROC_INST_ID_  AND C1.ASSIGNEE_=? ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_ORDER_PRIVATE_EXT C2 on C2.ORDER_ID=c_bo_base_order.ID ");
            //关联业务表
            appendBusiTable(sql,schema,records,relationMap);
            if (StringUtils.isNotBlank(tagSelect)) {
                sql.append("LEFT JOIN " + schema + ".C_BO_ORDER_TAG_REF C0 on C0.ORDER_ID=c_bo_base_order.ID");
            }
            sql.append(" where 1=1 ");
            sql.append(tagSelect, " and C0.TAG_ID = ? ");
            // 添加办理权限
            OrderExportHandleSql.appendCompleteAuthSearch(sql, "C1", user, EorderConstants.PROC_AUTH_TYPE_COMPLETE);
            //sql尾部公有SQL
            sql = initDoBasicSearch(sql,param,relationMap,user);
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
        return sql;
    }
    
    /**
     * 代办工单数量SQL
     * @param param
     * @param user
     * @return
     */
    public static EasySQL getDoOrderCountSql(JSONObject param, UserModel user){
        String schema = user.getSchemaName();
        EasySQL sql = new EasySQL();
        sql.append("SELECT COUNT(1) ");
        sql.append("FROM " + schema + ".C_BO_BASE_ORDER T1");
        sql.append(" , ACT_RU_TASK T2");
        sql.append("WHERE T2.PROC_INST_ID_ = T1.PROC_INST_ID AND T1.STATUS IN ('1','5') AND T1.PROC_INST_ID IS NOT NULL");
        sql.append(user.getUserAcc(),"and t2.ASSIGNEE_ = ? ",false);
        sql.append(user.getEpCode(),"and t1.EP_CODE = ?",false);
        sql.append(param.getString("startDate"),"and t1.ARRIVE_TIME >= ?",false);
        sql.append(param.getString("endDate"),"and t1.ARRIVE_TIME <= ?",false);
        logger.info("[getDoOrderCountSql] "+sql.getSQL()+",param:"+JSON.toJSONString(sql.getParams()));
        return sql;
    }
    
    /**
     * 代领工单数量SQL
     * @param param
     * @param user
     * @return
     */
    public static EasySQL getWaitOrderCountSql(JSONObject param, UserModel user){
        String schema = user.getSchemaName();
        EasySQL sql = new EasySQL();
        Map<String, String> relationMap = new HashMap<String, String>();
        try {
            //初始字段
            sql.append(" SELECT COUNT(1) ");

            sql.append(" from " + schema + ".C_BO_BASE_ORDER c_bo_base_order ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
            sql.append(" INNER JOIN ACT_RU_TASK C1 ON c_bo_base_order.PROC_INST_ID=C1.PROC_INST_ID_ AND C1.ASSIGNEE_ IS NULL");
            sql.append(" LEFT JOIN  ACT_RU_IDENTITYLINK C2 ON C2.TASK_ID_ = C1.ID_ AND TYPE_='candidate' ");
            sql.append(" where 1=1 ");
            List<WorkGroupModel> workGroups = user.getWorkGroups();
            sql.append(" and (");
            sql.append(user.getUserAcc(), " C2.USER_ID_ = ? ");
            sql.append("OR C2.GROUP_ID_ in ('-1'");
            for (WorkGroupModel group : workGroups) {
                sql.append("W(" + group.getName() + ")", ", ?");
            }
            List<DeptModel> depts = user.getDepts();
            if (depts != null && depts.size() > 0) {
                for (DeptModel dept : depts) {
                    String deptCode = dept.getDeptCode();
                    String deptName = dept.getDeptName();
                    sql.append("D(" + deptCode + "-" + deptName + ")", ", ?");
                }
            }
            List<RoleModel> roles = user.getRoles();
            if (roles!=null && roles.size()>0) {
            	for (RoleModel role:roles) {
            		String roleId = role.getRoleId();
            		String roleName = role.getRoleName();
            		sql.append("R("+ roleId +"-"+ roleName +")", ", ?");
            	}
            }
            sql.append(")");
            sql.append(")");
            // 添加办理权限
            OrderExportHandleSql.appendCompleteAuthSearch(sql, "C1", user, EorderConstants.PROC_AUTH_TYPE_COMPLETE);
            sql = initDoBasicSearch(sql,param,relationMap,user,null);
            logger.info("[getWaitOrderCountSql] "+sql.getSQL()+",param:"+JSON.toJSONString(sql.getParams()));
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
        return sql;
    }
    
    /**
     * 暂存工单数量SQL
     * @param param
     * @param user
     * @return
     */
    public static EasySQL getStagingOrderCountSql(JSONObject param, UserModel user){
        String schema = user.getSchemaName();
        EasySQL sql = new EasySQL();
        Map<String, String> relationMap = new HashMap<String, String>();
        try {
            //初始字段
            sql.append(" SELECT COUNT(1)");
            sql.append(" from " + schema + ".C_BO_BASE_ORDER c_bo_base_order ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
            sql.append("LEFT JOIN ACT_RU_TASK C1 on c_bo_base_order.PROC_INST_ID=C1.PROC_INST_ID_");
            sql.append(" where 1=1 ");
            sql.append("5", " and c_bo_base_order.STATUS = ? ");
            sql.append(user.getUserAcc(), "and (((c_bo_base_order.PROC_INST_ID is null or c_bo_base_order.PROC_INST_ID = '') and c_bo_order_ex.CURR_HANDLER = ? )");
            sql.append(user.getUserAcc(), "or (C1.ASSIGNEE_=?");
            // 添加办理权限
//            OrderHandleSql.appendCompleteAuthSearch(sql, "C1", user, Constants.PROC_AUTH_TYPE_COMPLETE);
            sql.append("))");
            sql = initBasicSearch(sql,param,relationMap,user);
            logger.info("[getStagingOrderCountSql] "+sql.getSQL()+",param:"+JSON.toJSONString(sql.getParams()));
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
        return sql;
    }
    

    /**
     * 质检工单sql
     * @param param
     * @param user
     * @param easyQuery
     * @return
     */
    public static EasySQL getQualityOrderSql(JSONObject param, UserModel user, EasyQuery easyQuery) {
        String proId = param.getString("processId");
        String schema = user.getSchemaName();
        List<EasyRow> records = new ArrayList<EasyRow>();
        EasySQL sql = new EasySQL();
        Map<String, String> relationMap = new HashMap<String, String>();
        DBTypes type=easyQuery.getTypes();
        try {
            if (StringUtils.isNotBlank(proId)) {
                // 有流程分类
                records = QueryProcessUtil.getDbField(user, proId);
            }
            //初始字段
            sql = initField(sql,user,records,relationMap, proId);
            sql.append(" from " + schema + ".C_BO_BASE_ORDER c_bo_base_order ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
            //关联业务表
            appendBusiTable(sql,schema,records,relationMap);
            sql.append(" where 1=1 ");
            sql.append(DictConstants.DICT_SY_YN_Y, "and c_bo_order_ex.IS_MAIN_ORDER=?", false);
            if(DBTypes.ORACLE.equals(type) || DBTypes.PostgreSql.equals(type)){
                sql.append( " and c_bo_base_order.ORDER_CHARGE is not null ");
            }else{
                sql.append( " and c_bo_base_order.ORDER_CHARGE is not null and c_bo_base_order.ORDER_CHARGE!='' ");
            }

            sql.append("and c_bo_base_order.STATUS in ('-1'");
            sql.append(OrderextConstants.ORDER_STATUS_END, ", ?");
            sql.append(OrderextConstants.ORDER_STATUS_CLOSE, ", ?");
            sql.append(OrderextConstants.ORDER_STATUS_ENDCLOSE, ", ?");
            sql.append(")");
            sql = initBasicSearch(sql,param,relationMap,user);
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
        return sql;
    }

    /**
     * 授权工单SQL
     * @param param
     * @param user
     * @return
     */
    public static EasySQL getAuthOrderSql(JSONObject param, UserModel user){
        String nodeId = param.getString("nodeId");
        String grantAcc = param.getString("grantAcc");
        String proId = param.getString("processId");
        String tagSelect = param.getString("tagSelect");
        String schema = user.getSchemaName();
        List<EasyRow> records = new ArrayList<EasyRow>();
        EasySQL sql = new EasySQL();
        Map<String, String> relationMap = new HashMap<String, String>();
        try {
            if (StringUtils.isNotBlank(proId)) {
                // 有流程分类
                records = QueryProcessUtil.getDbField(user, proId);
            }
            //初始字段
            sql = initField(sql,user,records,relationMap, proId);
            sql.append(" ,C2.TAG_NAME,C1.ID_ AS TASK_ID ");
            sql.append(" from " + schema + ".C_BO_BASE_ORDER c_bo_base_order ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
            sql.append(grantAcc, " INNER JOIN ACT_RU_TASK C1 on c_bo_base_order.PROC_INST_ID=C1.PROC_INST_ID_  AND C1.ASSIGNEE_=? ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_ORDER_PRIVATE_EXT C2 on C2.ORDER_ID=c_bo_base_order.ID ");
            //关联业务表
            appendBusiTable(sql,schema,records,relationMap);
            if (StringUtils.isNotBlank(tagSelect)) {
                sql.append("LEFT JOIN " + schema + ".C_BO_ORDER_TAG_REF C0 on C0.ORDER_ID=c_bo_base_order.ID");
            }
            sql.append(" where 1=1 ");
            sql.append(nodeId, " and C1.TASK_DEF_KEY_ = ? ");
            sql.append(OrderextConstants.ORDER_STATUS_HANDLE, "AND c_bo_base_order.STATUS = ?");
            sql.append(tagSelect, " and C0.TAG_ID = ? ");
            sql = initBasicSearch(sql,param,relationMap,user);
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
        return sql;
    }

    /**
     * 待领工单SQL
     * @param param
     * @param user
     * @return
     */
    public static EasySQL getToDoOrderSql(JSONObject param, UserModel user){
    	logger.info("getToDoOrderSql的param: "+ param.toJSONString());
        String proId = param.getString("processId");
        String schema = user.getSchemaName();
        List<EasyRow> records = new ArrayList<EasyRow>();
        EasySQL sql = new EasySQL();
        Map<String, String> relationMap = new HashMap<String, String>();
        try {
            if (StringUtils.isNotBlank(proId)) {
                // 有流程分类
                records = QueryProcessUtil.getDbField(user, proId);
            }
            //初始字段
            sql = initField(sql,user,records,relationMap, proId);
            sql.append(" ,C1.ID_ AS TASK_ID ");

            sql.append(" from " + schema + ".C_BO_BASE_ORDER c_bo_base_order ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
            sql.append(" INNER JOIN ACT_RU_TASK C1 ON c_bo_base_order.PROC_INST_ID=C1.PROC_INST_ID_ AND C1.ASSIGNEE_ IS NULL");
            sql.append(" LEFT JOIN  ACT_RU_IDENTITYLINK C2 ON C2.TASK_ID_ = C1.ID_ AND TYPE_='candidate' ");
            //关联业务表
            appendBusiTable(sql,schema,records,relationMap);
            sql.append(" where 1=1 ");
            List<WorkGroupModel> workGroups = user.getWorkGroups();
            sql.append(" and (");
            sql.append(user.getUserAcc(), " C2.USER_ID_ = ? ");
            sql.append("OR C2.GROUP_ID_ in ('-1'");
            for (WorkGroupModel group : workGroups) {
                sql.append("W(" + group.getName() + ")", ", ?");
            }
            List<DeptModel> depts = user.getDepts();
            if (depts != null && depts.size() > 0) {
                for (DeptModel dept : depts) {
                    String deptCode = dept.getDeptCode();
                    String deptName = dept.getDeptName();
                    sql.append("D(" + deptCode + "-" + deptName + ")", ", ?");
                }
            }
            List<RoleModel> roles = user.getRoles();
            if (roles!=null && roles.size()>0) {
            	for (RoleModel role:roles) {
            		String roleId = role.getRoleId();
            		String roleName = role.getRoleName();
            		sql.append("R("+ roleId +"-"+ roleName +")", ", ?");
            	}
            }
            sql.append(")");
            sql.append(")");
            // 添加办理权限
            OrderExportHandleSql.appendCompleteAuthSearch(sql, "C1", user, EorderConstants.PROC_AUTH_TYPE_COMPLETE);
            sql = initDoBasicSearch(sql,param,relationMap,user,null);
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
        return sql;
    }

    /**
     * 已办工单SQL
     * @param param
     * @param user
     * @return
     */
    public static EasySQL getDoneOrderSql(JSONObject param, UserModel user){
        String proId = param.getString("processId");
        String schema = user.getSchemaName();
        List<EasyRow> records = new ArrayList<EasyRow>();
        EasySQL sql = new EasySQL();
        Map<String, String> relationMap = new HashMap<String, String>();
        try {
            if (StringUtils.isNotBlank(proId)) {
                // 有流程分类
                records = QueryProcessUtil.getDbField(user, proId);
            }
            //初始字段
            sql = initField(sql,user,records,relationMap, proId);
            sql.append(" from " + schema + ".C_BO_BASE_ORDER c_bo_base_order ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
            sql.append(user.getUserAcc(), " INNER JOIN " + schema + ".C_BO_ORDER_FOLLOW C1 on c_bo_base_order.ID=C1.ORDER_ID  AND C1.ASSIGNEE=? ");
            sql.append(OrderFollowModel.TYPE_CLAIM, "and C1.TYPE<>?");
            sql.append(OrderFollowModel.TYPE_SAVE, "and C1.TYPE<>?");
            sql.append(OrderFollowModel.TYPE_TRANSFER, "and C1.TYPE<>?");
            //关联业务表
            appendBusiTable(sql,schema,records,relationMap);
            sql.append(" where 1=1 ");
            sql = initBasicSearch(sql,param,relationMap,user);
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
        return sql;
    }

    /**
     * 暂存工单SQL
     * @param param
     * @param user
     * @param isMy
     * @return
     */
    public static EasySQL getTempSaveOrderSql(JSONObject param, UserModel user,Boolean isMy){
        String proId = param.getString("processId");
        String schema = user.getSchemaName();
        List<EasyRow> records = new ArrayList<EasyRow>();
        EasySQL sql = new EasySQL();
        Map<String, String> relationMap = new HashMap<String, String>();
        try {
            if (StringUtils.isNotBlank(proId)) {
                // 有流程分类
                records = QueryProcessUtil.getDbField(user, proId);
            }
            //初始字段
            sql = initField(sql,user,records,relationMap, proId);
            sql.append(" ,C1.ID_ AS TASK_ID ");
            sql.append(" from " + schema + ".C_BO_BASE_ORDER c_bo_base_order ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
            sql.append("LEFT JOIN ACT_RU_TASK C1 on c_bo_base_order.PROC_INST_ID=C1.PROC_INST_ID_");
            //关联业务表
            appendBusiTable(sql,schema,records,relationMap);
            sql.append(" where 1=1 ");
            sql.append("5", " and c_bo_base_order.STATUS = ? ");
            if(isMy){
                sql.append(user.getUserAcc(), "and (((c_bo_base_order.PROC_INST_ID is null or c_bo_base_order.PROC_INST_ID = '') and c_bo_order_ex.CURR_HANDLER = ? )");
                sql.append(user.getUserAcc(), "or (C1.ASSIGNEE_=?");
                // 添加办理权限
                OrderExportHandleSql.appendCompleteAuthSearch(sql, "C1", user, EorderConstants.PROC_AUTH_TYPE_COMPLETE);
                sql.append("))");
            }
            sql = initBasicSearch(sql,param,relationMap,user);
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
        return sql;
    }

    /**
     * 工单通用SQL
     * @param param
     * @param user
     * @return
     */
    public static EasySQL getCommonListSql(JSONObject param, UserModel user){
        String proId = param.getString("processId");
        String schema = user.getSchemaName();
        List<EasyRow> records = new ArrayList<EasyRow>();
        EasySQL sql = new EasySQL();
        Map<String, String> relationMap = new HashMap<String, String>();
        try {
            if (StringUtils.isNotBlank(proId)) {
                records = QueryProcessUtil.getDbField(user, proId);
            }
            //初始字段
            sql = initField(sql,user,records,relationMap, proId);
            sql.append(",C2.ORDER_ID AS OID ");
            sql.append(" from " + schema + ".C_BO_BASE_ORDER c_bo_base_order ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
            sql.append(" LEFT JOIN "  + schema +".C_BO_ORDER_ATTENTION C2 ON c_bo_base_order.ID=C2.ORDER_ID ");
            sql.append(user.getUserAcc()," and C2.CREATE_USER =? ");
            //关联业务表
            appendBusiTable(sql,schema,records,relationMap);
            sql.append(" where 1=1 ");
            //sql尾部公有SQL
            sql = initBasicSearch(sql,param,relationMap,user);
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
        return sql;
    }


    /**
     * 回访工单通用SQL
     * @param param
     * @param user
     * @return
     */
    public static EasySQL getVisitListSql(JSONObject param, UserModel user){
        String proId = param.getString("processId");
        String schema = user.getSchemaName();
        List<EasyRow> records = new ArrayList<EasyRow>();
        EasySQL sql = new EasySQL();
        Map<String, String> relationMap = new HashMap<String, String>();
        try {
            if (StringUtils.isNotBlank(proId)) {
                records = QueryProcessUtil.getDbField(user, proId);
            }
            //初始字段
            sql = initField(sql,user,records,relationMap, proId);
            sql.append(",v.VISIT_USER_ACC,v.VISIT_USER_NAME,v.VISIT_DEPT_CODE,v.VISIT_DEPT_NAME,v.VISIT_TIME,v.VISIT_TYPE,v.VISIT_RESULT,v.SOURCE_TYPE VISIT_SOURCE_TYPE ");
            sql.append(",v.CONTACT_NUM,v.ID VISIT_ID ");
            sql.append(" from " + schema + ".C_BO_ORDER_VISIT_RECORD v ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_BASE_ORDER c_bo_base_order on c_bo_base_order.ID = v.M_ID ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
            //关联业务表
            appendBusiTable(sql,schema,records,relationMap);
            sql.append(" where 1=1 ");
            sql.append(param.getString("called")," and v.CONTACT_NUM = ? ");
            sql.append(param.getString("visitResult")," and v.VISIT_RESULT = ? ");
            if(StringUtils.isBlank(param.getString("orderNo"))&&StringUtils.isBlank(param.getString("orderNoSuffix"))){
                sql.append(param.getString("vStartDate"), " and v.CREATE_TIME >= ? ");
                sql.append(param.getString("vEndDate"), " and v.CREATE_TIME <= ? ");
            }
            String ids = param.getString("phrase");
            if(StringUtils.isNotBlank(ids)) {
                sql.appendIn(ids.split(",")," and c_bo_base_order.ID  ");
            }
            //sql尾部公有SQL
            sql = initBasicSearch(sql,param,relationMap,user);
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
        return sql;
    }

    /**
     * 全部、部门、我的工单SQL
     * @param param
     * @param user
     * @return
     */
    public static EasySQL getOrderListSql(JSONObject param, UserModel user){
        String dept = param.getString("dept");
        String proId = param.getString("processId");
        String schema = user.getSchemaName();
        List<EasyRow> records = new ArrayList<EasyRow>();
        EasySQL sql = new EasySQL();
        Map<String, String> relationMap = new HashMap<String, String>();
        try {
            if (StringUtils.isNotBlank(proId)) {
                records = QueryProcessUtil.getDbField(user, proId);
            }
            // 初始字段            
//            String selectFields = "c_bo_base_order.ORDER_NO, "+desenCaller("c_bo_base_order.CALLER")+", C6.ORDER_TYPE, C6.APPEAL_SOURCE, C6.SOURCE_NO, c_bo_base_order.PROVINCE, c_bo_base_order.CUIBAN_NUM, ";
//			// 创建人账号、创建时间、当前处理人、上一环节处理时间、归档时间、是否退赔、退赔账号
//			selectFields += "c_bo_base_order.CREATE_ACC, c_bo_base_order.CREATE_TIME, c_bo_order_ex.CURR_HANDLER, c_bo_order_ex.DEAL_TIME, c_bo_base_order.END_TIME, C6.IS_REFUND, "+desenCaller("C6.REFUND_ACC")+", ";

            String selectFields = "";

            // 退赔金额、退赔原因、 工单状态、联系号码、 投诉内容
			selectFields += "C8.REFUND_NUM, C6.REFUND_REASON, c_bo_base_order.STATUS, C6.EX_2, C6.REGION,";
			// 一级分类、二级分类、三级分类、创建人姓名、处理人姓名、功能类型、用户类型、处理进度、工单处理时长
			selectFields += "C6.FIRST_TYPE_NAME, C6.SECOND_TYPE_NAME, C6.THIRD_TYPE_NAME, c_bo_base_order.CREATE_NAME, c_bo_order_ex.CURR_HANDLER, C6.PRODUCT_TYPE, ";
			selectFields += "C6.USER_ATTR, C6.HANDLE_PROC, c_bo_order_ex.TOTAL_DURATION_EX, c_bo_order_ex.CLAIM_TIME";
            initField(new EasySQL(),user,records,relationMap, proId);
			// sql
			sql.append("select");
			sql.append(desenSQL(user) + selectFields);
			sql.append(", GROUP_CONCAT(C7.PRODUCT_NAME) PRODUCT_NAME");
            if(Integer.parseInt(dept) != 1) {
            	sql.append(",C2.ORDER_ID AS Oid , 'false' AS IS_HIS ");
            	selectFields += ",Oid , IS_HIS ";
            }
            
            PorderBusiSql.getBusiField(sql);
        	selectFields += ",P_ORDER_TYPE , P_TOTAL_DURATION, COLOR, FONT_COLOR";
            sql.append(" from " + schema + ".C_BO_BASE_ORDER c_bo_base_order ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
            if(Integer.parseInt(dept)==0 || Integer.parseInt(dept)==1) {
                sql.append(" LEFT JOIN " + schema +".C_BO_ORDER_FOLLOW C1 on c_bo_base_order.ID=C1.ORDER_ID ");
            }
            if(Integer.parseInt(dept) != 1) {
                sql.append(" LEFT JOIN "  + schema +".C_BO_ORDER_ATTENTION C2 ON c_bo_base_order.ID=C2.ORDER_ID ");
                sql.append(user.getUserAcc()," and C2.CREATE_USER =? ");
            }
            //关联业务表
			sql.append("left join "+ schema +".c_box_music_standard_base C6 on c_bo_base_order.ID=C6.M_ID");
			sql.append("left join "+ schema +".c_box_music_product C7 on c_bo_base_order.ORDER_NO=C7.ORDER_NO");
			sql.append("left join "+ schema +".c_box_music_standard_ex C8 on c_bo_base_order.ID=C8.M_ID");
            sql.append("left join "+ schema + ".c_bo_order_follow C9 on c_bo_base_order.ID = C9.ORDER_ID");

            sql.append(" where 1=1 ");
            if (Integer.parseInt(dept)== 0) {
                sql.appendRLike(user.getDeptCode(), " and (C1.CREATE_DEPT_CODE like ? ");
                sql.appendRLike(user.getDeptCode(), " or c_bo_order_ex.CURR_HANDLE_GROUP like ?) ");
            } else if (Integer.parseInt(dept) == 1) {
                sql.append(user.getUserAcc()," and (C1.CREATE_ACC = ? ");
                sql.append(user.getUserAcc()," or c_bo_order_ex.CURR_HANDLER = ? )");
            } else if (Integer.parseInt(dept) == 3) {
                sql.appendRLike(user.getDeptProvinceCode()," and c_bo_base_order.PROVINCE like ?");
            }
            String ids = param.getString("phrase");
            if(StringUtils.isNotBlank(ids)) {
                sql.appendIn(ids.split(",")," and c_bo_base_order.ID  ");
            }
            appendSearchAuthSearch(sql, "c_bo_base_order", user, EorderConstants.PROC_AUTH_TYPE_SEARCH);
            //sql尾部公有SQL
            sql.append(" and c_bo_base_order.PROC_INST_ID IS NOT NULL AND c_bo_base_order.PROC_INST_ID <> '' ");
            sql = initBasicSearch(sql,param,relationMap,user);
            sql.append("group by " + headSQL() + selectFields);
            sql.append("order by P_ORDER_TYPE DESC,P_TOTAL_DURATION DESC,c_bo_base_order.CREATE_TIME desc ");
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
        return sql;
    }
    private static String headSQL1() {
        String selectFields = "c_bo_base_order.ORDER_NO, c_bo_base_order.CALLER, C6.ORDER_TYPE, C6.APPEAL_SOURCE, C6.SOURCE_NO, c_bo_base_order.PROVINCE, c_bo_base_order.CUIBAN_NUM, ";
        // 创建人账号、创建时间、当前处理人、上一环节处理时间、归档时间、是否退赔、退赔账号
        selectFields += "c_bo_base_order.CREATE_ACC, c_bo_base_order.CREATE_TIME, CURR_HANDLER, c_bo_order_ex.DEAL_TIME, c_bo_base_order.END_TIME, C6.IS_REFUND, C6.REFUND_ACC, ";

        return selectFields;
    }

    private static String desenSQL1(UserModel user) {
        String selectFields = "c_bo_base_order.ORDER_NO, c_bo_base_order.CALLER, C6.ORDER_TYPE, C6.APPEAL_SOURCE, C6.SOURCE_NO, c_bo_base_order.PROVINCE, c_bo_base_order.CUIBAN_NUM, ";
        // 创建人账号、创建时间、当前处理人、上一环节处理时间、归档时间、是否退赔、退赔账号
        selectFields += "c_bo_base_order.CREATE_ACC, c_bo_base_order.CREATE_TIME, c_bo_order_ex.CURR_HANDLER, c_bo_order_ex.DEAL_TIME, c_bo_base_order.END_TIME, C6.IS_REFUND,C6.REFUND_ACC, ";


        selectFields += "GROUP_CONCAT(CASE WHEN C9.TYPE = '33' THEN C9.CONTENT ELSE NULL END)  CONTENT," ;
        selectFields += "GROUP_CONCAT(CASE WHEN C9.TYPE = '66' THEN C9.CONTENT ELSE NULL END)  AUTO_REPLAY_MSG," ;
        return selectFields;
    }
    public static EasySQL getOrderProductListSql(JSONObject param, UserModel user){
        String dept = param.getString("dept");
        String proId = param.getString("processId");
        String schema = user.getSchemaName();
        List<EasyRow> records = new ArrayList<EasyRow>();
        EasySQL sql = new EasySQL();
        Map<String, String> relationMap = new HashMap<String, String>();
        try {
            if (StringUtils.isNotBlank(proId)) {
                records = QueryProcessUtil.getDbField(user, proId);
            }
//            // 初始字段
//            String selectFields = "c_bo_base_order.ORDER_NO, c_bo_base_order.CALLER, C6.ORDER_TYPE, C6.APPEAL_SOURCE, C6.SOURCE_NO, c_bo_base_order.PROVINCE, c_bo_base_order.CUIBAN_NUM, ";
//			// 创建人账号、创建时间、当前处理人、上一环节处理时间、归档时间、是否退赔、退赔账号
//			selectFields += "c_bo_base_order.CREATE_ACC, c_bo_base_order.CREATE_TIME, c_bo_order_ex.CURR_HANDLER, c_bo_order_ex.DEAL_TIME, c_bo_base_order.END_TIME, C6.IS_REFUND, C6.REFUND_ACC, ";

            String selectFields = "";

            // 退赔金额、退赔原因、 工单状态、联系号码、 投诉内容
			selectFields += "C8.REFUND_NUM, C6.REFUND_REASON, c_bo_base_order.STATUS, C6.EX_2, C6.REGION,  ";
			// 一级分类、二级分类、三级分类、创建人姓名、处理人姓名、功能类型、用户类型、处理进度、工单处理时长
			selectFields += "C6.FIRST_TYPE_NAME, C6.SECOND_TYPE_NAME, C6.THIRD_TYPE_NAME, c_bo_base_order.CREATE_NAME, C8.HANDLE_AGENT_ACC, C6.PRODUCT_TYPE, ";
			selectFields += "C6.USER_ATTR, C6.HANDLE_PROC, c_bo_order_ex.TOTAL_DURATION_EX, c_bo_order_ex.CLAIM_TIME";
			selectFields += ", C7.CLAIMANT, C7.PRODUCT_ID, C7.ORDER_CHANNEL, C7.ORDER_TIME, C7.PRODUCT_TAG, C7.PRODUCT_DEPT";
            initField(new EasySQL(),user,records,relationMap, proId);
			// sql
			sql.append("select");
			sql.append(desenSQL1(user) + selectFields);
            if(Integer.parseInt(dept) != 1) {
            	sql.append(",C2.ORDER_ID AS Oid , 'false' AS IS_HIS ");
            	selectFields += ",Oid , IS_HIS ";
            }
            PorderBusiSql.getBusiField(sql);
        	selectFields += ",P_ORDER_TYPE , P_TOTAL_DURATION, COLOR, FONT_COLOR";
            sql.append(" from " + schema + ".C_BO_BASE_ORDER c_bo_base_order ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
            if(Integer.parseInt(dept)==0 || Integer.parseInt(dept)==1) {
                sql.append(" LEFT JOIN " + schema +".C_BO_ORDER_FOLLOW C1 on c_bo_base_order.ID=C1.ORDER_ID ");
            }
            if(Integer.parseInt(dept) != 1) {
                sql.append(" LEFT JOIN "  + schema +".C_BO_ORDER_ATTENTION C2 ON c_bo_base_order.ID=C2.ORDER_ID ");
                sql.append(user.getUserAcc()," and C2.CREATE_USER =? ");
            }
            //关联业务表
			sql.append("left join "+ schema +".c_box_music_standard_base C6 on c_bo_base_order.ID=C6.M_ID");
			sql.append("left join "+ schema +".c_box_music_product C7 on c_bo_base_order.ORDER_NO=C7.ORDER_NO");
			sql.append("left join "+ schema +".c_box_music_standard_ex C8 on c_bo_base_order.ID=C8.M_ID");
            sql.append("left join "+ schema + ".c_bo_order_follow C9 on c_bo_base_order.ID = C9.ORDER_ID");

            sql.append(" where 1=1 ");
            if (Integer.parseInt(dept)== 0) {
                sql.appendRLike(user.getDeptCode(), " and (C1.CREATE_DEPT_CODE like ? ");
                sql.appendRLike(user.getDeptCode(), " or c_bo_order_ex.CURR_HANDLE_GROUP like ?) ");
            } else if (Integer.parseInt(dept) == 1) {
                sql.append(user.getUserAcc()," and (C1.CREATE_ACC = ? ");
                sql.append(user.getUserAcc()," or c_bo_order_ex.CURR_HANDLER = ? )");
            } else if (Integer.parseInt(dept) == 3) {
                sql.appendRLike(user.getDeptProvinceCode()," and c_bo_base_order.PROVINCE like ?");
            }
            String ids = param.getString("phrase");
            if(StringUtils.isNotBlank(ids)) {
                sql.appendIn(ids.split(",")," and c_bo_base_order.ID  ");
            }
            appendSearchAuthSearch(sql, "c_bo_base_order", user, EorderConstants.PROC_AUTH_TYPE_SEARCH);
            //sql尾部公有SQL
            sql = initBasicSearch(sql,param,relationMap,user);
            sql.append("group by "+ headSQL1() + selectFields );

            sql.append(" order by P_ORDER_TYPE DESC,P_TOTAL_DURATION DESC,c_bo_base_order.CREATE_TIME desc ");
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
        return sql;
    }

	public static List<EasyRow> getDbField(UserModel user,String proId) throws SQLException{
		EasyQuery query = QueryFactory.getQuery(user.getEpCode());
		EasySQL sql = new EasySQL("select DB_NAME,DB_TABLE_NAME from "+user.getSchemaName()+".C_BO_AUTO_FORM_DB");
		sql.append(" where 1=1");
		sql.append(proId," and PROCESS_ID=?");
		sql.append(user.getBusiOrderId()," and BUSI_ORDER_ID=?");
		sql.append(user.getEpCode()," and ENT_ID=?");
		// sql.append("Y"," and IS_BUSI_DB=? ");
		sql.append("order by db_name desc");
		List<EasyRow> record = query.queryForList(sql.getSQL(), sql.getParams());
		return record;
	}

    public static EasySQL getAllOrderListSql(JSONObject param, UserModel user){
        String dept = param.getString("dept");
        String proId = param.getString("processId");
        String schema = user.getSchemaName();
        List<EasyRow> records = new ArrayList<EasyRow>();
        EasySQL sql = new EasySQL();
        Map<String, String> relationMap = new HashMap<String, String>();
        try {
            if (StringUtils.isNotBlank(proId)) {
                records = getDbField(user, proId);
            }
            String ids = param.getString("phrase");
            //初始字段
            sql.append("SELECT distinct * FROM(( ");

//            String selectFields = "c_bo_base_order.ORDER_NO, " + desenCaller("c_bo_base_order.CALLER") +", C6.ORDER_TYPE, C6.APPEAL_SOURCE, C6.SOURCE_NO, c_bo_base_order.PROVINCE, c_bo_base_order.CUIBAN_NUM, ";
//			// 创建人账号、创建时间、当前处理人、上一环节处理时间、归档时间、是否退赔、退赔账号
//			selectFields += "c_bo_base_order.CREATE_ACC, c_bo_base_order.CREATE_TIME, c_bo_order_ex.CURR_HANDLER, c_bo_order_ex.DEAL_TIME, c_bo_base_order.END_TIME, C6.IS_REFUND, "+ desenCaller("C6.REFUND_ACC")+", ";

            String selectFields = "";

            // 退赔金额、退赔原因、 工单状态、联系号码、 投诉内容
			selectFields += "C8.REFUND_NUM, C6.REFUND_REASON, c_bo_base_order.STATUS, C6.EX_2, C6.REGION,  ";
			// 一级分类、二级分类、三级分类、创建人姓名、处理人姓名、功能类型、用户类型、处理进度、工单处理时长
			selectFields += "C6.FIRST_TYPE_NAME, C6.SECOND_TYPE_NAME, C6.THIRD_TYPE_NAME, c_bo_base_order.CREATE_NAME, c_bo_order_ex.CURR_HANDLER, C6.PRODUCT_TYPE, ";
			selectFields += "C6.USER_ATTR, C6.HANDLE_PROC, c_bo_order_ex.TOTAL_DURATION_EX, c_bo_order_ex.CLAIM_TIME";
            initField(new EasySQL(),user,records,relationMap, proId);
			// sql
			sql.append("select");
			sql.append(desenSQL(user) + selectFields);
			sql.append(", GROUP_CONCAT(C7.PRODUCT_NAME) PRODUCT_NAME");
            if(Integer.parseInt(dept) != 1) {
            	sql.append(",C2.ORDER_ID AS Oid , 'false' AS IS_HIS ");
            	selectFields += ",Oid , IS_HIS ";
            }
            
            PorderBusiSql.getBusiField(sql);
        	selectFields += ",P_ORDER_TYPE , P_TOTAL_DURATION, COLOR, FONT_COLOR";
            
            sql.append(" from " + schema + ".C_BO_BASE_ORDER c_bo_base_order ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
            if(Integer.parseInt(dept)==0 || Integer.parseInt(dept)==1) {
                sql.append(" LEFT JOIN " + schema +".C_BO_ORDER_FOLLOW C1 on c_bo_base_order.ID=C1.ORDER_ID ");
            }
            if(Integer.parseInt(dept) != 1) {
                sql.append(" LEFT JOIN "  + schema +".C_BO_ORDER_ATTENTION C2 ON c_bo_base_order.ID=C2.ORDER_ID ");
                sql.append(user.getUserAcc()," and C2.CREATE_USER =? ");
            }
            //关联业务表
			sql.append("left join "+ schema +".c_box_music_standard_base C6 on c_bo_base_order.ID=C6.M_ID");
			sql.append("left join "+ schema +".c_box_music_product C7 on c_bo_base_order.ORDER_NO=C7.ORDER_NO");
			sql.append("left join "+ schema +".c_box_music_standard_ex C8 on c_bo_base_order.ID=C8.M_ID");

            sql.append("left join "+ schema + ".c_bo_order_follow C9 on c_bo_base_order.ID = C9.ORDER_ID");

            sql.append(" where 1=1 ");

            if (Integer.parseInt(dept)== 0) {
                sql.appendRLike(user.getDeptCode(), " and (C1.CREATE_DEPT_CODE like ? ");
                sql.appendRLike(user.getDeptCode(), " or c_bo_order_ex.CURR_HANDLE_GROUP like ?) ");
            } else if (Integer.parseInt(dept) == 1) {
                sql.append(user.getUserAcc()," and (C1.CREATE_ACC = ? ");
                sql.append(user.getUserAcc()," or c_bo_order_ex.CURR_HANDLER = ? )");
            } else if (Integer.parseInt(dept) == 3) {
                sql.appendRLike(user.getDeptProvinceCode()," and c_bo_base_order.PROVINCE like ?");
            }
            if(StringUtils.isNotBlank(ids)) {
                sql.appendIn(ids.split(",")," and c_bo_base_order.ID  ");
            }
            appendSearchAuthSearch(sql, "c_bo_base_order", user, EorderConstants.PROC_AUTH_TYPE_SEARCH);
            //sql尾部公有SQL
            sql = initBasicUnionSearch(sql,param,relationMap,user);
            sql.append("group by " + headSQL() + selectFields);
            sql.append(")");
            sql.append(" union all (");

            //初始字段
			sql.append("select");
			sql.append(desenSQL(user) + selectFields);
			sql.append(", GROUP_CONCAT(C7.PRODUCT_NAME) PRODUCT_NAME");
            
            PorderBusiSql.getBusiField(sql);
        	selectFields += ",P_ORDER_TYPE , P_TOTAL_DURATION, COLOR, FONT_COLOR";
            
            sql.append(" from " + schema + ".C_BO_BASE_ORDER_HIS c_bo_base_order ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
            if(Integer.parseInt(dept)==0 || Integer.parseInt(dept)==1) {
                sql.append(" LEFT JOIN " + schema +".C_BO_ORDER_FOLLOW C1 on c_bo_base_order.ID=C1.ORDER_ID ");
            }
            if(Integer.parseInt(dept) != 1) {
                sql.append(" LEFT JOIN "  + schema +".C_BO_ORDER_ATTENTION C2 ON c_bo_base_order.ID=C2.ORDER_ID ");
                sql.append(user.getUserAcc()," and C2.CREATE_USER =? ");
            }
            //关联业务表
			sql.append("left join "+ schema +".c_box_music_standard_base C6 on c_bo_base_order.ID=C6.M_ID");
			sql.append("left join "+ schema +".c_box_music_product C7 on c_bo_base_order.ORDER_NO=C7.ORDER_NO");
			sql.append("left join "+ schema +".c_box_music_standard_ex C8 on c_bo_base_order.ID=C8.M_ID");
            sql.append(" where 1=1 ");
            if (Integer.parseInt(dept)== 0) {
                sql.appendRLike(user.getDeptCode(), " and (C1.CREATE_DEPT_CODE like ? ");
                sql.appendRLike(user.getDeptCode(), " or c_bo_order_ex.CURR_HANDLE_GROUP like ?) ");
            } else if (Integer.parseInt(dept) == 1) {
                sql.append(user.getUserAcc()," and (C1.CREATE_ACC = ? ");
                sql.append(user.getUserAcc()," or c_bo_order_ex.CURR_HANDLER = ? )");
            } else if (Integer.parseInt(dept) == 3) {
                sql.appendRLike(user.getDeptProvinceCode()," and c_bo_base_order.PROVINCE like ?");
            }
            if(StringUtils.isNotBlank(ids)) {
                sql.appendIn(ids.split(",")," and c_bo_base_order.ID  ");
            }
            sql.append(" and c_bo_base_order.PROC_INST_ID IS NOT NULL AND c_bo_base_order.PROC_INST_ID <> '' ");
            appendSearchAuthSearch(sql, "c_bo_base_order", user, EorderConstants.PROC_AUTH_TYPE_SEARCH);
            sql = initBasicUnionSearch(sql,param,relationMap,user);
            sql.append("group by " + headSQL() + selectFields);
            sql.append(") )A order by P_ORDER_TYPE DESC,P_TOTAL_DURATION DESC,CREATE_TIME desc ");
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
        return sql;
    }

    public static EasySQL getAllOrderProductListSql(JSONObject param, UserModel user){
        String dept = param.getString("dept");
        String proId = param.getString("processId");
        String schema = user.getSchemaName();
        List<EasyRow> records = new ArrayList<EasyRow>();
        EasySQL sql = new EasySQL();
        Map<String, String> relationMap = new HashMap<String, String>();
        try {
            if (StringUtils.isNotBlank(proId)) {
                records = getDbField(user, proId);
            }
            String ids = param.getString("phrase");
            //初始字段
            sql.append("SELECT distinct * FROM(( ");
            
//            String selectFields = "c_bo_base_order.ORDER_NO, c_bo_base_order.CALLER, C6.ORDER_TYPE, C6.APPEAL_SOURCE, C6.SOURCE_NO, c_bo_base_order.PROVINCE, c_bo_base_order.CUIBAN_NUM, ";
//			// 创建人账号、创建时间、当前处理人、上一环节处理时间、归档时间、是否退赔、退赔账号
//			selectFields += "c_bo_base_order.CREATE_ACC, c_bo_base_order.CREATE_TIME, c_bo_order_ex.CURR_HANDLER, c_bo_order_ex.DEAL_TIME, c_bo_base_order.END_TIME, C6.IS_REFUND, C6.REFUND_ACC, ";

           String selectFields = "";

            // 退赔金额、退赔原因、 工单状态、联系号码、 投诉内容
			selectFields += "C8.REFUND_NUM, C6.REFUND_REASON, c_bo_base_order.STATUS, C6.EX_2, C6.REGION, ";
			// 一级分类、二级分类、三级分类、创建人姓名、处理人姓名、功能类型、用户类型、处理进度、工单处理时长
			selectFields += "C6.FIRST_TYPE_NAME, C6.SECOND_TYPE_NAME, C6.THIRD_TYPE_NAME, c_bo_base_order.CREATE_NAME, C8.HANDLE_AGENT_ACC, C6.PRODUCT_TYPE, ";
			selectFields += "C6.USER_ATTR, C6.HANDLE_PROC, c_bo_order_ex.TOTAL_DURATION_EX, c_bo_order_ex.CLAIM_TIME";
			selectFields += ", C7.CLAIMANT, C7.PRODUCT_ID, C7.ORDER_CHANNEL, C7.ORDER_TIME, C7.PRODUCT_TAG, C7.PRODUCT_DEPT";
            initField(new EasySQL(),user,records,relationMap, proId);
			// sql
			sql.append("select");
			sql.append(desenSQL1(user) + selectFields);
            if(Integer.parseInt(dept) != 1) {
            	sql.append(",C2.ORDER_ID AS Oid , 'false' AS IS_HIS ");
            	selectFields += ",Oid , IS_HIS ";
            }
            PorderBusiSql.getBusiField(sql);
        	selectFields += ",P_ORDER_TYPE , P_TOTAL_DURATION, COLOR, FONT_COLOR";
            
            sql.append(" from " + schema + ".C_BO_BASE_ORDER c_bo_base_order ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
            if(Integer.parseInt(dept)==0 || Integer.parseInt(dept)==1) {
                sql.append(" LEFT JOIN " + schema +".C_BO_ORDER_FOLLOW C1 on c_bo_base_order.ID=C1.ORDER_ID ");
            }
            if(Integer.parseInt(dept) != 1) {
                sql.append(" LEFT JOIN "  + schema +".C_BO_ORDER_ATTENTION C2 ON c_bo_base_order.ID=C2.ORDER_ID ");
                sql.append(user.getUserAcc()," and C2.CREATE_USER =? ");
            }
            //关联业务表
			sql.append("left join "+ schema +".c_box_music_standard_base C6 on c_bo_base_order.ID=C6.M_ID");
			sql.append("left join "+ schema +".c_box_music_product C7 on c_bo_base_order.ORDER_NO=C7.ORDER_NO");
			sql.append("left join "+ schema +".c_box_music_standard_ex C8 on c_bo_base_order.ID=C8.M_ID");
            sql.append("left join "+ schema + ".c_bo_order_follow C9 on c_bo_base_order.ID = C9.ORDER_ID");
            sql.append(" where 1=1 ");
            if (Integer.parseInt(dept)== 0) {
                sql.appendRLike(user.getDeptCode(), " and (C1.CREATE_DEPT_CODE like ? ");
                sql.appendRLike(user.getDeptCode(), " or c_bo_order_ex.CURR_HANDLE_GROUP like ?) ");
            } else if (Integer.parseInt(dept) == 1) {
                sql.append(user.getUserAcc()," and (C1.CREATE_ACC = ? ");
                sql.append(user.getUserAcc()," or c_bo_order_ex.CURR_HANDLER = ? )");
            } else if (Integer.parseInt(dept) == 3) {
                sql.appendRLike(user.getDeptProvinceCode()," and c_bo_base_order.PROVINCE like ?");
            }
            if(StringUtils.isNotBlank(ids)) {
                sql.appendIn(ids.split(",")," and c_bo_base_order.ID  ");
            }
            appendSearchAuthSearch(sql, "c_bo_base_order", user, EorderConstants.PROC_AUTH_TYPE_SEARCH);
            //sql尾部公有SQL
            sql = initBasicUnionSearch(sql,param,relationMap,user);
            sql.append(")");
            sql.append(" union all (");
            //初始字段
			sql.append("select");
			sql.append(selectFields);
            PorderBusiSql.getBusiField(sql);
        	selectFields += ",P_ORDER_TYPE , P_TOTAL_DURATION, COLOR, FONT_COLOR";
            
            sql.append(" from " + schema + ".C_BO_BASE_ORDER_HIS c_bo_base_order ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
            if(Integer.parseInt(dept)==0 || Integer.parseInt(dept)==1) {
                sql.append(" LEFT JOIN " + schema +".C_BO_ORDER_FOLLOW C1 on c_bo_base_order.ID=C1.ORDER_ID ");
            }
            if(Integer.parseInt(dept) != 1) {
                sql.append(" LEFT JOIN "  + schema +".C_BO_ORDER_ATTENTION C2 ON c_bo_base_order.ID=C2.ORDER_ID ");
                sql.append(user.getUserAcc()," and C2.CREATE_USER =? ");
            }
            //关联业务表
			sql.append("left join "+ schema +".c_box_music_standard_base C6 on c_bo_base_order.ID=C6.M_ID");
			sql.append("left join "+ schema +".c_box_music_product C7 on c_bo_base_order.ORDER_NO=C7.ORDER_NO");
			sql.append("left join "+ schema +".c_box_music_standard_ex C8 on c_bo_base_order.ID=C8.M_ID");
            sql.append(" where 1=1 ");
            if (Integer.parseInt(dept)== 0) {
                sql.appendRLike(user.getDeptCode(), " and (C1.CREATE_DEPT_CODE like ? ");
                sql.appendRLike(user.getDeptCode(), " or c_bo_order_ex.CURR_HANDLE_GROUP like ?) ");
            } else if (Integer.parseInt(dept) == 1) {
                sql.append(user.getUserAcc()," and (C1.CREATE_ACC = ? ");
                sql.append(user.getUserAcc()," or c_bo_order_ex.CURR_HANDLER = ? )");
            } else if (Integer.parseInt(dept) == 3) {
                sql.appendRLike(user.getDeptProvinceCode()," and c_bo_base_order.PROVINCE like ?");
            }
            if(StringUtils.isNotBlank(ids)) {
                sql.appendIn(ids.split(",")," and c_bo_base_order.ID  ");
            }
            appendSearchAuthSearch(sql, "c_bo_base_order", user, EorderConstants.PROC_AUTH_TYPE_SEARCH);
            sql = initBasicUnionSearch(sql,param,relationMap,user);
            sql.append(" group by "+ headSQL1() + selectFields );
            sql.append(") )A order by P_ORDER_TYPE DESC,P_TOTAL_DURATION DESC,CREATE_TIME desc ");
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
        return sql;
    }

    /**
     * 全部、部门、我的历史工单SQL
     * @param param
     * @param user
     * @return
     */
    public static EasySQL getOrderHisListSql(JSONObject param, UserModel user){
        String dept = param.getString("dept");
        String proId = param.getString("processId");
        String schema = user.getSchemaName();
        List<EasyRow> records = new ArrayList<EasyRow>();
        EasySQL sql = new EasySQL();
        Map<String, String> relationMap = new HashMap<String, String>();
        try {
            if (StringUtils.isNotBlank(proId)) {
                records = QueryProcessUtil.getDbField(user, proId);
            }
            
//            String selectFields = "c_bo_base_order.ORDER_NO, "+desenCaller("c_bo_base_order.CALLER")+", C6.ORDER_TYPE, C6.APPEAL_SOURCE, C6.SOURCE_NO, c_bo_base_order.PROVINCE, c_bo_base_order.CUIBAN_NUM, ";
//			// 创建人账号、创建时间、当前处理人、上一环节处理时间、归档时间、是否退赔、退赔账号
//			selectFields += "c_bo_base_order.CREATE_ACC, c_bo_base_order.CREATE_TIME, c_bo_order_ex.CURR_HANDLER, c_bo_order_ex.DEAL_TIME, c_bo_base_order.END_TIME, C6.IS_REFUND,"+desenCaller("C6.REFUND_ACC")+", ";
            String selectFields = "";

            // 退赔金额、退赔原因、 工单状态、联系号码、 投诉内容
			selectFields += "C8.REFUND_NUM, C6.REFUND_REASON, c_bo_base_order.STATUS, C6.EX_2, C6.REGION, ";
			// 一级分类、二级分类、三级分类、创建人姓名、处理人姓名、功能类型、用户类型、处理进度、工单处理时长
			selectFields += "C6.FIRST_TYPE_NAME, C6.SECOND_TYPE_NAME, C6.THIRD_TYPE_NAME, c_bo_base_order.CREATE_NAME, c_bo_order_ex.CURR_HANDLER, C6.PRODUCT_TYPE, ";
			selectFields += "C6.USER_ATTR, C6.HANDLE_PROC, c_bo_order_ex.TOTAL_DURATION_EX, c_bo_order_ex.CLAIM_TIME";
            initField(new EasySQL(),user,records,relationMap, proId);
			// sql
			sql.append("select");
			sql.append(desenSQL(user) + selectFields);
			sql.append(", GROUP_CONCAT(C7.PRODUCT_NAME) PRODUCT_NAME");
            if(Integer.parseInt(dept) != 1) {
            	sql.append(",C2.ORDER_ID AS Oid , 'false' AS IS_HIS ");
            	selectFields += ",Oid , IS_HIS ";
            }
            
            PorderBusiSql.getBusiField(sql);
        	selectFields += ",P_ORDER_TYPE , P_TOTAL_DURATION, COLOR, FONT_COLOR";
            sql.append(" from " + schema + ".C_BO_BASE_ORDER_HIS c_bo_base_order ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
            if(Integer.parseInt(dept)==0 || Integer.parseInt(dept)==1) {
                sql.append(" LEFT JOIN " + schema +".C_BO_ORDER_FOLLOW C1 on c_bo_base_order.ID=C1.ORDER_ID ");
            }
            if(Integer.parseInt(dept) != 1) {
                sql.append(" LEFT JOIN "  + schema +".C_BO_ORDER_ATTENTION C2 ON c_bo_base_order.ID=C2.ORDER_ID ");
                sql.append(user.getUserAcc()," and C2.CREATE_USER =? ");
            }
            //关联业务表
			sql.append("left join "+ schema +".c_box_music_standard_base C6 on c_bo_base_order.ID=C6.M_ID");
			sql.append("left join "+ schema +".c_box_music_product C7 on c_bo_base_order.ORDER_NO=C7.ORDER_NO");
			sql.append("left join "+ schema +".c_box_music_standard_ex C8 on c_bo_base_order.ID=C8.M_ID");
            sql.append("left join "+ schema + ".c_bo_order_follow C9 on c_bo_base_order.ID = C9.ORDER_ID");

            sql.append(" where 1=1 ");
            if (Integer.parseInt(dept)== 0) {
                sql.appendRLike(user.getDeptCode(), " and (C1.CREATE_DEPT_CODE like ? ");
                sql.appendRLike(user.getDeptCode(), " or c_bo_order_ex.CURR_HANDLE_GROUP like ?) ");
            } else if (Integer.parseInt(dept) == 1) {
                sql.append(user.getUserAcc()," and (C1.CREATE_ACC = ? ");
                sql.append(user.getUserAcc()," or c_bo_order_ex.CURR_HANDLER = ? )");
            } else if (Integer.parseInt(dept) == 3) {
                sql.appendRLike(user.getDeptProvinceCode()," and c_bo_base_order.PROVINCE like ?");
            }
            String ids = param.getString("phrase");
            if(StringUtils.isNotBlank(ids)) {
                sql.appendIn(ids.split(",")," and c_bo_base_order.ID  ");
            }
            appendSearchAuthSearch(sql, "c_bo_base_order", user, EorderConstants.PROC_AUTH_TYPE_SEARCH);

            //sql尾部公有SQL
            sql = initBasicSearch(sql,param,relationMap,user);
            sql.append(" and c_bo_base_order.PROC_INST_ID IS NOT NULL AND c_bo_base_order.PROC_INST_ID <> '' ");
            sql.append("group by " + headSQL() + selectFields);
            sql.append(") )A order by P_ORDER_TYPE DESC,P_TOTAL_DURATION DESC,CREATE_TIME desc ");
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
        return sql;
    }

    public static EasySQL getOrderHisProductListSql(JSONObject param, UserModel user){
        String dept = param.getString("dept");
        String proId = param.getString("processId");
        String schema = user.getSchemaName();
        List<EasyRow> records = new ArrayList<EasyRow>();
        EasySQL sql = new EasySQL();
        Map<String, String> relationMap = new HashMap<String, String>();
        try {
            if (StringUtils.isNotBlank(proId)) {
                records = QueryProcessUtil.getDbField(user, proId);
            }
//            String selectFields = "c_bo_base_order.ORDER_NO, c_bo_base_order.CALLER, C6.ORDER_TYPE, C6.APPEAL_SOURCE, C6.SOURCE_NO, c_bo_base_order.PROVINCE, c_bo_base_order.CUIBAN_NUM, ";
//            // 创建人账号、创建时间、当前处理人、上一环节处理时间、归档时间、是否退赔、退赔账号
//            selectFields += "c_bo_base_order.CREATE_ACC, c_bo_base_order.CREATE_TIME, c_bo_order_ex.CURR_HANDLER, c_bo_order_ex.DEAL_TIME, c_bo_base_order.END_TIME, C6.IS_REFUND,C6.REFUND_ACC, ";
//
//
//            selectFields += "GROUP_CONCAT(CASE WHEN C9.TYPE = '33' THEN C9.CONTENT ELSE NULL END)  CONTENT," ;
//            selectFields += "GROUP_CONCAT(CASE WHEN C9.TYPE = '66' THEN C9.CONTENT ELSE NULL END)  AUTO_REPLAY_MSG," ;
            String selectFields = "";
//            String selectFields = "c_bo_base_order.ORDER_NO, c_bo_base_order.CALLER, C6.ORDER_TYPE, C6.APPEAL_SOURCE, C6.SOURCE_NO, c_bo_base_order.PROVINCE, c_bo_base_order.CUIBAN_NUM, ";
//			// 创建人账号、创建时间、当前处理人、上一环节处理时间、归档时间、是否退赔、退赔账号
//			selectFields += "c_bo_base_order.CREATE_ACC, c_bo_base_order.CREATE_TIME, c_bo_order_ex.CURR_HANDLER, c_bo_order_ex.DEAL_TIME, c_bo_base_order.END_TIME, C6.IS_REFUND, C6.REFUND_ACC, ";
			// 退赔金额、退赔原因、 工单状态、联系号码、 投诉内容
			selectFields += "C8.REFUND_NUM, C6.REFUND_REASON, c_bo_base_order.STATUS, C6.EX_2, C6.REGION,  ";
			// 一级分类、二级分类、三级分类、创建人姓名、处理人姓名、功能类型、用户类型、处理进度、工单处理时长
			selectFields += "C6.FIRST_TYPE_NAME, C6.SECOND_TYPE_NAME, C6.THIRD_TYPE_NAME, c_bo_base_order.CREATE_NAME, C8.HANDLE_AGENT_ACC, C6.PRODUCT_TYPE, ";
			selectFields += "C6.USER_ATTR, C6.HANDLE_PROC, c_bo_order_ex.TOTAL_DURATION_EX, c_bo_order_ex.CLAIM_TIME";
			selectFields += ", C7.CLAIMANT, C7.PRODUCT_ID, C7.ORDER_CHANNEL, C7.ORDER_TIME, C7.PRODUCT_TAG, C7.PRODUCT_DEPT";
            initField(new EasySQL(),user,records,relationMap, proId);
			// sql
			sql.append("select");
			sql.append(desenSQL1(user) + selectFields);
            if(Integer.parseInt(dept) != 1) {
            	sql.append(",C2.ORDER_ID AS Oid , 'false' AS IS_HIS ");
            	selectFields += ",Oid , IS_HIS ";
            }
            PorderBusiSql.getBusiField(sql);
        	selectFields += ",P_ORDER_TYPE , P_TOTAL_DURATION, COLOR, FONT_COLOR";
            sql.append(" from " + schema + ".C_BO_BASE_ORDER_HIS c_bo_base_order ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
            if(Integer.parseInt(dept)==0 || Integer.parseInt(dept)==1) {
                sql.append(" LEFT JOIN " + schema +".C_BO_ORDER_FOLLOW C1 on c_bo_base_order.ID=C1.ORDER_ID ");
            }
            if(Integer.parseInt(dept) != 1) {
                sql.append(" LEFT JOIN "  + schema +".C_BO_ORDER_ATTENTION C2 ON c_bo_base_order.ID=C2.ORDER_ID ");
                sql.append(user.getUserAcc()," and C2.CREATE_USER =? ");
            }
            //关联业务表
			sql.append("left join "+ schema +".c_box_music_standard_base C6 on c_bo_base_order.ID=C6.M_ID");
			sql.append("left join "+ schema +".c_box_music_product C7 on c_bo_base_order.ORDER_NO=C7.ORDER_NO");
			sql.append("left join "+ schema +".c_box_music_standard_ex C8 on c_bo_base_order.ID=C8.M_ID");
            sql.append("left join "+ schema + ".c_bo_order_follow C9 on c_bo_base_order.ID = C9.ORDER_ID");

            sql.append(" where 1=1 ");
            if (Integer.parseInt(dept)== 0) {
                sql.appendRLike(user.getDeptCode(), " and (C1.CREATE_DEPT_CODE like ? ");
                sql.appendRLike(user.getDeptCode(), " or c_bo_order_ex.CURR_HANDLE_GROUP like ?) ");
            } else if (Integer.parseInt(dept) == 1) {
                sql.append(user.getUserAcc()," and (C1.CREATE_ACC = ? ");
                sql.append(user.getUserAcc()," or c_bo_order_ex.CURR_HANDLER = ? )");
            } else if (Integer.parseInt(dept) == 3) {
                sql.appendRLike(user.getDeptProvinceCode()," and c_bo_base_order.PROVINCE like ?");
            }
            String ids = param.getString("phrase");
            if(StringUtils.isNotBlank(ids)) {
                sql.appendIn(ids.split(",")," and c_bo_base_order.ID  ");
            }
            appendSearchAuthSearch(sql, "c_bo_base_order", user, EorderConstants.PROC_AUTH_TYPE_SEARCH);
            //sql尾部公有SQL
            sql = initBasicSearch(sql,param,relationMap,user);
            sql.append(" group by "+ headSQL1() + selectFields );
            sql.append(") )A group by "+ headSQL1() + selectFields + " order by P_ORDER_TYPE DESC,P_TOTAL_DURATION DESC,CREATE_TIME desc ");
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
        return sql;
    }

    /**
     * 转派工单SQL
     * @param param
     * @param user
     * @return
     */
    public static EasySQL getTransSql(JSONObject param, UserModel user){
        String proId = param.getString("processId");
        String tagSelect = param.getString("tagSelect");
        String schema = user.getSchemaName();
        List<EasyRow> records = new ArrayList<EasyRow>();
        EasySQL sql = new EasySQL();
        Map<String, String> relationMap = new HashMap<String, String>();
        YCUserPrincipal principal = user.getYcUserPrincipal();
        boolean sign=principal.isResource("cc-erder-manage-transferOrderList-jurisdiction");
        try {
            if (StringUtils.isNotBlank(proId)) {
                // 有流程分类
                records = QueryProcessUtil.getDbField(user, proId);
            }
            //初始字段
            sql = initField(sql,user,records,relationMap, proId);
            sql.append(" ,C2.TAG_NAME,C1.ID_ AS TASK_ID ");
            sql.append(" from " + schema + ".C_BO_BASE_ORDER c_bo_base_order ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
            if(!sign){
                sql.append(user.getUserAcc(), " INNER JOIN ACT_RU_TASK C1 on C1.PROC_INST_ID_=c_bo_base_order.PROC_INST_ID  AND C1.ASSIGNEE_=? ");
            }else{
                sql.append("  INNER JOIN ACT_RU_TASK C1 on C1.PROC_INST_ID_=c_bo_base_order.PROC_INST_ID ");
            }
            sql.append(" LEFT JOIN " + schema +".C_BO_ORDER_PRIVATE_EXT C2 on C2.ORDER_ID=c_bo_base_order.ID ");
            //关联业务表
            appendBusiTable(sql,schema,records,relationMap);
            if (StringUtils.isNotBlank(tagSelect)) {
                sql.append("LEFT JOIN " + schema + ".C_BO_ORDER_TAG_REF C0 on C0.ORDER_ID=c_bo_base_order.ID");
            }
            sql.append(" where 1=1 ");
            sql.append(OrderextConstants.ORDER_STATUS_HANDLE, "AND c_bo_base_order.STATUS = ?");
            if(!sign){
                sql.append(param.getString("createAcc"), " and c_bo_base_order.CREATE_ACC = ? ");
            }
            sql.append(tagSelect, " and C0.TAG_ID = ? ");
            sql = initBasicSearch(sql,param,relationMap,user);
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
        return sql;
    }

    public static EasySQL getAttentionSql(JSONObject param, UserModel user){
        String proId = param.getString("processId");
        String schema = user.getSchemaName();
        List<EasyRow> records = new ArrayList<EasyRow>();
        EasySQL sql = new EasySQL();
        Map<String, String> relationMap = new HashMap<String, String>();
        try {
            if (StringUtils.isNotBlank(proId)) {
                records = QueryProcessUtil.getDbField(user, proId);
            }
            //初始字段
            sql.append("SELECT distinct * FROM(( ");
            sql = initField(sql,user,records,relationMap, proId);
            sql.append(",C1.ID AS ATTENTION_ID ");
            sql.append(" FROM " + schema + ".C_BO_ORDER_ATTENTION C1 ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_BASE_ORDER c_bo_base_order on  C1.ORDER_ID = c_bo_base_order.ID ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
            //关联业务表
            appendBusiTable(sql,schema,records,relationMap);
            sql.append(" where 1=1 ");
            sql.append(user.getUserAcc(), "AND C1.CREATE_USER = ?");
            //sql尾部公有SQL
            sql = initBasicUnionSearch(sql,param,relationMap,user);
            sql.append(")");
            sql.append(" union all (");
            sql = initField(sql,user,records,relationMap, proId);
            sql.append(",C1.ID AS ATTENTION_ID ");
            sql.append(" FROM " + schema + ".C_BO_ORDER_ATTENTION C1 ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_BASE_ORDER_HIS c_bo_base_order on  C1.ORDER_ID = c_bo_base_order.ID ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
            //关联业务表
            appendBusiTable(sql,schema,records,relationMap);
            sql.append(" where 1=1 ");
            sql.append(user.getUserAcc(), "AND C1.CREATE_USER = ?");
            //sql尾部公有SQL
            sql = initBasicUnionSearch(sql,param,relationMap,user);
            sql.append(") )A ORDER BY CREATE_TIME DESC");
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
        return sql;
    }

    public static EasySQL getEndCloseSql(JSONObject param, UserModel user){
        String proId = param.getString("processId");
        String schema = user.getSchemaName();
        List<EasyRow> records = new ArrayList<EasyRow>();
        EasySQL sql = new EasySQL();
        Map<String, String> relationMap = new HashMap<String, String>();
        try {
            if (StringUtils.isNotBlank(proId)) {
                records = QueryProcessUtil.getDbField(user, proId);
            }
            //初始字段
            sql.append("SELECT distinct * FROM(( ");
            sql = initField(sql,user,records,relationMap, proId);
            sql.append(" from "+schema +".C_BO_BASE_ORDER_HIS  c_bo_base_order ");
            sql.append(" LEFT JOIN "+schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
            //关联业务表
            appendBusiTable(sql,schema,records,relationMap);
            sql.append(" where 1=1 ");
            sql.append(param.getString("FLOW_KEY"), " and c_bo_base_order.PROC_KEY = ? ");
            sql.append(" AND c_bo_base_order.STATUS  in ('" + EorderConstants.ORDER_STATUS_DONE + "', '" +  EorderConstants.ORDER_STATUS_CLOSED + "') ");
            sql.appendRLike(param.getString("ORDER_NO"), " AND c_bo_base_order.ORDER_NO LIKE ? ");
            sql.appendRLike(param.getString("caller")," and c_bo_base_order.CALLER like ? ");
            sql.appendLike(param.getString("PROC_NAME"), " AND c_bo_base_order.PROC_NAME LIKE ? ");
            sql.appendLike(param.getString("orderTitle")," and c_bo_base_order.ORDER_TITLE like ?");
            //sql尾部公有SQL
            sql = initBasicUnionSearch(sql,param,relationMap,user);
            sql.append(")");
            sql.append(" union all (");
            sql = initField(sql,user,records,relationMap, proId);
            sql.append(" from "+schema +".C_BO_BASE_ORDER  c_bo_base_order ");
            sql.append(" LEFT JOIN "+schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
            //关联业务表
            appendBusiTable(sql,schema,records,relationMap);
            sql.append(" where 1=1 ");
            sql.append(param.getString("FLOW_KEY"), " and c_bo_base_order.PROC_KEY = ? ");
            sql.append(" AND c_bo_base_order.STATUS  in ('" + EorderConstants.ORDER_STATUS_DONE + "', '" +  EorderConstants.ORDER_STATUS_CLOSED + "') ");
            sql.appendRLike(param.getString("ORDER_NO"), " AND c_bo_base_order.ORDER_NO LIKE ? ");
            sql.appendRLike(param.getString("caller")," and c_bo_base_order.CALLER like ? ");
            sql.appendLike(param.getString("PROC_NAME"), " AND c_bo_base_order.PROC_NAME LIKE ? ");
            sql.appendLike(param.getString("orderTitle")," and c_bo_base_order.ORDER_TITLE like ?");
            //sql尾部公有SQL
            sql = initBasicUnionSearch(sql,param,relationMap,user);
            sql.append(") )A ORDER BY CREATE_TIME DESC");
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
        return sql;
    }

    /**
     * 运行中SQL
     * @param param
     * @param user
     * @return
     */
    public static EasySQL getProcessSql(JSONObject param, UserModel user){
        String proId = param.getString("processId");
        String schema = user.getSchemaName();
        List<EasyRow> records = new ArrayList<EasyRow>();
        EasySQL sql = new EasySQL();
        Map<String, String> relationMap = new HashMap<String, String>();
        try {
            if (StringUtils.isNotBlank(proId)) {
                // 有流程分类
                records = QueryProcessUtil.getDbField(user, proId);
            }
            //初始字段
            sql = initField(sql,user,records,relationMap, proId);
            sql.append(" from " + schema + ".C_BO_BASE_ORDER c_bo_base_order ");
            sql.append(" LEFT JOIN " + schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
            //关联业务表
            appendBusiTable(sql,schema,records,relationMap);
            sql.append(" where 1=1 ");
            sql.append(OrderextConstants.ORDER_STATUS_HANDLE, "AND c_bo_base_order.STATUS = ?");
            sql.append(OrderextConstants.ORDER_STATUS_DEL, "AND c_bo_base_order.STATUS != ?");
            sql.append(param.getString("PROC_DEF_ID"), " AND c_bo_base_order.PROC_DEF_ID=? ");
            sql = initBasicSearch(sql,param,relationMap,user);
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
        return sql;
    }

    /**
     * 获取表对应关系
     * @param procId
     * @param user
     * @return not null
     * @throws SQLException
     */
    private static Map<String,String> getRelationMap(String procId,UserModel user) throws SQLException {
        Map<String, String> relationMap = new HashMap<String, String>();
        relationMap.put("c_bo_base_order","c_bo_base_order");
        relationMap.put("c_bo_order_ex","c_bo_order_ex");
        if (StringUtils.isNotBlank(procId)) {
           List<EasyRow> records = QueryProcessUtil.getDbField(user, procId);
           if(Objects.nonNull(records)) {
               records.stream().forEach(record -> {
                   String dbName = record.getColumnValue("DB_NAME");
                   String dbTableName = record.getColumnValue("DB_TABLE_NAME");
                   if (StringUtils.isNotBlank(dbTableName)) {
                       relationMap.put(dbName, dbTableName);
                   }
               });
           }
        }
        return relationMap;
    }

    public static EasySQL initField(EasySQL sql,UserModel user,List<EasyRow> records,Map relationMap, String procKey) throws SQLException {
        List<JSONObject> queryForList = new ArrayList<>();
        OrderNoticeCfg cfg = OrderGlobalCfgService.getCache(user.getEpCode(), user.getBusiOrderId());
        String baseCfg = cfg.getBaseCfg();
        JSONObject cfgObj = StringUtils.isNotBlank(baseCfg)?JSONObject.parseObject(baseCfg):null;
        JSONArray fields = cfgObj != null?cfgObj.getJSONArray("COLUMN_CONFIG"):null;
        JSONArray tables = cfgObj != null?cfgObj.getJSONArray("BASE_TABLES"):null;
        //基础字段--必需
        sql.append(" select DISTINCT c_bo_base_order.ORDER_CHARGE, c_bo_base_order.QC_STATE, c_bo_base_order.QC_TASK_ID, " +
        		"c_bo_base_order.SOURCE_TYPE,c_bo_base_order.SOURCE_RECORD_ID," +
                "c_bo_base_order.SESSION_ID,c_bo_base_order.ID,c_bo_base_order.ORDER_NO,c_bo_base_order.PROC_INST_ID," +
                "c_bo_base_order.PROC_KEY,c_bo_base_order.PROC_DEF_ID,c_bo_base_order.STATUS,c_bo_base_order.BUSI_STATUS," +
                "c_bo_base_order.CUIBAN_NUM,c_bo_base_order.OVERSEE_NUM,c_bo_base_order.NOTE_NUM,c_bo_base_order.CALL_ID," +
                "c_bo_base_order.BAKUP_NUM,c_bo_base_order.TIME_LIMIT,c_bo_base_order.ORDER_LIMIT_TIME," +
                "c_bo_base_order.END_TIME, c_bo_base_order.PROC_NAME AS NAME ,c_bo_base_order.CREATE_TIME,c_bo_base_order.CALLER,");
        sql.append("(CASE  WHEN c_bo_order_ex.IS_READ IS NULL THEN c_bo_base_order.IS_READ ELSE c_bo_order_ex.IS_READ END) IS_READ," +
                "(CASE  WHEN c_bo_order_ex.DEAL_ACC IS NULL THEN c_bo_base_order.DEAL_ACC ELSE c_bo_order_ex.DEAL_ACC END) DEAL_ACC,"+
                "(CASE  WHEN c_bo_order_ex.DURATION IS NULL THEN c_bo_base_order.DURATION ELSE c_bo_order_ex.DURATION END) DURATION," +
                "(CASE  WHEN c_bo_order_ex.TOTAL_DURATION IS NULL THEN c_bo_base_order.TOTAL_DURATION ELSE c_bo_order_ex.TOTAL_DURATION END) TOTAL_DURATION,"+
                "(CASE  WHEN c_bo_order_ex.DURATION_EX IS NULL THEN c_bo_base_order.DURATION_EX ELSE c_bo_order_ex.DURATION_EX END) DURATION_EX," +
                "(CASE  WHEN c_bo_order_ex.TOTAL_DURATION_EX IS NULL THEN c_bo_base_order.TOTAL_DURATION_EX ELSE c_bo_order_ex.TOTAL_DURATION_EX END) TOTAL_DURATION_EX,"+
                "(CASE  WHEN c_bo_order_ex.IS_HALT IS NULL THEN c_bo_base_order.IS_HALT ELSE c_bo_order_ex.IS_HALT END) IS_HALT," +
                "(CASE  WHEN c_bo_order_ex.HALT_LONG IS NULL THEN c_bo_base_order.HALT_LONG ELSE c_bo_order_ex.HALT_LONG END) HALT_LONG," +
                "c_bo_order_ex.HALT_TYPE");
        //基础表字段--动态字段
        if (fields != null) {
            for (int i = 0; i < tables.size(); i++) {
                relationMap.put(tables.getString(i),tables.getString(i));
            }
            for (int i = 0; i < fields.size(); i++) {
                JSONObject field = fields.getJSONObject(i);
                if (field != null && OrderextConstants.SF_YN_Y.equals(field.getString("IS_SHOW_FIELD"))) {
                    String hashName = OrderBusiUtil.getAlias("A",field.getString("AUTO_FORM_DB_ID"));
                    // 是否加密
                    String enableCrypt = field.getString("ENABLE_CRYPT");
                    // 如果是加密字段，则需要加上E_前缀表示需要解密
                    if ("Y".equals(enableCrypt)) {
                        sql.append("," + field.getString("AUTO_FORM_DB_ID") + "." + field.getString("FIELD_NAME") + " as E_" + hashName + "_" + field.getString("FIELD_NAME"));
                    } else {
                        sql.append("," + field.getString("AUTO_FORM_DB_ID") + "." + field.getString("FIELD_NAME") + " as " + hashName + "_" + field.getString("FIELD_NAME"));
                    }
                }
            }
        }
        //业务表字段--动态字段
        for (int i = 0; i < records.size(); i++) {
            EasyRow record = records.get(i);
            if (record != null) {
                String dbName = record.getColumnValue("DB_NAME");
                String dbTableName = record.getColumnValue("DB_TABLE_NAME");
                // 查询要显示的字段
                queryForList = QueryProcessUtil.getFieldList(user, dbName, procKey);
                if (StringUtils.isNotBlank(dbTableName)) {
                    // 遍历list
                    for (JSONObject json : queryForList) {
                    	// 是否加密
                    	String enableCrypt = json.getString("ENABLE_CRYPT");
                        String fieldName = json.getString("FIELD_NAME");
                        String hashName = OrderBusiUtil.getAlias("A",json.getString("AUTO_FORM_DB_ID"));
                        // 如果是加密字段，则需要加上E_前缀表示需要解密
                        if ("Y".equals(enableCrypt)) {
                        	sql.append(",C" + (6 + i) + "." + fieldName + " as E_" + hashName + "_" + fieldName);
                        } else {
                            sql.append(",C" + (6 + i) + "." + fieldName + " as " + hashName + "_" + fieldName);
                        }
                    }
                    relationMap.put(dbName, "C" + (6 + i));
                }
            }
        }
        return sql;
    }

    public static EasySQL appendBusiTable(EasySQL sql,String schema,List<EasyRow> records,Map relationMap){
        for (int i = 0; i < records.size(); i++) {
            EasyRow record = records.get(i);
            String dbName = record.getColumnValue("DB_NAME");
            String dbTableName = record.getColumnValue("DB_TABLE_NAME");
            if (StringUtils.isNotBlank(dbTableName)) {
                sql.append(" LEFT JOIN " + schema + "." + dbTableName +" " + relationMap.get(dbName) + " on "
                        + relationMap.get(dbName) + ".M_ID=c_bo_base_order.ID");
            }
        }
        return sql;
    }

    /**
     * 拼接办理权限sql条件
     * @param sql
     * @param ruTaskTableTag	运行中任务表的别名
     * @param user
     * @return
     */
    public static void appendCompleteAuthSearch(EasySQL sql, String ruTaskTableTag, UserModel user, String authType) {
    	if (DictConstants.DICT_SY_YN_Y.equals(EorderConstants.getEnableOrderCompleteAuth())) {
            Set<String> nodeKeys = OrderAuthCache.getInstance().getNodeKeysByAuth(user, authType);
            sql.append("and "+ ruTaskTableTag +".TASK_DEF_KEY_ in ('-1'");
            for (String nodeKey:nodeKeys) {
            	sql.append(nodeKey, ", ?");
            }
            sql.append(")");
    	}
    }

    /**
     * 拼接查看权限sql条件
     * @param sql
     * @param tableTag	主表的别名
     * @param user
     * @return
     */
    public static void appendSearchAuthSearch(EasySQL sql, String tableTag, UserModel user, String authType) {
    	if (DictConstants.DICT_SY_YN_Y.equals(EorderConstants.getEnableOrderSearchAuth())) {
    		ProcAuthCache procAuthCache = new ProcAuthCache();
            Set<String> procKeys = procAuthCache.getFlowKeysByAuth(user, authType);
            sql.append("and "+ tableTag +".PROC_KEY in ('-1'");
            for (String procKey:procKeys) {
            	sql.append(procKey, ", ?");
            }
            sql.append(")");
    	}
    }

    public static EasySQL initDoBasicSearch(EasySQL sql,JSONObject param,Map relationMap,UserModel user){
    	return initDoBasicSearch(sql, param, relationMap, user, null);
    }
    public static EasySQL initDoBasicSearch(EasySQL sql,JSONObject param,Map relationMap,UserModel user, DBTypes dbTypes){
        String proId = param.getString("processId");
        String entId = user.getEpCode();
        String busiOrderId = user.getBusiOrderId();
        sql.append(proId, " and c_bo_base_order.PROC_KEY = ? ");
        sql.append(entId, " and c_bo_base_order.EP_CODE = ? ");
        sql.append(busiOrderId, " and c_bo_base_order.BUSI_ORDER_ID = ? ");
        sql.appendRLike(param.getString("orderNo"), " and c_bo_base_order.ORDER_NO like ? ");

        //工单编号后缀查询  20220520 chenzhibin
        sql.append(param.getString("orderNoSuffix"), " and c_bo_base_order.ORDER_CODE = ? ");

        if(StringUtils.isBlank(param.getString("orderNo"))&&StringUtils.isBlank(param.getString("orderNoSuffix"))){
            sql.append(param.getString("startDate"), " and c_bo_base_order.ARRIVE_TIME >= ? ");
            sql.append(param.getString("endDate"), " and c_bo_base_order.ARRIVE_TIME <= ? ");
        }
        logger.info("调用extendQuery之前param: "+ param);
        logger.info("调用extendQuery之前relationMap: "+ JSONObject.toJSONString(relationMap));
        QueryProcessUtil.extendQuery(param, sql, relationMap,user, proId);
        extendViewCondition(sql,param,relationMap,user);
        if (dbTypes == DBTypes.PostgreSql) {
            sql.append("group by c_bo_base_order.CREATE_TIME");
        }
        
        sql.append("order by P_ORDER_TYPE DESC,P_TOTAL_DURATION DESC,c_bo_base_order.CREATE_TIME desc ");
        
        logger.info("加了条件的sql: "+ sql.getSQL() +"; param: "+ JSONObject.toJSONString(sql.getParams()));
        return sql;
    }
    public static EasySQL initDoBasicSearchCount(EasySQL sql,JSONObject param,Map relationMap,UserModel user){
        String proId = param.getString("processId");
        String entId = user.getEpCode();
        String busiOrderId = user.getBusiOrderId();
        sql.append(proId, " and c_bo_base_order.PROC_KEY = ? ");
        sql.append(entId, " and c_bo_base_order.EP_CODE = ? ");
        sql.append(busiOrderId, " and c_bo_base_order.BUSI_ORDER_ID = ? ");
        sql.appendRLike(param.getString("orderNo"), " and c_bo_base_order.ORDER_NO like ? ");

        //工单编号后缀查询  20220520 chenzhibin
        sql.append(param.getString("orderNoSuffix"), " and c_bo_base_order.ORDER_CODE = ? ");

        if(StringUtils.isBlank(param.getString("orderNo"))&&StringUtils.isBlank(param.getString("orderNoSuffix"))){
            sql.append(param.getString("startDate"), " and c_bo_base_order.ARRIVE_TIME >= ? ");
            sql.append(param.getString("endDate"), " and c_bo_base_order.ARRIVE_TIME <= ? ");
        }
        QueryProcessUtil.extendQuery(param, sql, relationMap,user, proId);
        extendViewCondition(sql,param,relationMap,user);
        return sql;
    }

    public static EasySQL initBasicSearch(EasySQL sql,JSONObject param,Map relationMap,UserModel user){
    	return initBasicSearch(sql, param, relationMap, user, null);
    }
    public static EasySQL initBasicSearch(EasySQL sql,JSONObject param,Map relationMap,UserModel user, DBTypes dbTypes){
        String proId = param.getString("processId");
        String entId = user.getEpCode();
        String busiOrderId = user.getBusiOrderId();
        sql.append(proId, " and c_bo_base_order.PROC_KEY = ? ");
        sql.append(entId, " and c_bo_base_order.EP_CODE = ? ");
        sql.append(busiOrderId, " and c_bo_base_order.BUSI_ORDER_ID = ? ");
        sql.appendRLike(param.getString("orderNo"), " and c_bo_base_order.ORDER_NO like ? ");

        //工单编号后缀查询  20220520 chenzhibin
        sql.append(param.getString("orderNoSuffix"), " and c_bo_base_order.ORDER_CODE = ? ");

        if(StringUtils.isBlank(param.getString("orderNo"))&&StringUtils.isBlank(param.getString("orderNoSuffix"))){
            sql.append(param.getString("startDate"), " and c_bo_base_order.CREATE_TIME >= ? ");
            sql.append(param.getString("endDate"), " and c_bo_base_order.CREATE_TIME <= ? ");
        }
        
        if (StringUtils.isNotBlank(param.getString("prov"))) {
        	sql.append(user.getDeptProvinceCode(), " and c_bo_base_order.PROVINCE = ? ", false);
        }
        QueryProcessUtil.extendQuery(param, sql, relationMap,user, proId);
        extendViewCondition(sql,param,relationMap,user);
        return sql;
    }
    public static EasySQL initBasicSearchCount(EasySQL sql,JSONObject param,Map relationMap,UserModel user){
        String proId = param.getString("processId");
        String entId = user.getEpCode();
        String busiOrderId = user.getBusiOrderId();
        sql.append(proId, " and c_bo_base_order.PROC_KEY = ? ");
        sql.append(entId, " and c_bo_base_order.EP_CODE = ? ");
        sql.append(busiOrderId, " and c_bo_base_order.BUSI_ORDER_ID = ? ");
        sql.appendRLike(param.getString("orderNo"), " and c_bo_base_order.ORDER_NO like ? ");

        //工单编号后缀查询  20220520 chenzhibin
        sql.append(param.getString("orderNoSuffix"), " and c_bo_base_order.ORDER_CODE = ? ");

        if(StringUtils.isBlank(param.getString("orderNo"))&&StringUtils.isBlank(param.getString("orderNoSuffix"))){
            sql.append(param.getString("startDate"), " and c_bo_base_order.CREATE_TIME >= ? ");
            sql.append(param.getString("endDate"), " and c_bo_base_order.CREATE_TIME <= ? ");
        }
        
        if (StringUtils.isNotBlank(param.getString("prov"))) {
        	sql.append(user.getDeptProvinceCode(), " and c_bo_base_order.PROVINCE = ? ", false);
        }
        QueryProcessUtil.extendQuery(param, sql, relationMap,user, proId);
        extendViewCondition(sql,param,relationMap,user);
        return sql;
    }

    private static EasySQL initBasicUnionSearch(EasySQL sql,JSONObject param,Map relationMap,UserModel user){
        String proId = param.getString("processId");
        String entId = user.getEpCode();
        String busiOrderId = user.getBusiOrderId();
        sql.append(proId, " and c_bo_base_order.PROC_KEY = ? ");
        sql.append(entId, " and c_bo_base_order.EP_CODE = ? ");
        sql.append(busiOrderId, " and c_bo_base_order.BUSI_ORDER_ID = ? ");
        sql.appendRLike(param.getString("orderNo"), " and c_bo_base_order.ORDER_NO like ? ");

        //工单编号后缀查询  20220520 chenzhibin
        sql.append(param.getString("orderNoSuffix"), " and c_bo_base_order.ORDER_CODE = ? ");

        if(StringUtils.isBlank(param.getString("orderNo"))&&StringUtils.isBlank(param.getString("orderNoSuffix"))){
            sql.append(param.getString("startDate"), " and c_bo_base_order.CREATE_TIME >= ? ");
            sql.append(param.getString("endDate"), " and c_bo_base_order.CREATE_TIME <= ? ");
        }


        if (StringUtils.isNotBlank(param.getString("prov"))) {
        	sql.append(user.getDeptProvinceCode(), " and c_bo_base_order.PROVINCE = ? ", false);
        }
        QueryProcessUtil.extendQuery(param, sql, relationMap,user, proId);
        extendViewCondition(sql,param,relationMap,user);
       // sql.append(" order by c_bo_base_order.CREATE_TIME desc");
        return sql;
    }

    private static void extendViewCondition(EasySQL sql,JSONObject param,Map relationMap,UserModel user){
        JSONObject condition = JsonKit.getJSONObject(param, "condition");
        if(Objects.isNull(condition)){
            return;
        }
        JSONObject base = JsonKit.getJSONObject(condition, "base");
        JSONObject busi = JsonKit.getJSONObject(condition, "busi");
        base.entrySet().stream().forEach(item -> {
            String key = item.getKey();
            String field = key.substring(0,key.indexOf("-"));
            String option = key.substring(key.indexOf("-")+1);
            String value = (String)item.getValue();
            initOptionSql(sql,option,field,value);
        });
        busi.entrySet().stream().forEach(item -> {
            String key = item.getKey();
            String tableId = key.substring(0,key.indexOf("."));
            String field = key.substring(key.indexOf(".")+1,key.indexOf("-"));
            field = relationMap.get(tableId)+"."+field;
            String option = key.substring(key.indexOf("-")+1);
            String value = (String)item.getValue();
            initOptionSql(sql,option,field,value);
        });
    }

    private static void initOptionSql(EasySQL sql,String option,String field,String value){
        switch (option){
            case "eq":
                sql.append(value," and " + field +" = ? ");
                break;
            case "!eq":
                sql.append(value," and " + field +" != ? ");
                break;
            case "gt":
                sql.append(value," and " + field +" > ? ");
                break;
            case "lt":
                sql.append(value," and " + field +" < ? ");
                break;
            case "in":
                String[] vals = value.split(",");
                sql.append(value," and " + field +" in  ");
                for (int i=0;i<vals.length;i++) {
                    if(i == 0){
                        sql.append(vals[i]," ( ?");
                    }else if(i == vals.length -1){
                        sql.append(vals[i],"  ,?)");
                    }else{
                        sql.append(vals[i],"  ,?");
                    }
                }
                break;
            default:
                break;
        }
    }


    public static EasySQL initCommonField(EasySQL sql,UserModel user,JSONObject listConfig,Map relationMap) throws SQLException {
        //基础字段--必需
        sql.append(" select DISTINCT c_bo_base_order.ORDER_CHARGE, c_bo_base_order.QC_STATE, c_bo_base_order.QC_TASK_ID, " +
                "c_bo_base_order.SOURCE_TYPE,c_bo_base_order.SOURCE_RECORD_ID," +
                "c_bo_base_order.SESSION_ID,c_bo_base_order.ID,c_bo_base_order.ORDER_NO,c_bo_base_order.PROC_INST_ID," +
                "c_bo_base_order.PROC_KEY,c_bo_base_order.PROC_DEF_ID,c_bo_base_order.STATUS,c_bo_base_order.BUSI_STATUS," +
                "c_bo_base_order.CUIBAN_NUM,c_bo_base_order.OVERSEE_NUM,c_bo_base_order.NOTE_NUM,c_bo_base_order.CALL_ID," +
                "c_bo_base_order.BAKUP_NUM,c_bo_base_order.TIME_LIMIT,c_bo_base_order.ORDER_LIMIT_TIME," +
                "c_bo_base_order.END_TIME, c_bo_base_order.PROC_NAME AS NAME ,c_bo_base_order.CREATE_TIME,c_bo_base_order.CALLER,");
        sql.append("(CASE  WHEN c_bo_order_ex.IS_READ IS NULL THEN c_bo_base_order.IS_READ ELSE c_bo_order_ex.IS_READ END) IS_READ," +
                "(CASE  WHEN c_bo_order_ex.DEAL_ACC IS NULL THEN c_bo_base_order.DEAL_ACC ELSE c_bo_order_ex.DEAL_ACC END) DEAL_ACC,"+
                "(CASE  WHEN c_bo_order_ex.DURATION IS NULL THEN c_bo_base_order.DURATION ELSE c_bo_order_ex.DURATION END) DURATION," +
                "(CASE  WHEN c_bo_order_ex.TOTAL_DURATION IS NULL THEN c_bo_base_order.TOTAL_DURATION ELSE c_bo_order_ex.TOTAL_DURATION END) TOTAL_DURATION,"+
                "(CASE  WHEN c_bo_order_ex.DURATION_EX IS NULL THEN c_bo_base_order.DURATION_EX ELSE c_bo_order_ex.DURATION_EX END) DURATION_EX," +
                "(CASE  WHEN c_bo_order_ex.TOTAL_DURATION_EX IS NULL THEN c_bo_base_order.TOTAL_DURATION_EX ELSE c_bo_order_ex.TOTAL_DURATION_EX END) TOTAL_DURATION_EX,"+
                "(CASE  WHEN c_bo_order_ex.IS_HALT IS NULL THEN c_bo_base_order.IS_HALT ELSE c_bo_order_ex.IS_HALT END) IS_HALT," +
                "(CASE  WHEN c_bo_order_ex.HALT_LONG IS NULL THEN c_bo_base_order.HALT_LONG ELSE c_bo_order_ex.HALT_LONG END) HALT_LONG");
        //基础表字段--动态字段
        if(Objects.nonNull(listConfig)) {
            Optional.ofNullable(listConfig.getJSONArray("SHOW_CONFIG")).ifPresent(configs -> {
                configs.stream().forEach(item -> {
                    JSONObject config = (JSONObject) item;
                    String fieldName = config.getString("FIELD_NAME");
                    String dbId = config.getString("AUTO_FORM_DB_ID");
                    String hashName = OrderBusiUtil.getAlias("A", dbId);
                    sql.append("," + hashName + "." + fieldName + " as " + hashName + "_" + fieldName );
                });
            });
        }
        return sql;
    }

    private static EasySQL initCommonSearch(EasySQL sql,JSONObject param,Map relationMap,UserModel user){
        DBTypes sqlType = QueryFactory.getQuery(user.getEpCode()).getTypes();
        String proId = param.getString("processId");
        String entId = user.getEpCode();
        String busiOrderId = user.getBusiOrderId();
        sql.append(proId, " and c_bo_base_order.PROC_KEY = ? ");
        sql.append(entId, " and c_bo_base_order.EP_CODE = ? ");
        sql.append(busiOrderId, " and c_bo_base_order.BUSI_ORDER_ID = ? ");
        sql.append(param.getString("sessionId")," and c_bo_base_order.SESSION_ID = ? ");
        sql.append(param.getString("orderNo"), " and c_bo_base_order.ORDER_NO = ? ");
        JSONObject exSearch = param.getJSONObject("extSearch");
        if(exSearch != null) {
            JSONObject orSearch = new JSONObject();
            for (Map.Entry<String,Object> entry:exSearch.entrySet()) {
                JSONObject json = (JSONObject) entry.getValue();
                String relation = json.getString("relation");
                if("or".equals(relation)) {
                    orSearch.put(entry.getKey(),json);
                    continue;
                }
                appendExSearch(sql,entry.getKey(),json,sqlType,relation);
            }
            if( !orSearch.isEmpty() && checkExistValue(orSearch)) {
                sql.append(" and ( 1 != 1 ");
                for (Map.Entry<String,Object> entry:orSearch.entrySet()) {
                    JSONObject json = (JSONObject) entry.getValue();
                    String relation = json.getString("relation");
                    appendExSearch(sql,entry.getKey(),json,sqlType,relation);
                }
                sql.append(" ) ");
            }
        }
        return sql;
    }

    /**
     * 判断是否存在查询条件
     * @param search
     * @return
     */
    private static boolean checkExistValue(JSONObject search) {
        boolean flag = false;
        for (Map.Entry<String,Object> entry:search.entrySet()) {
            JSONObject json = (JSONObject) entry.getValue();
            String value = json.getString("value");
            String groups = json.getString("groups");
            if(StringUtils.isNotBlank(value)) {
                flag = true;
                break;
            }
            if ("Y".equals(groups)) {
                String startValue = json.getString("startValue");
                String endValue = json.getString("endValue");
                if(StringUtils.isNotBlank(startValue) || StringUtils.isNotBlank(endValue)) {
                    flag = true;
                    break;
                }
            }

        }
        return flag;
    }

    private static void appendExSearch(EasySQL sql, String Field, JSONObject json, DBTypes sqlType,String relation) {
        String vague = json.getString("vague");
        String groups = json.getString("groups");
        String alias = json.getString("alias");
        if ("Y".equals(groups)) {
            sql.append(getDataValue(json.getString("startValue"),json.getString("dataType"),sqlType), " "+relation+" " + OrderBusiUtil.getAlias("A",alias) + "."+ Field + " >= ? ");
            sql.append(getDataValue(json.getString("endValue"),json.getString("dataType"),sqlType), " "+relation+" " + OrderBusiUtil.getAlias("A",alias) + "."+ Field + " <= ? ");
            return;
        }
        if("02".equals(vague)) {
            sql.appendRLike(getDataValue(json.getString("value"),json.getString("dataType"),sqlType), " "+relation+" " + OrderBusiUtil.getAlias("A",alias) + "."+  Field + " like ? ");
        }else if ("03".equals(vague)){
            sql.appendLLike(getDataValue(json.getString("value"),json.getString("dataType"),sqlType), " "+relation+" " + OrderBusiUtil.getAlias("A",alias) + "."+ Field + " like ? ");
        }else if("04".equals(vague)) {
            sql.appendLike(getDataValue(json.getString("value"),json.getString("dataType"),sqlType), " "+relation+" " + OrderBusiUtil.getAlias("A",alias) + "."+ Field + " like ? ");
        }else {
            sql.append(getDataValue(json.getString("value"),json.getString("dataType"),sqlType), " "+relation+" " + OrderBusiUtil.getAlias("A",alias) + "."+ Field + " = ? ");
        }
    }

    private static Object getDataValue(String value,String dataType, DBTypes sqlType){
        Object obj = value;
        try {
            if(DBTypes.PostgreSql == sqlType) {
                switch (dataType) {
                    case "int2":
                        obj = Integer.parseInt(value);
                        break;
                    case "int4":
                        obj = Integer.parseInt(value);
                        break;
                    case "int8":
                        obj = Integer.parseInt(value);
                        break;
                    case "float4":
                        obj = Float.parseFloat(value);
                        break;
                    case "float8":
                        obj = Double.parseDouble(value);
                        break;
                    case "money":
                        obj = Double.parseDouble(value);
                        break;
                    case "numeric":
                        obj = BigDecimal.valueOf(Double.parseDouble(value));
                        break;
                    case "bit":
                        obj = Boolean.valueOf(value);
                        break;
                    case "bool":
                        obj = Boolean.valueOf(value);
                        break;
                    case "timestamp":
                        obj = Timestamp.valueOf(value);
                        break;
                    default:
                        break;
                }
            }
        }catch (Exception e) {

        }
        return obj;
    }

    private static EasySQL appendCommonBusiTable(EasySQL sql,String schema,Map<String,String> relationMap){
        for (Map.Entry<String,String> entry: relationMap.entrySet()) {
            String dbName = entry.getKey();
            if("c_bo_base_order".equals(dbName) || "c_bo_order_ex".equals(dbName)) {
                continue;
            }
            String alias = OrderBusiUtil.getAlias("A", dbName);
            sql.append(" LEFT JOIN " + schema + "." + entry.getValue() +" " + alias + " on "
                    + alias + ".M_ID=c_bo_base_order.ID");
        }
        return sql;
    }

    /**
     * 新版配置
     * @param param
     * @param user
     * @return
     */
    public static EasySQL getOrderSql(JSONObject param, UserModel user) throws SQLException {
        String proId = param.getString("processId");
        String schema = user.getSchemaName();
        EasySQL sql = new EasySQL();
        Map<String, String> relationMap = getRelationMap(proId,user);
        JSONObject config = QueryProcessUtil.getConfig(proId,param.getString("type"),user);
        Assert.notNull(config,"列表配置为空，不做查询");
        sql.append("SELECT distinct * FROM(( ");
        //初始字段
        sql = initCommonField(sql,user,config,relationMap);
        sql.append(" from "+schema +".C_BO_BASE_ORDER_HIS  c_bo_base_order ");
        sql.append(" LEFT JOIN "+schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
        //关联业务表
        appendCommonBusiTable(sql,schema,relationMap);
        sql.append(" where 1=1 ");
        //sql尾部公有SQL
        initCommonSearch(sql,param,relationMap,user);
        sql.append(")");
        sql.append(" union all (");
        sql = initCommonField(sql,user,config,relationMap);
        sql.append(" from "+schema +".C_BO_BASE_ORDER  c_bo_base_order ");
        sql.append(" LEFT JOIN "+schema + ".C_BO_ORDER_EX c_bo_order_ex ON c_bo_order_ex.ORDER_ID = c_bo_base_order.ID");
        //关联业务表
        appendCommonBusiTable(sql,schema,relationMap);
        sql.append(" where 1=1 ");
        //sql尾部公有SQL
        initCommonSearch(sql,param,relationMap,user);
        sql.append(") )A ORDER BY CREATE_TIME DESC");
        logger.info("getOrderSql:" + sql.getFullSq());
        return sql;
    }


    /**
     * 爱音乐定制脱敏
     * @param Caller 手机号字段名
     * @return
     */
    private static String desenCaller(String Caller, UserModel user) {
        CCEntModuleContext entModuleContext = new CCEntModuleContext(user.getEpCode(), user.getBusiOrderId(), "cx-mix-api");
        boolean numExpProtect = entModuleContext.isNumProtectEmp(user);
        if (numExpProtect) {
            String[] split = Caller.split("\\.");
            String fieldName = split.length >= 2 ?  split[split.length - 1] : Caller;
            return "CONCAT(SUBSTRING(" + Caller +", 1, 5), '****', SUBSTRING(" + Caller + ", 10, LENGTH(" + Caller + ") - 9)) AS " + fieldName;
        } else {
            return Caller;
        }
    }

    private static String headSQL() {
        String selectFields = "c_bo_base_order.ORDER_NO, c_bo_base_order.CALLER, C6.ORDER_TYPE, C6.APPEAL_SOURCE, C6.SOURCE_NO, c_bo_base_order.PROVINCE, c_bo_base_order.CUIBAN_NUM, ";
        // 创建人账号、创建时间、当前处理人、上一环节处理时间、归档时间、是否退赔、退赔账号
        selectFields += "c_bo_base_order.CREATE_ACC, c_bo_base_order.CREATE_TIME, CURR_HANDLER, c_bo_order_ex.DEAL_TIME, c_bo_base_order.END_TIME, C6.IS_REFUND, C6.REFUND_ACC, ";

        return selectFields;
    }

    private static String desenSQL(UserModel user) {
        String selectFields = "c_bo_base_order.ORDER_NO, " +desenCaller("c_bo_base_order.CALLER", user) +", C6.ORDER_TYPE, C6.APPEAL_SOURCE, C6.SOURCE_NO, c_bo_base_order.PROVINCE, c_bo_base_order.CUIBAN_NUM, ";
        // 创建人账号、创建时间、当前处理人、上一环节处理时间、归档时间、是否退赔、退赔账号
        selectFields += "c_bo_base_order.CREATE_ACC, c_bo_base_order.CREATE_TIME, (SELECT  group_concat( t10.user_acct)FROM ayykefu_ycmain.cc_user t10 WHERE t10.USERNAME = c_bo_order_ex.DEAL_NAME) CURR_HANDLER, c_bo_order_ex.DEAL_TIME, c_bo_base_order.END_TIME, C6.IS_REFUND, "+ desenCaller("C6.REFUND_ACC", user)+", ";


        selectFields += "GROUP_CONCAT(CASE WHEN C9.TYPE = '33' THEN C9.CONTENT ELSE NULL END)  CONTENT," ;
        selectFields += "GROUP_CONCAT(CASE WHEN C9.TYPE = '66' THEN C9.CONTENT ELSE NULL END)  AUTO_REPLAY_MSG," ;
        return selectFields;
    }

}
