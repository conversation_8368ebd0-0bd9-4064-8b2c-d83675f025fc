/**
 * 
 */
package com.yunqu.cc.mixgw.service;

import java.io.IOException;

import javax.servlet.annotation.WebServlet;

import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.mixgw.base.AppBaseServlet;
import com.yunqu.cc.mixgw.base.CommonLogger;

/**
 * <AUTHOR>
 * @date 2023-09-13 10:49:18
 */
@WebServlet("/servlet/workbench")
public class WorkbenchServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;

	public void actionForIndex() {
		try {
			if (isAdmin()) {
				this.getResponse().sendRedirect("/cx-mix-imusic/pages/homeIndex/index.html");
			} else {
				this.getResponse().sendRedirect("/cc-workbench/pages/dashboard/newPage/index.html");
			}
		} catch (IOException e) {
			CommonLogger.logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
	}

}
