package com.yunqu.cc.mixgw.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.IBaseService;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.model.WorkGroupModel;
import com.yq.busi.common.service.SchemaService;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.base.QueryFactory;
import com.yunqu.cc.mixgw.service.osql.OrderEndTimeHandleSql;
import com.yunqu.cc.mixgw.util.DBFuncUtils;
import com.yunqu.cc.mixgw.util.FileUtils;
import com.yunqu.cc.mixgw.util.excel.ExcelExport;
import com.yunqu.cc.mixgw.util.excel.ExcelExportDataHandle;
import com.yunqu.cc.mixgw.util.excel.impl.ExcelExportDataHandleImpl;
import com.yunqu.cc.mixgw.util.mapper.CamelJsonMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;

import javax.servlet.ServletOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.sql.SQLException;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

public class SummaryNoticeService extends IBaseService {
    @Override
    public String getServiceId() {
        return "CX_MIX_SUMMARY_NOTICE";
    }
    private Logger logger = CommonLogger.getLogger("timeout");
    private final String entId = Constants.getEntId();
    private final String busiOrderId = Constants.getBusiOrderId();

    private final String CRM_TEMPLATE_CODE = "1091549368";

    private final String WORK_TEMPLATE_CODE = "1091549368";

    private String schema = SchemaService.findSchemaByEntId(Constants.getEntId());

    @Override
    public String getName() {
        return "推送工信部工单清单";
    }

    @Override
    public JSONObject invokeMethod(JSONObject jsonObject) throws ServiceException {
        logger.info(CommonUtil.getClassNameAndMethod(this) + " >>> 请求业务接口 reqParam:" + JSONObject.toJSONString(jsonObject));
        String command = jsonObject.getString("command");
        if (StringUtils.equals(command, "dayExe")) {
            // 判断是否在时间内
            String ayyOrderMinistryExportWorkgroup = Constants.getAyyOrderMinistryExportWorkgroup().split("&")[0];
            String[] timeArray = ayyOrderMinistryExportWorkgroup.split(","); // 08:30,12:00,18:00
            // 每一分钟跑一次，判断当前时间点是否在时间段内
            logger.info("[汇总工单]：" + DateUtil.getCurrentDateStr("yyyy-MM-dd HH:mm:ss") + " 当前时间：" + DateUtil.getCurrentDateStr("HH:mm") + " 判断汇总时间：" + ayyOrderMinistryExportWorkgroup);
            for (String s : timeArray) {
                // 获取当前时间点
                String time = DateUtil.getCurrentDateStr("HH:mm");
                if(StringUtils.contains(time, s.substring(0, 4))) { // 按十分钟间段判断
                    export();
                    return EasyResult.ok();
                }
            }
        } else if (StringUtils.equals(command, "monthExe")) { // 每个月汇总
            // 判断当前天是否是1号
            if (DateUtil.getCurrentDateStr("dd").equals(Constants.getAyyOrderMinistryExportWorkgroup().split("&")[3])) {
                exportAll();
            }
        } else if (StringUtils.equals(command, "CRMWarning")) { // CRM 看看过去一分钟有没有录单
            sendMonitor();
        }
        return null;
    }

    private void sendMonitor() {
        try {

            String ayyCrmFlowWarningConfig = Constants.getAyyCrmWarningConfig();
            logger.info("[CRM开始预警]" + ayyCrmFlowWarningConfig);


            String ayyCrmWarningConfig = ayyCrmFlowWarningConfig.split("@")[0];
            String[] ayyCrmWarningConfigArray = ayyCrmWarningConfig.split("\\?");
            String[] timeRound = ayyCrmWarningConfigArray[0].split(","); // 开始预警时间范围 08:30,18:00
            String timeLength = ayyCrmWarningConfigArray[1]; // 多久没有录单
            String[] phones = ayyCrmWarningConfigArray[2].split(",");

            String currentDateStr = DateUtil.getCurrentDateStr("HH:mm");
            // 判断CRM的配置当前时间是否在时间范围内
            if (isRangTime(currentDateStr, timeRound[0], timeRound[1])) {
                EasySQL sql = new EasySQL();
                sql.append("select sum(case when APPEAL_SOURCE = '04' then 1 else 0 end) CRM_COUNT " +
                        " from " + schema + ".cx_mix_order_copy");
                sql.append("where 1 = 1");
                sql.append(DateUtil.addMinute(DateUtil.TIME_FORMAT, DateUtil.getCurrentDateStr(), -Integer.parseInt(timeLength)), "and create_time >= ? ");
                sql.append(DateUtil.getCurrentDateStr(), "and create_time <= ? ");

                JSONObject jsonObject = QueryFactory.getReadQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
                logger.info("[CRM报警]：" + DateUtil.getCurrentDateStr("yyyy-MM-dd HH:mm:ss") + " 当前预警时间：" + currentDateStr + " 预警时间段：" + timeRound[0] + "~" + timeRound[1] + " 预警时长：" + timeLength + " 预警手机：" + phones + " 查询工单结果：" + jsonObject);
                int crmCount = jsonObject.getIntValue("CRM_COUNT");
                UserModel userModel = new UserModel();
                userModel.setUserAcc("system");
                userModel.setBusiOrderId(busiOrderId);
                userModel.setSchemaName(schema);
                userModel.setEpCode(entId);

                if (crmCount == 0) { // crm报警
                    for (String phone : phones) {
                        JSONObject exJson = new JSONObject();
                        exJson.put("time", DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD));
                        exJson.put("system", "爱音乐客服系统");
                        exJson.put("level", "预警");
                        exJson.put("title", "工信部预警");
                        exJson.put("content", "集团CRM工单1个小时未录单");
                        sendMsg(userModel, phone, Constants.WARN_TEMPLATE, exJson);

                    }
                }

            }


            String ayyFlowWarningConfig = ayyCrmFlowWarningConfig.split("@")[1];
            String[] ayyFlowWarningConfigArray = ayyFlowWarningConfig.split("\\?");
            String[] timeRoundFlow = ayyFlowWarningConfigArray[0].split(","); // 开始预警时间范围 08:30,18:00
            String timeLengthFlow = ayyFlowWarningConfigArray[1]; // 多久没有录单
            String[] phonesFlow = ayyFlowWarningConfigArray[2].split(",");
            if (isRangTime(currentDateStr, timeRoundFlow[0], timeRoundFlow[1])) {
                EasySQL sql = new EasySQL();
                sql.append("select  " +
                        "sum(case when APPEAL_SOURCE = '09' AND PROVINCE = '440000' AND SOURCE_NO <> '' AND SOURCE_NO is not null THEN 1 ELSE 0 END) STREAM_COUNT from " + schema + ".cx_mix_order_copy");
                sql.append("where 1 = 1");
                sql.append(DateUtil.addMinute(DateUtil.TIME_FORMAT, DateUtil.getCurrentDateStr(), -Integer.parseInt(timeLengthFlow)), "and create_time >= ? ");
                sql.append(DateUtil.getCurrentDateStr(), "and create_time <= ? ");

                JSONObject jsonObject = QueryFactory.getReadQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
                logger.info("[广东工作流报警]：" + DateUtil.getCurrentDateStr("yyyy-MM-dd HH:mm:ss") + " 当前预警时间：" + currentDateStr + " 预警时间段：" + timeRound[0] + "~" + timeRound[1] + " 预警时长：" + timeLengthFlow + " 预警手机：" + phonesFlow + " 查询工单结果：" + jsonObject);
                int streamCount = jsonObject.getIntValue("STREAM_COUNT");

                UserModel userModel = new UserModel();
                userModel.setUserAcc("system");
                userModel.setBusiOrderId(busiOrderId);
                userModel.setSchemaName(schema);
                userModel.setEpCode(entId);
                if (streamCount == 0) { // 广东工作流报警
                    for (String phone : phonesFlow) {
                        JSONObject exJson = new JSONObject();
                        exJson.put("time", DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD));
                        exJson.put("system", "爱音乐客服系统");
                        exJson.put("level", "预警");
                        exJson.put("title", "工信部预警");
                        exJson.put("content", "广东工作流1个小时未录单");
                        sendMsg(userModel, phone, Constants.WARN_TEMPLATE, exJson);
                    }
                }
            }


        } catch (Exception e) {
            logger.error(CommonUtil.getClassNameAndMethod(this) + " >>> 报警短信发送失败", e);
        }
    }
    private void exportAll() {
        try {
            String name = "";
            List headers = new ArrayList<String>();
            List fields = new ArrayList<String>();
            Map dictMap = new HashMap<String,String>();
            Map treeMap = new HashMap<String,JSONObject>();
            Map formatMap = new HashMap<String,JSONObject>();
            List desenMap = new ArrayList();  // 号码脱敏
            List contenDesenMap = new ArrayList();  // 号码脱敏
            EasySQL sql = getOneMonth();
            logger.info("[所有工信部清单SQL]：" + sql.getFullSq());


            headers.add("产品部门");
            fields.add("PRODUCT_DEPT");

            headers.add("业务名称");
            fields.add("PRODUCT_NAME");

            headers.add("数量");
            fields.add("ALL_COUNT");


            String fileName = DateUtil.getCurrentDateStr("yyyyMMddHHmmss") + ".xlsx";
            String ayyOrderMinistryExportWorkgroup = Constants.getAyyOrderMinistryExportWorkgroup().split("&")[2];

            List<JSONObject> jsonObjects = sendByWorkGroup(Arrays.asList(ayyOrderMinistryExportWorkgroup.split(",")), schema);
            for (JSONObject jsonObject : jsonObjects) {
                String busiId = RandomKit.randomStr();
                UserModel user = new UserModel();
                user.setUserAcc("system");
                user.setSchemaName(schema);
                user.setEpCode(entId);
                user.setBusiOrderId(busiOrderId);
                user.setBusiId(busiId);
                FileInputStream fileInputStream = exportExcel(user, "", sql, headers, fields, dictMap, treeMap, formatMap, desenMap, contenDesenMap);
                FileUtils.fileUpload(fileInputStream,"summary", schema, entId,busiOrderId,busiId, fileName);

                UserModel userModel = new UserModel();
                userModel.setUserAcc("system");
                userModel.setBusiId(busiId);
                userModel.setBusiOrderId(Constants.getBusiOrderId());
                userModel.setSchemaName(schema);
                userModel.setEpCode(Constants.getEntId());
                String email = jsonObject.getString("email");
                if (StringUtils.isNotBlank(email)) {
                    JSONObject info = getOneMonthCount();
                    String allCount = info.getString("allCount");
                    String overCount = info.getString("overCount");
                    String onOverCount = info.getString("onOverCount");
                    sendEmail(userModel, email, DateUtil.addMonth("MM", DateUtil.getCurrentDateStr("MM"), -1) + "月工信部投诉工单统计",
                            "本月工信部工单共"+allCount+"宗，其中24小时内处理工单"+onOverCount+"宗，超24小时处理"+overCount+"宗。");
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    private void export() {
        logger.info("工信部工单定时催单");
        try {
            String name = "";
            List headers = new ArrayList<String>();
            List fields = new ArrayList<String>();
            Map dictMap = new HashMap<String,String>();
            Map treeMap = new HashMap<String,JSONObject>();
            Map formatMap = new HashMap<String,JSONObject>();
            List desenMap = new ArrayList();  // 号码脱敏
            List contenDesenMap = new ArrayList();  // 号码脱敏
            EasySQL sql = getOrderList();
            logger.info("[工信部清单SQL]：" + sql.getFullSq());

//            headers.add("工单编号");
//            fields.add("ORDER_NO");
//            desenMap.add("CALLER");
//
//            headers.add("受理号码");
//            fields.add("PROVINCE");
//
//            headers.add("工单类型");
//            fields.add("ORDER_TYPE");
//            dictMap.put("ORDER_TYPE", "IM_ORDER_TYPE");
//
//            headers.add("创建时间");
//            fields.add("CREATE_TIME");
//
//            headers.add("归档时间");
//            fields.add("END_TIME");
//
//            headers.add("转派时间");
//            fields.add("CLAIM_TIME");
//
//            headers.add("省份");
//            fields.add("PROVINCE");
//            dictMap.put("PROVINCE", "IM_ORDER_PROVINCE");
//
//            headers.add("申诉来源");
//            fields.add("APPEAL_SOURCE");
//            dictMap.put("APPEAL_SOURCE", "IM_APPEAL_SOURCE");
//
//            headers.add("投诉内容");
//            fields.add("CONTENT");
//            contenDesenMap.add("CONTENT");
//
//            headers.add("业务名称");
//            fields.add("PRODUCT_NAME");
//
//            headers.add("部门");
//            fields.add("PRODUCT_DEPT");
//
//            headers.add("订购渠道");
//            fields.add("ORDER_CHANNEL");
//
//            headers.add("转派人员");
//            fields.add("CLAIM_ACC");
//
//            headers.add("工单接受人员");
//            fields.add("RECEIVER");
//
//            headers.add("处理进度");
//            fields.add("HANDLE_PROC");
//            dictMap.put("HANDLE_PROC", "IM_HANDLE_PROC");
//
//            headers.add("超出时限");
//            fields.add("TIME_OUT");

            headers.add("工单编号");
            fields.add("ORDER_NO");

            headers.add("省份");
            fields.add("PROVINCE");
            dictMap.put("PROVINCE", "IM_ORDER_PROVINCE");

            headers.add("创建时间");
            fields.add("CREATE_TIME");

            headers.add("创建人名字");
            fields.add("CREATE_NAME");

            headers.add("处理人");
            fields.add("CURR_HANDLER");

            headers.add("处理状态");
            fields.add("ORDER_STATUS");
            dictMap.put("ORDER_STATUS", "IM_HANDLE_STATUS");

            headers.add("产品部门");
            fields.add("PRODUCT_DEPT");

            headers.add("产品名称");
            fields.add("PRODUCT_NAME");

            headers.add("投诉内容");
            fields.add("CONTENT");


            String fileName = DateUtil.getCurrentDateStr("yyyyMMddHHmmss") + ".xlsx";
            String ayyOrderMinistryExportWorkgroup = Constants.getAyyOrderMinistryExportWorkgroup().split("&")[1];

            List<JSONObject> jsonObjects = sendByWorkGroup(Arrays.asList(ayyOrderMinistryExportWorkgroup.split(",")), schema);
            for (JSONObject jsonObject : jsonObjects) {
                String busiId = RandomKit.randomStr();
                UserModel user = new UserModel();
                user.setUserAcc("system");
                user.setSchemaName(schema);
                user.setEpCode(entId);
                user.setBusiOrderId(busiOrderId);
                user.setBusiId(busiId);
                FileInputStream fileInputStream = exportExcel(user, "", sql, headers, fields, dictMap, treeMap, formatMap, desenMap, contenDesenMap);
                FileUtils.fileUpload(fileInputStream,"summary", schema, entId,busiOrderId,busiId, fileName);

                UserModel userModel = new UserModel();
                userModel.setUserAcc("system");
                userModel.setBusiId(busiId);
                userModel.setBusiOrderId(Constants.getBusiOrderId());
                userModel.setSchemaName(schema);
                userModel.setEpCode(Constants.getEntId());
                String email = jsonObject.getString("email");
                String mobile = jsonObject.getString("mobile");

                JSONObject info = getInfo();
                logger.info("[INFO] ：" + info);
                String overCount = info.getString("OVER_COUNT");
                String noOverCount = info.getString("NO_OVER_COUNT");
                String allCount = info.getString("ALL_COUNT");

                if (StringUtils.isNotBlank(email)) {

                    sendEmail(userModel, email, DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD) + "】工信部清单",
                            "工信部处理中工单共"+allCount+"宗，准备超时"+noOverCount+"宗，已超时"+overCount+"宗。");
                }
                if (StringUtils.isNotBlank(mobile)) {
                    JSONObject exJson = new JSONObject();
                    exJson.put("time", DateUtil.getCurrentDateStr());
                    exJson.put("system", "爱音乐客服系统");
                    exJson.put("level", "预警");
                    exJson.put("title", "工信部预警");
                    exJson.put("content", "工信部处理中工单共"+allCount+"宗，准备超时"+noOverCount+"宗，已超时"+overCount+"宗。");
                    sendMsg(userModel, mobile,Constants.WARN_TEMPLATE, exJson);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    /**
     *
     * @param currentTime 检查时间
     * @param start 开始时间
     * @param end 结束时间
     * @return 在时间范围内返回true，否则返回false
     */
    private boolean isRangTime(String currentTime, String start, String end) {
        LocalTime startTime = LocalTime.parse(start);
        LocalTime endTime = LocalTime.parse(end);
        LocalTime checkTime = LocalTime.parse(currentTime);
        // 判断checkTime是否在startTime和endTime之间
        if (checkTime.isAfter(startTime) && checkTime.isBefore(endTime)) {
            return true;
        } else {
            return false;
        }
    }
    public  List<JSONObject> sendByWorkGroup(List<String> workGroupName, String schema) throws SQLException {
        EasySQL sql = new EasySQL();
        sql.appendIn(workGroupName.toArray(new String[0]), "select  t1.email,t1.user_acct, t1.mobile from cc_user t1 left join "+schema+".c_cf_workgroup_user  t2 on t1.user_id = t2.user_id left join "+schema+".c_cf_workgroup t3 on t3.id = t2.WORKGROUP_ID" +
                " where t2.workgroup_id and t3.name ");
        List<JSONObject> jsonObjects = QueryFactory.getWriteQuery().queryForList(sql.getSQL(), sql.getParams(), CamelJsonMapper.getInstance());
        jsonObjects = deduplicateByField(jsonObjects, "userAcct");
        return jsonObjects;

    }

    private FileInputStream exportExcel(UserModel user,String title, EasySQL sql, List headers , List fields, Map dictMap, Map treeMap, Map formatMap, List desenMap, List contenDesenMap) throws Exception {
//
//        UserModel userModel = new UserModel();
//        userModel.setEpCode(entId);
//        userModel.setBusiOrderId(busiOrderId);
//        userModel.setSchemaName(schema);

        ExcelExportDataHandle handle = new ExcelExportDataHandleImpl();
        handle.setEntId(entId);
        handle.setBusiOrderId(busiOrderId);
        handle.setDictMap(dictMap);
        handle.setTreeMap(treeMap);
        handle.setFormatMap(formatMap);
        handle.setDesenMap(desenMap);
        handle.setContenDesenMap(contenDesenMap);
        ExcelExport exportExcel = new ExcelExport().setUser(user).setEasySql(sql)
                .setFields(fields).setHeaderList(headers).setExcelExportDataHandle(handle);

        return exportExcel.export(title);
    }
    private EasySQL getOrderList() throws SQLException {

        int ayyOrderSuperTime = Integer.parseInt(Constants.getAyyOrderSuperTime()) ; // 超时时限 24
        int ayyOrderTimeoutTime = Integer.parseInt(Constants.getAyyOrderTimeoutTime()) ; // 预超时 6
        int ayyOrderTimeoutTimeHalf = ayyOrderTimeoutTime / 2;
        String currentDateStr = DateUtil.getCurrentDateStr();//  2024-07-03 15:27:30
//        String ayyOrderSuperTimeAddHour = DateUtil.addHour(DateUtil.TIME_FORMAT, currentDateStr, -ayyOrderSuperTime); //  2024-07-02 15:27:30
//        String ayyOrderTimeoutTimeAddHour = DateUtil.addHour(DateUtil.TIME_FORMAT, ayyOrderSuperTimeAddHour, ayyOrderTimeoutTime);// 2024-07-02 09:27:30
//        String ayyOrderTimeoutTimeHalfAddHour = DateUtil.addHour(DateUtil.TIME_FORMAT, ayyOrderSuperTimeAddHour, ayyOrderTimeoutTimeHalf);//2024-07-02 09:27:30

        currentDateStr = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, currentDateStr, -1);
        String start = currentDateStr + " 00:00:01";
        String end = currentDateStr + " 23:59:59";

        String timeout24 = DateUtil.addHour(DateUtil.TIME_FORMAT, DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT), 24);
        EasySQL sql = new EasySQL();
        sql.append("SELECT t1.ORDER_NO, t1.PROVINCE, t1.CREATE_TIME,t1.CREATE_NAME,t1.CURR_HANDLER,t1.ORDER_STATUS,t1.PRODUCT_DEPT,t1.PRODUCT_NAME,t1.CONTENT,");
        sql.append("(CASE WHEN t2.ORDER_NO is not null and (t2.ADD_ACC = '' or t2.ADD_ACC is null) then (select group_concat(t4.user_acct) from cc_user t4 left join " + schema + ".c_cf_workgroup_user t3  on t4.user_id = t3.user_id where t3.workgroup_id =  t2.workgroup_id) ");
        sql.append(" when t2.order_no is not null and t2.ADD_ACC <> '' and t2.ADD_ACC is not null then t2.add_acc ");
        sql.append(" else '' end) RECEIVER,");
        sql.append(" t1.HANDLE_PROC");

//        sql.append(timeout,",(case when timestampdiff(HOUR, t1.CREATE_TIME, ?) > 0 ")
//                .append(timeout," then timestampdiff(HOUR, t1.CREATE_TIME, ?) else 0 end ) TIME_OUT");
        sql.append("from " + schema + ".cx_mix_order_copy t1 left join " + schema + ".cx_music_oder_workgrpop_log t2 on t1.ORDER_NO = t2.ORDER_NO ");
        sql.append(" where 1 = 1 ");
//        sql.append(start, " and t1.create_time >= ? ");
//        sql.append(end, " and t1.create_time <= ? ");
//        sql.append("2023-02-10 00:00:00", " and t1.create_time >= ? ");
//        sql.append("2024-10-10 10:10:10", " and t1.create_time <= ? ");
        sql.append(" and t1.ORDER_TYPE = '07' ");
        sql.append(" and t1.ORDER_STATUS = '01' ");
        sql.append(" group by t1.ORDER_NO ");


        return  sql;
    }


    private JSONObject getInfo() {
        try {
            int ayyOrderSuperTime = Integer.parseInt(Constants.getAyyOrderSuperTime()) ; // 超时时限 24
            int ayyOrderTimeoutTime = Integer.parseInt(Constants.getAyyOrderTimeoutTime()) ; // 预超时 6
            String currentDateStr = DateUtil.getCurrentDateStr();//  2024-07-03 15:27:30
            String ayyOrderSuperTimeAddHour = DateUtil.addHour(DateUtil.TIME_FORMAT, currentDateStr, -ayyOrderSuperTime); //  2024-07-02 15:27:30
            String ayyOrderTimeoutTimeAddHour = DateUtil.addHour(DateUtil.TIME_FORMAT, ayyOrderSuperTimeAddHour, ayyOrderTimeoutTime);// 2024-07-02 09:27:30

            EasySQL sql = new EasySQL();
            sql.append("select ");
            sql.append(ayyOrderSuperTimeAddHour, "sum(case when t1.CREATE_TIME < ? then 1 else 0 end) over_count, "); // 超时
            sql.append(ayyOrderSuperTimeAddHour , "sum(case when ( ? <= t1.CREATE_TIME ").append(ayyOrderTimeoutTimeAddHour, " AND ? > t1.CREATE_TIME )  then 1 else 0 end) no_over_count,"); // 准备超时
            sql.append("count(1) all_count");
            sql.append("from " + schema + ".cx_mix_order_copy t1" );
            sql.append(" where t1.ORDER_TYPE = '07' ");
            sql.append(" and t1.ORDER_STATUS = '01' ");
            logger.info("查询当日工单超时信息，sql：" + sql.getFullSq());
            EasyQuery query = QueryFactory.getReadQuery();
            return query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
        } catch (Exception e) {
            logger.error("获取工单信息异常", e);
            return new JSONObject();
        }
    }

    public static List<JSONObject> deduplicateByField(List<JSONObject> jsonObjectList, String fieldName) {
        Set<Object> seenValues = new HashSet<>();
        return jsonObjectList.stream()
                .filter(jsonObject -> seenValues.add(jsonObject.get(fieldName)))
                .collect(Collectors.toList());
    }


    private boolean sendEmail(UserModel user, String noticeNumber, String title, String content) {
        JSONObject rtJson = new JSONObject();
        try {
            JSONObject param = new JSONObject();
            param.put("serialId", ServiceID.EMAIL_INTERFACE);
            param.put("command", ServiceCommand.REQ_SEND_EMAIL);
            param.put("title", title);
            param.put("content", content);
            param.put("to", noticeNumber); // 接受账号
            param.put("source", "99");// 99-标准通知
            String sendTime = EasyCalendar.newInstance().getDateTime("-");
            param.put("sendTime", sendTime);
            param.put("busiId", user.getBusiId());
            param.put("userAcc", user.getUserAcc());
            param.put("haveAttachment", "Y");// N-不存在附件
            param.put("emailId", user.getBusiId());

//            param.put("from", emailAcc); // 发送邮件的账号
            param.put("schema",user.getSchemaName());
            param.put("epCode", user.getEpCode());
            param.put("busiOrderId", user.getBusiOrderId());
            logger.info("请求邮件接口参数：" + param);
            IService service = ServiceContext.getService(ServiceID.EMAIL_INTERFACE);
            JSONObject result = service.invoke(param);
            if (result.getString("respCode").equals(GWConstants.RET_CODE_SUCCESS)) {
                rtJson.put("isSuccess", "1");
                rtJson.put("respDesc", "邮件消息发送成功！");

                logger.info( "[" + noticeNumber + "] 邮件消息发送成功！");
                return true;
            } else {
                rtJson.put("isSuccess", "2");
                rtJson.put("respDesc", "[" + noticeNumber + "] 邮件消息发送失败！接口返回码为：" + result.getString("respCode"));
                logger.error( "[" + noticeNumber + "] 邮件消息发送失败！接口返回码为：" + result.getString("respCode"));
                return false;
            }
        } catch (Exception e) {
            rtJson.put("isSuccess", "2");
            rtJson.put("respDesc", "邮件消息发送失败！系统异常!");
            logger.error( "[" + noticeNumber + "] 邮件消息发送接口异常！" + e.getMessage(), e);
            return false;
        }
    }


    private EasySQL getOneMonth() {
        String end = DateUtil.addMonth("yyyyMMdd", DateUtil.getCurrMonthEndDay("yyyyMMdd"), 0);
        String begin = DateUtil.addMonth("yyyyMMdd", DateUtil.getCurrMonthBeginDay("yyyyMMdd"), -1);
        // 查询上一个月工信部工单
        EasySQL sql = new EasySQL();
        sql.append("SELECT t2.product_dept,t2.PRODUCT_NAME, count(t1.ORDER_NO) ALL_COUNT ");
        sql.append("from " + schema + ".cx_mix_order_copy t1 left join " + schema + ".c_box_music_product t2 on t1.ORDER_NO = t2.ORDER_NO ");
        sql.append(" where 1 = 1 ");
        sql.append(" and t1.ORDER_TYPE = '07' ");
        sql.append(begin, "and t1.DATEID >= ? ");
        sql.append(end, "and t1.DATEID <= ? ");
        sql.append(" group by t2.product_dept, t2.PRODUCT_NAME ");
        return sql;
    }

    private JSONObject getOneMonthCount() throws SQLException {
        JSONObject result = new JSONObject();
        String end = DateUtil.addMonth("yyyyMMdd", DateUtil.getCurrMonthEndDay("yyyyMMdd"), 0);
        String begin = DateUtil.addMonth("yyyyMMdd", DateUtil.getCurrMonthBeginDay("yyyyMMdd"), -1);
        // 查询上一个月工信部工单
        EasySQL sql = new EasySQL();
        sql.append("SELECT t1.ORDER_NO,t1.CREATE_TIME,t1.END_TIME ");
        sql.append("from " + schema + ".cx_mix_order_copy t1 ");
        sql.append(" where 1 = 1 ");
        sql.append(" and t1.ORDER_TYPE = '07' ");
        sql.append(begin, "and t1.DATEID >= ? ");
        sql.append(end, "and t1.DATEID <= ? ");

        EasyQuery query = QueryFactory.getReadQuery();
        List<JSONObject> jsonObjects = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
        result.put("allCount", jsonObjects.size());
        int overCount = 0;
        int onOverCount = 0;
        for (JSONObject jsonObject : jsonObjects) {
            String createTime = jsonObject.getString("CREATE_TIME");
            String endTime = jsonObject.getString("END_TIME");
            String s = DateUtil.addHour(DateUtil.TIME_FORMAT, createTime, 24);
            if (DateUtil.bwSeconds(s, endTime) > 0) { // 超时
                overCount++;
            } else {
                onOverCount++;
            }
        }
        result.put("overCount", overCount);
        result.put("onOverCount", onOverCount);
        return result;
    }

    private boolean sendMsg(UserModel user, String phone, String code, JSONObject exJson) {
        try {
            if(StringUtils.isNotBlank(phone)){
                //发送短信
                JSONObject param = new JSONObject();
                param.put("userAcc",user.getUserAcc());
                param.put("epCode",user.getEpCode());
                param.put("busiOrderId",user.getBusiOrderId());
                param.put("sender", Constants.APP_NAME);
                param.put("source", "1");
                param.put("command", ServiceCommand.SENDMESSAGE);
                // 接收电话与内容
                JSONObject json = new JSONObject();
                json.put("receiver", phone);// 接收号码
                json.put("content","@"+code);//模板编号
                json.put("exJson",exJson);
                param.put("receivers", Collections.singletonList(json));
                logger.info("请求短信接口参数：" + param);
                IService service = ServiceContext.getService(ServiceID.SMSGW_INTEFACE);
                JSONObject result = service.invoke(param);
                logger.info("[LoginSmsService]:发送短信：接收号码"+phone+",result:"+result);
                if (result.getString("respCode").equals(GWConstants.RET_CODE_SUCCESS)) {
                    return true;
                } else {
                    return false;
                }
            }
            logger.error("[LoginSmsService]:发送短信手机号码为空");
            return false;
        } catch (Exception e) {
            logger.error("[LoginSmsService]:发送短信报错"+e.getMessage()+e.getCause(),e);
            return false;
        }
    }

}
