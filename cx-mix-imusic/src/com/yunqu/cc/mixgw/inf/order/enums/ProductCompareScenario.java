package com.yunqu.cc.mixgw.inf.order.enums;

/**
 * 产品比较场景枚举
 * 用于定义不同的产品信息比较结果场景
 */
public enum ProductCompareScenario {
	/**
	 * 场景1：产品和金额一致，无需更新
	 */
	SAME_PRODUCTS_SAME_AMOUNT,
	
	/**
	 * 场景2.1：包含原产品且金额一致，增加剩余产品
	 */
	CONTAINS_ORIGINAL_SAME_AMOUNT,
	
	/**
	 * 场景2.2：包含原产品但金额不一致，增加剩余产品并更新金额
	 */
	CONTAINS_ORIGINAL_DIFF_AMOUNT,
	
	/**
	 * 场景2.3：不包含原产品，保留原产品并增加新产品
	 */
	NOT_CONTAINS_ORIGINAL,
	
	/**
	 * 场景2.4：替换原产品
	 */
	REPLACE_ORIGINAL
} 