package com.yunqu.cc.mixgw.inf;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.IBaseService;
import com.yunqu.cc.mixgw.base.CommonLogger;

public class EorderAssignByService extends IBaseService {

    private final Logger logger = CommonLogger.getLogger("order-other");

	@Override
	public String getServiceId() {
		return "SERVICE_EORDER_ASSIGN";
	}

	@Override
	public String getName() {
		return "工单分发策略服务";
	}

	@Override
	public JSONObject invokeMethod(JSONObject params){
		JSONObject result = new JSONObject();
		try {
			JSONObject task = params.getJSONObject("task");
			JSONObject node = params.getJSONObject("node");
			JSONObject orderInfo = params.getJSONObject("orderInfo");
			String userAcc = params.getString("userAcc");
			String userName = params.getString("userName");
			String schema = params.getString("schema");
			String entId = params.getString("entId");
			String busiOrderId = params.getString("busiOrderId");
			
			String dealAcc = orderInfo.getString("DEAL_ACC");
			if (StringUtils.isBlank(dealAcc)) {
				result.put("assignee", "");
			} else if ("system".equals(dealAcc) && !"system".equals(userAcc)) {
				result.put("assignee", userAcc);
			} else {
				result.put("assignee", dealAcc);
			}
			result.put("respCode", "000");
			result.put("respMsg", "获取上一处理人成功");
			return result;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			result.put("respCode", "999");
			result.put("respMsg", "获取上一处理人异常: "+ e.getMessage());
		}
		return result;
	}
	
}
