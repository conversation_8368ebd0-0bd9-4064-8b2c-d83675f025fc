package com.yunqu.cc.mixgw.task.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.cache.RedisUtil;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.base.QueryFactory;
import com.yunqu.cc.mixgw.util.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import java.sql.SQLException;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024-03-19 09:00:56
 */
public class AutoHandleServiceV2 {

	private AutoHandleServiceV2() {

	}

	public static AutoHandleServiceV2 getInstance() {
		return new AutoHandleServiceV2();
	}

	private Logger logger = CommonLogger.getLogger("task");

	private static final String AUTO_HANDLE_APPEAL_SOURCE = "IMUSIC_AUTO_HANDLE_APPEAL_SOURCE_";

	private static final String AUTO_HANDLE_GROUP_NAME = "AUTO_HANDLE_GROUP_NAME_";

	public void autoHandle(JSONObject orderInfo, List<JSONObject> rpaActionList, boolean isFollow, String entId, String busiOrderId, String schema) {

		// 参数定义及初始化
		UserModel user = getDefaultUser(entId, busiOrderId, schema);

		String orderId = orderInfo.getString("ORDER_ID");
		String orderNo = orderInfo.getString("ORDER_NO");

		try {
			// 自动处理结果 Y-成功 N-失败
			String ex8 = orderInfo.getString("EX_8");
			// 是否渠道转发 01-否 02-是
			String ex3 = orderInfo.getString("EX_3");
			if (!StringUtils.equalsAny(ex3, "01", "02")) {
				ex3 = "01";
			}

			// 经过坐席处理
			if (checkIsAgentEntry(orderInfo)) {
				logger.info("工单[" + orderId + "][" + orderNo + "] 当前工单经过坐席处理，跳过自动处理环节");
				ex8 = "Y";
				RpaOrderLogUtils.saveLogByHandler(entId, schema, orderId, RpaOrderLogUtils.OPER_RESULT_SUCC, "工单[" + orderId + "][" + orderNo + "] 经过坐席处理，无需自动处理", busiOrderId);

				// 解析相关参数
				for (JSONObject productJson : rpaActionList) {
					if (Constants.RPA_ACTION_REFUND.equals(productJson.getString("ACTION"))) {
						ex3 = "01";
					}
				}
				batchUpdateProductStatus(schema, RpaOrderLogUtils.OPER_RESULT_SUCC, rpaActionList, "");
				// 流转工单
				if (isFollow) {
					submitOrder(user, orderId, orderNo, ex3, ex8);
				}
				return;
			}

			// crm已归档工单，直接流转人工
			if(checkCrmIsClose(orderInfo)) {
				logger.info("工单[" + orderId + "][" + orderNo + "] 当前工单省份已归档，流转人工归档");
                ex8 = "N";
                RpaOrderLogUtils.saveLogByHandler(entId, schema, orderId, RpaOrderLogUtils.OPER_RESULT_SUCC, "工单[" + orderId + "][" + orderNo + "] 已归档，无需自动处理", busiOrderId);
				batchUpdateProductStatus(schema, RpaOrderLogUtils.OPER_RESULT_SUCC, rpaActionList, "");
				// 流转工单
				if (isFollow) {
					submitOrder(user, orderId, orderNo, ex3, ex8);
				}
				return;
			}

			if (checkIsWechatPay(orderInfo)) {
				logger.info("工单[" + orderId + "][" + orderNo + "] 订单类型为微信支付，跳过自动处理环节");

				// 判断是否需要转人工
				JSONObject wxPlay = getWxPlay(schema, orderId);
				if (wxPlay == null) {
					RpaOrderLogUtils.saveLogByHandler(entId, schema, orderId, RpaOrderLogUtils.OPER_RESULT_FAIL, "此工单号不存在投诉", busiOrderId);
					ex8 = "N";
				} else {
					String autoHandleResult = wxPlay.getString("AUTO_HANDLE_RESULT");
					String autoHandleMsg = wxPlay.getString("AUTO_HANDLE_MSG");
					if (StringUtils.equals(autoHandleResult, "0")) { // 自动处理成功
						RpaOrderLogUtils.saveLogByHandler(entId, schema, orderId, RpaOrderLogUtils.OPER_RESULT_SUCC, DateUtil.getCurrentDateStr() + autoHandleMsg, busiOrderId);
						ex8 = "Y";
					} else { // 失败转人工
						RpaOrderLogUtils.saveLogByHandler(entId, schema, orderId, RpaOrderLogUtils.OPER_RESULT_FAIL, DateUtil.getCurrentDateStr() + autoHandleMsg, busiOrderId);
						ex8 = "N";
					}
				}
				// 流转工单
				if (isFollow) {
					submitOrder(user, orderId, orderNo, ex3, ex8);
				}
				return;
			}

			// 自动处理开关
			boolean authHandleFlag = Constants.getAutoHandleSwitch();
			if (!authHandleFlag) {
				logger.info("工单[" + orderId + "][" + orderNo + "] 全局自动处理开关未开启");
				RpaOrderLogUtils.saveLogByCheck(entId, schema, orderId, RpaOrderLogUtils.OPER_RESULT_FAIL, "工单[" + orderId + "][" + orderNo + "] 全局自动处理开关未开启", busiOrderId);
				ex8 = "N";

				batchUpdateProductStatus(schema, RpaOrderLogUtils.OPER_RESULT_FAIL, rpaActionList, "");
				// 流转工单
				if (isFollow) {
					submitOrder(user, orderId, orderNo, ex3, ex8);
				}
				return;
			}

			String appealSource = orderInfo.getString("APPEAL_SOURCE");
			String groupName = orderInfo.getString("GROUP_NAME");
			List<String> actionList = getActionList(appealSource, groupName, entId, busiOrderId, schema);
			if (actionList.size() == 0) {
				logger.info("工单[" + orderId + "][" + orderNo + "] 未匹配到可自动处理的意图");
				RpaOrderLogUtils.saveLogByHandler(entId, schema, orderId, RpaOrderLogUtils.OPER_RESULT_FAIL, "工单[" + orderId + "][" + orderNo + "] 未匹配到可自动处理的意图", busiOrderId);
				ex8 = "N";

				batchUpdateProductStatus(schema, RpaOrderLogUtils.OPER_RESULT_FAIL, rpaActionList, "");
				// 流转工单
				if (isFollow) {
					submitOrder(user, orderId, orderNo, ex3, ex8);
				}
				return;
			}

			// 进行自动处理
			for (JSONObject productJson : rpaActionList) {
				String recordId = productJson.getString("ID");
				String action = productJson.getString("ACTION");
				if (Constants.RPA_ACTION_REFUND.equals(action)) {
					ex3 = "01";
				}

				// 该意图不允许自动处理
				if (!actionList.contains(action)) {
					ex8 = "N";
					logger.info("工单[" + orderId + "][" + orderNo + "] 不允许处理意图[" + action + "]");
					updateProductStatus(schema, RpaOrderLogUtils.OPER_RESULT_FAIL, recordId, "");
					continue;
				}

				String number = productJson.getString("MOBILE");
				if (StringUtils.isBlank(number)) {
					number = orderInfo.getString("CALLER");
				}
				String productId = productJson.getString("PRODUCT_ID");
				String productName = productJson.getString("PRODUCT_NAME");
				String productCode = productJson.getString("product_code");

				String operResult = RpaOrderLogUtils.OPER_RESULT_FAIL;

				// 屏蔽
				if (Constants.RPA_ACTION_SCREEN.equals(action)) {
					String replayMsg = "";
					if (ApiUtils.saveBlack(Arrays.asList(number), ApiUtils.BLACK_TYPE_PRODUCT, Constants.OP_ACC_RPA)) {
						operResult = RpaOrderLogUtils.OPER_RESULT_SUCC;
						if (StringUtils.isBlank(productJson.getString("SHIELDING_TIME"))) {
							productJson.put("SHIELDING_TIME", DateUtil.getCurrentDateStr());
						}
						replayMsg = OrderUtils.getOrderReplayMsg(orderId, productJson, entId, busiOrderId, schema);
						if (StringUtils.isBlank(replayMsg)) {
							ex8 = "N";
							RpaOrderLogUtils.saveLogByHandler(entId, schema, orderId, operResult, DateUtil.getCurrentDateStr() + "[" + number + "][屏蔽] 进行自动处理,生成回复模板失败", busiOrderId);
						} else {
							RpaOrderLogUtils.saveLogByHandler(entId, schema, orderId, operResult, DateUtil.getCurrentDateStr() + "[" + number + "][屏蔽] 进行自动处理", busiOrderId);
						}
					} else {
						ex8 = "N";
						RpaOrderLogUtils.saveLogByHandler(entId, schema, orderId, operResult, DateUtil.getCurrentDateStr() + "[" + number + "][屏蔽] 进行自动处理失败", busiOrderId);
					}
					updateProductStatus(schema, operResult, recordId, replayMsg);
				}

				// 退订
				if (Constants.RPA_ACTION_CANCEL.equals(action)) {
					String replayMsg = "";
					String refundTime = productJson.getString("REFUND_TIME");
					if (StringUtils.isNotBlank(refundTime)) {
						replayMsg = OrderUtils.getOrderReplayMsg(orderId, productJson, entId, busiOrderId, schema);
						operResult = RpaOrderLogUtils.OPER_RESULT_SUCC;

						if (StringUtils.isBlank(replayMsg)) {
							ex8 = "N";
							RpaOrderLogUtils.saveLogByHandler(entId, schema, orderId, operResult, DateUtil.getCurrentDateStr() + "[" + number + "][" + productId + "][" + productName + "][退订] 进行自动处理,生成回复模板失败", busiOrderId);
						} else {
							RpaOrderLogUtils.saveLogByHandler(entId, schema, orderId, operResult, DateUtil.getCurrentDateStr() + "[" + number + "][" + productId + "][" + productName + "][退订] 进行自动处理成功", busiOrderId);
						}
					} else if (BusiHandleService.productUnorder(entId, schema, orderId, productJson, number, Constants.OP_ACC_RPA, busiOrderId)) {
						if (StringUtils.isBlank(productJson.getString("REFUND_TIME"))) {
							refundTime = DateUtil.getCurrentDateStr();
							// 设置默认退订时间
							productJson.put("REFUND_TIME", refundTime);

							if (StringUtils.isNotBlank(productId)) {
								QueryFactory.getWriteQuery().execute("update " + schema + ".C_BOX_MUSIC_PRODUCT set REFUND_TIME = ? where PRODUCT_ID = ?", refundTime, productId);
							} else {
								QueryFactory.getWriteQuery().execute("update " + schema + ".C_BOX_MUSIC_PRODUCT set REFUND_TIME = ? where ID = ?", refundTime, recordId);
							}
						}
						replayMsg = OrderUtils.getOrderReplayMsg(orderId, productJson, entId, busiOrderId, schema);
						operResult = RpaOrderLogUtils.OPER_RESULT_SUCC;
						if (StringUtils.isBlank(replayMsg)) {
							ex8 = "N";
							RpaOrderLogUtils.saveLogByHandler(entId, schema, orderId, operResult, DateUtil.getCurrentDateStr() + "[" + number + "][" + productId + "][" + productName + "][退订] 进行自动处理,生成回复模板失败", busiOrderId);
						} else {
							RpaOrderLogUtils.saveLogByHandler(entId, schema, orderId, operResult, DateUtil.getCurrentDateStr() + "[" + number + "][" + productId + "][" + productName + "][退订] 进行自动处理成功", busiOrderId);
						}
					} else {
						ex8 = "N";
						RpaOrderLogUtils.saveLogByHandler(entId, schema, orderId, operResult, DateUtil.getCurrentDateStr() + "[" + number + "][" + productId + "][" + productName + "][退订] 进行自动处理失败", busiOrderId);
					}
					updateProductStatus(schema, operResult, recordId, replayMsg);
				}
				// 查询
				if (StringUtils.equalsAny(action, Constants.RPA_ACTION_SEARCH, Constants.RPA_ACTION_SEARCH_CHANNEL)) {
					JSONObject productInfo = BusiHandleService.queryProductInfo(number, productJson, Constants.OP_ACC_RPA);
					if (productInfo != null) {
						operResult = RpaOrderLogUtils.OPER_RESULT_SUCC;
					} else {
						ex8 = "N";
					}
					String handleMsg = OrderUtils.getOrderReplayMsg(orderId, productInfo, entId, busiOrderId, schema);
					if (StringUtils.isBlank(handleMsg)) {
						ex8 = "N";
						RpaOrderLogUtils.saveLogByHandler(entId, schema, orderId, operResult, DateUtil.getCurrentDateStr() + "[" + number + "][" + productId + "][" + productName + "][查询] 进行自动处理失败,生成回复模板失败", busiOrderId);
					} else {
						RpaOrderLogUtils.saveLogByHandler(entId, schema, orderId, operResult, DateUtil.getCurrentDateStr() + "[" + number + "][" + productId + "][" + productName + "][查询] 进行自动处理成功", busiOrderId);
					}
					updateProductStatus(schema, operResult, recordId, handleMsg);
				}

				// 退费
				if (Constants.RPA_ACTION_REFUND.equals(action)) {
					// 退赔产品管控，在管控列表的自动处理失败转人工
					if (BusiHandleService.busiringProduct(schema, number, productCode, productId)) {
						ex8 = "N";
						RpaOrderLogUtils.saveLogByHandler(entId, schema, orderId, operResult, DateUtil.getCurrentDateStr() + "[" + number + "][" + productId + "][" + productName + "][退费] 进行自动处理失败，存在退费管控产品", busiOrderId);
					} else {
						ex3 = "01";
					}
				}
			}

			if (isFollow) {
				submitOrder(user, orderId, orderNo, ex3, ex8);
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 工单[" + orderId + "][" + orderNo + "]自动处理异常 error:" + e.getMessage(), e);
			MinistryNoticeUtil.sendErrorNotice("orderNo[" + orderNo + "]自动处理流转异常");
			try {
				submitOrder(user, orderId, orderNo, "", "N");
			} catch (Exception e1) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + " 工单[" + orderId + "][" + orderNo + "] 流转人工失败，需要人工干预修复 error:" + e.getMessage(), e);
			}
		}
	}

	/**
	 * 提交工单
	 * @param user
	 * @param orderId
	 * @param orderNo
	 * @param ex8
	 * @throws Exception
	 */
	public void submitOrder(UserModel user, String orderId, String orderNo, String ex3, String ex8) throws Exception {
		String entId = user.getEpCode();
		String busiOrderId = user.getBusiOrderId();
		String schema = user.getSchemaName();

		// 生成回复内容
		String autoReplayMsg = "";
		if(!"N".equals(ex8)) {
			autoReplayMsg = OrderUtils.getReplayMsg(orderNo, schema);
			if(StringUtils.isNotBlank(autoReplayMsg)) {
				OrderUtils.addOrderFollow(orderId, "工单处理", autoReplayMsg, Constants.FOLLOW_TYPE_66, orderId, orderNo, user);
			}
		}

		// 更新工单数据
		this.getQuery().execute("update " + schema + ".C_BOX_MUSIC_STANDARD_EX set RPA_HANDLE_STATUS=?, AUTO_REPLAY_MSG=? where M_ID=?", "02", autoReplayMsg, orderId);
		this.getQuery().execute("update " + schema + ".C_BOX_MUSIC_STANDARD_BASE set EX_3=?,EX_8=? where M_ID = ?", ex3, ex8, orderId);

		IService service = ServiceContext.getService("CC_EORDER_COMPELETE_RECEIVETASK");
		JSONObject params = new JSONObject();
		params.put("orderId", orderId);
		params.put("orderNo", orderNo);
		params.put("entId", entId);
		params.put("busiOrderId", busiOrderId);
		params.put("schema", schema);

		// 退费流程变量
		JSONObject flowVar = new JSONObject();
		flowVar.put("EX_8", ex8);
		flowVar.put("EX_3", ex3);
		params.put("flowVar", flowVar.toJSONString());
		logger.info("进行工单流转 params:" + params.toJSONString());
		JSONObject result = service.invoke(params);
		logger.info("进行工单流转 result:" + JSONObject.toJSONString(result));
		if (result == null || result.getIntValue("state") != 1) {
			MinistryNoticeUtil.sendErrorNotice("orderNo[" + orderNo + "]自动处理流转异常");
			this.getQuery().execute("update " + schema + ".C_BOX_MUSIC_STANDARD_EX set RPA_HANDLE_STATUS=? where M_ID=?", "03", orderId);
		}
	}

	/**
	 * 批量更新意图处理状态
	 * @throws SQLException
	 */
	public void batchUpdateProductStatus(String schema, String operResult, List<JSONObject> recordList, String replyMsg) throws SQLException {
		for (JSONObject row : recordList) {
			String recordId = row.getString("ID");
			QueryFactory.getWriteQuery().execute("update " + schema + ".C_BOX_MUSIC_PRODUCT set DEAL_RESULT = ?,REPLY_MSG=? where ID = ?", operResult, replyMsg, recordId);
		}
	}

	/**
	 * 更新意图处理状态
	 * @throws SQLException
	 */
	public void updateProductStatus(String schema, String operResult, String recordId, String replyMsg) throws SQLException {
		QueryFactory.getWriteQuery().execute("update " + schema + ".C_BOX_MUSIC_PRODUCT set DEAL_RESULT = ?,REPLY_MSG=? where ID = ?", operResult, replyMsg, recordId);
	}

	/**
	 * 是否人工处理
	 * @param orderInfo
	 * @return
	 */
	private boolean checkIsAgentEntry(JSONObject orderInfo) {
		String agentEnterResult = orderInfo.getString("AGENT_ENTER_RESULT");
		if(StringUtils.equals(agentEnterResult, "01")) {
			return true;
		}
		return false;
	}

	/**
	 * 是否CRM归档
	 * @param orderInfo
	 * @return
	 */
	private boolean checkCrmIsClose(JSONObject orderInfo) {
		String crmIsClose = orderInfo.getString("CRM_IS_CLOSE");
		if(StringUtils.equals(crmIsClose, "1")) {
			return true;
		}
		return false;
	}

	/**
	 * 是否微信订单
	 * @param orderInfo
	 * @return
	 */
	private boolean checkIsWechatPay(JSONObject orderInfo) {
		String wechatPay = orderInfo.getString("WECHAT_PAY");
		if(StringUtils.equals(wechatPay, "Y")) {
			return true;
		}
		return false;
	}

	/**
	 * 获取微信投诉工单自动处理结果
	 * @throws SQLException
	 */
	private JSONObject getWxPlay(String schema, String orderId) throws SQLException {
		EasySQL sql = new EasySQL("select AUTO_HANDLE_RESULT,AUTO_HANDLE_MSG");
		sql.append("from " + schema + ".WX_COMPLAINT_ORDER");
		sql.append(orderId, "where ID = ?" );
		return this.getQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
	}

	/**
	 * 获取当前工单可自动处理的意图
	 * @param appealSource
	 * @param groupName
	 * @param entId
	 * @param busiOrderId
	 * @param schema
	 * @return
	 */
	public List<String> getActionList(String appealSource, String groupName, String entId, String busiOrderId, String schema) {
		List<String> actionList = new ArrayList<String>();
		String sourceActions = getSwitchBySource(entId, busiOrderId, schema, appealSource);
		if("2".equals(sourceActions)) {
			return actionList;
		}
		if(StringUtils.isNotBlank(groupName)) {
			String groupActions = getSwitchByGroupName(entId, busiOrderId, schema, groupName);
			if("2".equals(groupActions)) {
				return actionList;
			}

			for(String action : sourceActions.split(",")) {
				if(StringUtils.equalsAny(action, groupActions.split(","))) {
					actionList.add(action);
				}
			}
		} else {
			actionList = Arrays.asList(sourceActions.split(","));
		}
        return actionList;
	}

	/**
	 * 获取申诉来源自动处理开关
	 * @param entId
	 * @param busiOrderId
	 * @param schema
	 * @param appealSource 申诉来源
	 * @return
	 */
	public String getSwitchBySource(String entId, String busiOrderId, String schema, String appealSource) {
		try {
			String cacheKey = AUTO_HANDLE_APPEAL_SOURCE + entId + "_" + busiOrderId;
			String value = RedisUtil.hget(cacheKey, appealSource);
			if(StringUtils.isBlank(value)) {
				List<JSONObject> list = this.getQuery().queryForList("select STATE,ACTIONS,EXE_JSON from " + schema + ".CX_AUTO_OPEN_CONFIG where TYPE = ?", new Object[] {1}, new JSONMapperImpl());
				if(CommonUtil.listIsNotNull(list)) {
					for (JSONObject row : list) {
						String state = row.getString("STATE");
						String actions = row.getString("ACTIONS");
						if(StringUtils.isBlank(actions)) {
							actions = "99";
						}
						JSONObject exeJson = row.getJSONObject("EXE_JSON");
						String sources = exeJson.getString("sources");

						for(String source : sources.split(",")) {
							RedisUtil.hadd(cacheKey, source, "1".equals(state)?actions:state);
						}
					}
				}

				value = RedisUtil.hget(cacheKey, appealSource);
				if(StringUtils.isBlank(value)) {
					value = "2";
					RedisUtil.hadd(cacheKey, appealSource, value);
					return value;
				}
			}
			return value;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
		return "2";
	}

	/**
	 * 获取群昵称自动处理开关
	 * @param entId
	 * @param busiOrderId
	 * @param schema
	 * @param groupName 群昵称
	 * @return
	 */
	public String getSwitchByGroupName(String entId, String busiOrderId, String schema, String groupName) {
		try {
			String cacheKey = AUTO_HANDLE_GROUP_NAME + entId + "_" + busiOrderId;
			String value = RedisUtil.hget(cacheKey, groupName);
			if(StringUtils.isBlank(value)) {
				List<JSONObject> list = this.getQuery().queryForList("select GROUP_LIST from CC_RPA_GRAB_ACC_INFO where ENT_ID = ? and BUSI_ORDER_ID = ?", new Object[] {entId, busiOrderId}, new JSONMapperImpl());
				if(CommonUtil.listIsNotNull(list)) {
					for (JSONObject row : list) {
						JSONArray groupList = row.getJSONArray("GROUP_LIST");
						for(int j = 0; j < groupList.size(); j++) {
							JSONObject groupJson = groupList.getJSONObject(j);

							String gName = groupJson.getString("GROUP_NAME");
							String state = groupJson.getString("STATE");
							String actions = groupJson.getString("ACTIONS");
							if(StringUtils.isBlank(actions)) {
								actions = "99";
							}
							RedisUtil.hadd(cacheKey, gName, "1".equals(state)?actions:state);
						}
					}
				}

				value = RedisUtil.hget(cacheKey, groupName);
				if(StringUtils.isBlank(value)) {
					value = "2";
					RedisUtil.hadd(cacheKey, groupName, value);
					return value;
				}
			}
			return value;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
		return "2";
	}

	/**
	 * 获取初始用户
	 * @return
	 */
	public UserModel getDefaultUser(String entId, String busiOrderId, String schema) {
		UserModel user = new UserModel();
		user.setEpCode(entId);
		user.setBusiOrderId(busiOrderId);
		user.setSchemaName(schema);
		user.setUserAcc("rpa");
		user.setUserName("rpa机器人");
		user.setDeptCode("system");
		return user;
	}

	public EasyQuery getQuery() {
		return QueryFactory.getWriteQuery();
	}

}
