package com.yunqu.cc.mixgw.dao;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.model.WorkGroupModel;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.mixgw.base.AppDaoContext;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.util.DBFuncUtils;
import com.yunqu.cc.mixgw.util.PhoneDesenti;
import com.yunqu.cc.mixgw.util.mapper.CamelJsonMapper;
import com.yunqu.cc.oss.util.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.excel.ExcelExportUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@WebObject(name = "orderWorkGroup")
public class OrderWorkGroupDao extends AppDaoContext {
    private final Logger logger = CommonLogger.getLogger();

    @WebControl(name = "auth", type = Types.RECORD)
    public JSONObject getAuth() {
        UserModel user = UserUtil.getUser(request);
        List<String> collect = user.getWorkGroups().stream().map(WorkGroupModel::getName).collect(Collectors.toList());
        String[] split = Constants.getOrderWorkGroup().split(",");
        List<String> nomalGroup = Arrays.asList(split);
        boolean existsInNomalGroup = collect.stream().anyMatch(nomalGroup::contains);
        if (existsInNomalGroup) {
            return EasyResult.ok(false);
        } else {
            return EasyResult.ok(true);
        }
    }

    @WebControl(name = "getWorkGroup", type = Types.RECORD)
    public JSONObject getWorkGroup() {
        String orderWorkGroup = Constants.getOrderWorkGroup();
        String orderNo = param.getString("orderNo");
        if (StringUtils.isBlank(orderNo)) {
            return EasyResult.fail("工单编号为空");
        }
        EasySQL sql = new EasySQL();
        sql.appendIn(orderWorkGroup.split(","), " select t1.id, t1.name, (select " + DBFuncUtils.groupConcat(this.getQuery().getTypes(), "t2.user_acc") + " from " + getTableName("c_cf_workgroup_user") + " t2 where t2.WORKGROUP_ID = t1.id ) user_acc from "+ getTableName("c_cf_workgroup") + " t1 where t1.name ");
        JSONObject jsonObject = queryForList(sql.getSQL(), sql.getParams(), CamelJsonMapper.getInstance());
        JSONArray jsonArray = jsonObject.getJSONArray("data");
        List<JSONObject> data = JSONArray.parseArray(jsonArray.toJSONString(), JSONObject.class);

        EasySQL sql1 = new EasySQL();
        sql1.append(orderNo, "select t2.workgroup_id, t2.ADD_ACC  from " + getTableName("cx_music_oder_workgrpop_log") + " t2 where t2.order_no = ? ");
        sql1.appendIn(orderWorkGroup.split(","), "  and t2.WORKGROUP_NAME ");
        sql1.append(" group by t2.workgroup_id, t2.ADD_ACC");
        JSONObject exist = queryForList(sql1.getSQL(), sql1.getParams(), CamelJsonMapper.getInstance());
        List<JSONObject> existData = JSONArray.parseArray(exist.getJSONArray("data").toJSONString(), JSONObject.class);
        logger.info("map:" + existData);
        Map<String, String[]> existDataMap = existData.stream()
                .collect(Collectors.toMap(
                        jsonObj -> jsonObj.getString("workgroupId"),
                        jsonObj -> StringUtils.isBlank(jsonObj.getString("addAcc")) ? new String[0] : jsonObj.getString("addAcc").split(",")
                ));
        logger.info("map:" + existDataMap);
        data.forEach(json -> {
            json.put("userAcc", json.getString("userAcc").split(","));
            if(existDataMap.containsKey(json.getString("id"))) {
                json.put("existAcc", existDataMap.get(json.getString("id")));
            }
        });
//        logger.info("工作组字典：" + data);
        jsonObject.put("data", data);

        return jsonObject;
    }

    /**
     * 查处当前用户所在组或者匹配账号
     * @return
     */
    @WebControl(name = "getToCollectedList", type = Types.LIST)
    public JSONObject getToCollectedList() {

        UserModel userModel = UserUtil.getUser(request);
        String[] groupId = userModel.getWorkGroups().stream().map(WorkGroupModel::getId).toArray(String[]::new);
        String userAcc = userModel.getUserAcc();
        EasySQL sql = new EasySQL();
        sql.append("select t1.PROC_NAME AS NAME,t6.FORM_KEY_ FORM_KEY,t2.ID, t2.STATUS WORK_STATUS, t2.order_id, t1.PROC_INST_ID, t2.CREATE_TIME as en_time, t2.order_no, t1.caller, t3.ORDER_TYPE, t1.PROVINCE, t3.APPEAL_SOURCE, t1.status, t1.CREATE_TIME, CONCAT(buser.USERNAME, '-',t5.CURR_HANDLER) CURR_HANDLER, t5.CURR_NODE, t3.PRODUCT_NAME,t4.RFRS_RESULT,t4.REFUND_CHECK_RESULT" +
                " ,t4.REFUND_NUM, t5.TOTAL_DURATION_EX, t1.CUIBAN_NUM, t1.MAX_CUIBAN_TIME,t1.CREATE_ACC,t1.CREATE_NAME ");
        sql.append("from " +getTableName("c_bo_base_order") + " t1 left join " + getTableName("cx_music_oder_workgrpop_log") +" t2 on t2.ORDER_ID = t1.id ");
        sql.append(" left join " + getTableName("c_box_music_standard_base") + " t3 on t3.m_id = t2.order_id ");
        sql.append(" left join " + getTableName("c_box_music_standard_ex") + " t4 on t4.m_id = t2.order_id ");
        sql.append(" left join " + getTableName("c_bo_order_ex") + " t5 on t5.ORDER_ID = t2.order_id ");
        sql.append(" left join act_ru_task t6 on t6.PROC_INST_ID_ = t1.PROC_INST_ID " );

        sql.append("LEFT JOIN cc_user buser ON buser.USER_ACCT = t5.CURR_HANDLER ");

        sql.append(" where 1 = 1 ");
        sql.append(" and ( t1.proc_inst_id <> '' and t1.proc_inst_id is not null ) ");
//        sql.append(" and (t2.STATUS = '01' or t2.STATUS = '' or t2.STATUS is null) ");

        sql.append(" and (" +
                "(t3.ORDER_STATUS = '02' and t2.STATUS = '02' ) " + // 能看到已归档+已处理的工单
                "or" +
                "(t3.ORDER_STATUS = '01')" +
                ")");

        if (groupId.length != 0) {
            // 在组
            sql.appendIn(groupId, " and ((t2.WORKGROUP_ID ");
            sql.append(" and (t2.ADD_ACC = '' or t2.ADD_ACC = null )) ");
            // 在人
            sql.appendIn(groupId, "or (t2.WORKGROUP_ID ");
            sql.appendLike(userAcc, "and t2.ADD_ACC like ? ))" );
        } else {
            return EasyResult.ok("不存在工作组");
        }
        String workStatus = param.getString("workStatus");
        if (StringUtils.isNotBlank(workStatus)) {
            if (StringUtils.equals(workStatus, "01")) {
                sql.append(" and (t2.STATUS = '01' or t2.STATUS = '' or t2.STATUS is null )");
            } else if (StringUtils.equals(workStatus, "02")) {
                sql.append(workStatus, " and t2.STATUS = ? ");
            }
        }
        // 分派时间
        sql.append(param.getString("startTime"), " and t2.CREATE_TIME >= ?");
        sql.append(param.getString("entTime"), " and t2.CREATE_TIME <= ? ");
        // 工单编号
        sql.append(param.getString("orderNo"), " and t1.order_no = ? ");
        // 受理号码
        sql.append(param.getString("caller"), " and t1.CALLER = ? ");
        // 工单类型
        sql.append(param.getString("orderType"), "and t3.ORDER_TYPE = ? ");
        // 省份编码
        sql.append(param.getString("provinceCode"), " and t1.PROVINCE = ? ");
        // 申诉来源
        sql.append(param.getString("appealSource"), "and t3.APPEAL_SOURCE = ? ");
        // 工单状态
        sql.append(param.getString("orderStatus"), "and t1.status = ? ");
        // 产品名称
        sql.appendLike(param.getString("productName"), "and t3.PRODUCT_NAME like ? ");
        // 退赔金额
        sql.appendLike(param.getString("refundNum"), "and t4.REFUND_NUM = ? ");

        sql.append(param.getString("searchCode"), "and (t1.CALLER = ?").appendRLike(param.getString("searchCode"), " or t1.ORDER_NO like ? )");

        sql.append(" order by t2.CREATE_TIME desc ");
        logger.info("工作组工单查询sql：" + sql.getSQL() + "[]" + Arrays.toString(sql.getParams()));
        JSONObject result = queryForPageList(sql.getSQL(), sql.getParams());
        if(StringUtils.equalsAny("h5", param.getString("type"))) {
            PhoneDesenti phoneDesenti = new PhoneDesenti(true);
            JSONArray data = result.getJSONArray("data");
            if(CommonUtil.listIsNotNull(data)) {
                for (int i = 0; i < data.size(); i++) {
                    JSONObject row = data.getJSONObject(i);
                    row.put("CALLER", phoneDesenti.phoneToCC(row.getString("CALLER"), userModel.getEpCode(), userModel.getBusiOrderId(), userModel, "01"));
                }
            }
        }
        return result;
    }

}
