package com.yunqu.cc.mixgw.util;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.base.QueryFactory;
import com.yunqu.cc.mixgw.util.mapper.CamelJsonMapper;

/**
 * 工信部短信邮件通知
 */
public class MinistryNoticeUtil {
	
    private static final Logger logger = CommonLogger.getLogger();

    private final static String ERROR_TEMPLATE_CODE = "1091549368";
    
    public static void sendErrorNotice(String content) {
    	try {
	        JSONObject exJson = new JSONObject();
	        exJson.put("conten", content);
	
	        UserModel userModel = new UserModel();
	        userModel.setUserAcc("system");
	        userModel.setEpCode(Constants.getEntId());
	        userModel.setBusiOrderId(Constants.getBusiOrderId());
	        for (String phone : Constants.getErrorNoticePhone().split(",")) {
	            sendMsg(userModel, phone, ERROR_TEMPLATE_CODE, exJson);
	        }
	        
	        String title = "";
	        content = "新客服系统RPA机器人监控预警，" + content + "，有异常，请及时处理。";
	        for (String email : Constants.getErrorNoticeEmail().split(",")) {
	            sendEmail(userModel, email, title, content);
	        }
    	} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(MinistryNoticeUtil.class) + " error:" + e.getMessage(), e);
		}
    }
    
    public static void sendByWorkGroup(List<String> workGroupName, String schema, String busiId, String orderNo, String time) throws SQLException {
        if (StringUtils.equals(Constants.getAyyOrderMinistryWorkgroupOpen(), "N")) {
            logger.info("通知开关未开启");
            return;
        }

        EasySQL sql = new EasySQL();
        sql.appendIn(workGroupName.toArray(new String[0]), "select  t1.email,t1.user_acct, t1.mobile from cc_user t1 left join "+schema+".c_cf_workgroup_user  t2 on t1.user_id = t2.user_id left join "+schema+".c_cf_workgroup t3 on t3.id = t2.WORKGROUP_ID" +
                " where t2.workgroup_id and t3.name ");
        List<JSONObject> jsonObjects = QueryFactory.getWriteQuery().queryForList(sql.getSQL(), sql.getParams(), CamelJsonMapper.getInstance());
        jsonObjects = deduplicateByField(jsonObjects, "userAcct");
        logger.info("发送通知用户：");
        for (JSONObject jsonObject : jsonObjects) {
            UserModel userModel = new UserModel();
            userModel.setUserAcc("system");
            userModel.setBusiId(busiId);
            userModel.setBusiOrderId(Constants.getBusiOrderId());
            userModel.setSchemaName(schema);
            userModel.setEpCode(Constants.getEntId());
            String mobile = jsonObject.getString("mobile");
            String email = jsonObject.getString("email");
            if (StringUtils.isNotBlank(mobile)) {
                sendWorkGroupMsg(userModel, mobile, orderNo, time, false);
            }
            if (StringUtils.isNotBlank(email)) {
                sendWorkGroupEmail(userModel, email, orderNo, time, false);
            }
        }

    }

    /**
     * 催单
     * @param orderId
     * @param orderNo
     * @param procInstId
     * @param handleContent
     * @param entId
     * @param busiOrderId
     * @param schema
     * @throws ServiceException
     */
    public static void reminder(String orderId, String orderNo, String procInstId, String handleContent, JSONObject orderInfo, String entId, String busiOrderId, String schema, String preString) throws Exception {
        JSONObject param = new JSONObject();
        param.put("command", "urge");
        param.put("id", orderId);
        param.put("orderNo", orderNo);
        param.put("procInstId", procInstId);
        param.put("backup", handleContent);
        param.put("entId", entId);
        param.put("busiOrderId", busiOrderId);
        param.put("schema", schema);
        String createTime = orderInfo.getString("CREATE_TIME");
        if (StringUtils.isBlank(createTime)) {
            createTime = getOrderInfoCreateTime(schema, entId, orderNo);
        }
        logger.info("[" + orderNo + "] 创建时间：" + createTime);

        logger.info("[" + orderNo + "] 进行催单请求:" + param.toJSONString());
        IService service = ServiceContext.getService("CC_EORDER_OPERATION_HANDLE");
        JSONObject reqResult = service.invoke(param);
        logger.info("[" + orderNo + "] 进行催单响应:" + JSONObject.toJSONString(reqResult));


        // 通知提醒
        String rersAcc = orderInfo.getString("RFRS_ACC");
        String assigneeAgent = orderInfo.getString("ASSIGNEE_AGENT");
        if(StringUtils.isBlank(assigneeAgent)) {
            assigneeAgent = orderInfo.getString("HANDLE_AGENT_ACC");
        }
        if(StringUtils.isBlank(assigneeAgent)) {
            assigneeAgent = orderInfo.getString("RFRS_ACC");
        }


        // 工单处理人与退赔专员不一致
        if(StringUtils.isBlank(rersAcc) || StringUtils.equals(rersAcc, assigneeAgent)) {
            rersAcc = "";
        }

        JSONObject timeout = timeout(createTime, Constants.getAyyOrderSuperTime());
        boolean superTime = StringUtils.equals(timeout.getString("superTime"), "1");
        String superHour = timeout.getString("superHour");

        if(StringUtils.isNotBlank(assigneeAgent)) {
            // 有处理人通知到处理人
            orderInfo.put("ASSIGNEE_AGENT", assigneeAgent);
            logger.info("工单[" + orderNo + "]进行催单提醒，提醒对象：" + assigneeAgent);
            sendNotice(entId, busiOrderId, schema, orderInfo, orderNo, superHour, superTime, preString);

            String mobile = getQuery().queryForString("select  t1.MOBILE from CC_USER t1 where USER_ACCT = ?", assigneeAgent);
            if(StringUtils.isNotBlank(mobile)) {
                sendWorkGroupMsg0(entId, busiOrderId, assigneeAgent, mobile, orderNo, superHour, superTime, preString);
            }
            return;
        }

        EasySQL sql = new EasySQL("select  t1.EMAIL,t1.USER_ACCT, t1.MOBILE from CC_USER t1 ");
        sql.append("left join "+schema+".C_CF_WORKGROUP_USER  t2 on t1.USER_ID = t2.USER_ID ");
        sql.append("left join "+schema+".C_CF_WORKGROUP t3 on t3.id = t2.WORKGROUP_ID");
        sql.appendIn(Constants.getAyyOrderReminderWorkgroup().split(","), "where t2.WORKGROUP_ID and t3.NAME ");
        sql.append(rersAcc, " OR t1.USER_ACCT = ? ");
        List<JSONObject> jsonObjects = getQuery().queryForList(sql.getSQL(), sql.getParams(), CamelJsonMapper.getInstance());

        if(CommonUtil.listIsNotNull(jsonObjects)) {
            jsonObjects = deduplicateByField(jsonObjects, "userAcct");
        }

        // 内部通知
        if(jsonObjects != null && jsonObjects.size() > 0) {
            assigneeAgent = jsonObjects.stream().map(json -> json.getString("userAcct")).collect(Collectors.joining(";"));
            orderInfo.put("ASSIGNEE_AGENT", assigneeAgent);
            logger.info("工单[" + orderNo + "]进行催单提醒，提醒对象：" + assigneeAgent);
            sendNotice(entId, busiOrderId, schema, orderInfo, orderNo, superHour, superTime, preString);
        }
        // 短信通知
        for (JSONObject jsonObject : jsonObjects) {
            String userAcct = jsonObject.getString("userAcct");
            String mobile = jsonObject.getString("mobile");
            sendWorkGroupMsg0(entId, busiOrderId, userAcct, mobile, orderNo, superHour, superTime, preString);
        }
    }
    private static JSONObject getOrderInfo(String schema, String entId, String orderNo) throws SQLException {
        EasySQL sql = new EasySQL("select t1.ID ORDER_ID,t1.CREATE_TIME, t1.CONTENT,t1.ORDER_NO,t3.RPA_ACTION_DATA, t2.REFUND_REASON,");
        sql.append("t2.IS_REFUND,t2.EX_3,t2.EX_4,t2.EX_8,t4.TASK_DEF_KEY_ NODE_ID,t4.ASSIGNEE_ ASSIGNEE_AGENT,t1.PROC_INST_ID,");
        sql.append("t1.STATUS,t3.RFRS_ACC,t3.RFRS_ACC,t3.HANDLE_AGENT_ACC,t2.APPEAL_SOURCE,t5.GROUP_NAME,t3.AGENT_ENTER_RESULT");
        sql.append("from " + schema + ".C_BO_BASE_ORDER t1");
        sql.append("left join " + schema + ".C_BOX_MUSIC_STANDARD_BASE t2 on t1.ID = t2.M_ID");
        sql.append("left join " + schema + ".C_BOX_MUSIC_STANDARD_EX t3 on t1.ID = t3.M_ID");
        sql.append("left join ACT_RU_TASK t4 on t1.PROC_INST_ID = t4.PROC_INST_ID_");
        sql.append("left join CC_RPA_GRAB_MSG_LIST t5 on t3.GRAB_SERAIL_ID = t5.MSG_ID");
        sql.append("where 1=1");
        sql.append(orderNo, "and t1.ORDER_NO = ?");
        return QueryFactory.getQuery(entId).queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
    }
    private static String getOrderInfoCreateTime(String schema, String entId, String orderNo) throws SQLException {
        EasySQL sql = new EasySQL("select t1.ID ORDER_ID,t1.CREATE_TIME");
        sql.append("from " + schema + ".C_BO_BASE_ORDER t1");
        sql.append("where 1=1");
        sql.append(orderNo, "and t1.ORDER_NO = ?");
        return QueryFactory.getQuery(entId).queryForRow(sql.getSQL(), sql.getParams(), CamelJsonMapper.getInstance()).getString("createTime");
    }


    public static  JSONObject timeout(String createTime, String timeout) {
        JSONObject result = new JSONObject();
        String currentDateStr = DateUtil.getCurrentDateStr();
        String createTimeAddHour = DateUtil.addHour("yyyy-MM-dd HH:mm:ss", createTime, Integer.parseInt(timeout));
        int i = DateUtil.bwSeconds(currentDateStr,createTimeAddHour);
        boolean superTime;
        String superHour;
        if (i >= 0) {
            superTime = true;
            superHour = String.valueOf(i / 3600);
        } else {
            superTime = false;
            superHour = createTimeAddHour;
        }
        result.put("superTime", superTime?"1":"0");
        result.put("superHour", superHour);
        return result;
    }
    public static List<JSONObject> deduplicateByField(List<JSONObject> jsonObjectList, String fieldName) {
        Set<Object> seenValues = new HashSet<>();
        return jsonObjectList.stream()
                .filter(jsonObject -> seenValues.add(jsonObject.get(fieldName)))
                .collect(Collectors.toList());
    }

    public static void main(String[] args) {
        List<JSONObject> list = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("1", "1");
        jsonObject.put("2", "3");
        list.add(jsonObject);
        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("1", "3");
        list.add(jsonObject1);
        JSONObject jsonObject2 = new JSONObject();
        jsonObject2.put("1", "2");
        list.add(jsonObject2);
        System.out.printf(list.stream().map(json -> json.getString("1")).collect(Collectors.toList()).toString());


    }
    private static boolean sendWorkGroupMsg0(String entId, String busiOrderId, String acc, String phone, String orderNo, String time, boolean superTime, String pre) {
        UserModel userModel = new UserModel();
        userModel.setUserAcc(acc);
        userModel.setEpCode(entId);
        userModel.setBusiOrderId(busiOrderId);
        JSONObject exJson = new JSONObject();
        exJson.put("title", orderNo + pre);
        exJson.put("time", time);

        return sendMsg(userModel, phone, superTime ? Constants.ORDER_HANDLE_SUPER_TIME_TEMALATE : Constants.ORDER_HANDLE_NO_TIME_TEMALATE, exJson);
    }

    private static boolean sendWorkGroupMsg(UserModel user, String phone, String orderNo, String time, boolean superTime) {
        JSONObject exJson = new JSONObject();
        exJson.put("title", "工信部" + orderNo);
        exJson.put("time", time);
        return sendMsg(user, phone, superTime ? Constants.ORDER_HANDLE_SUPER_TIME_TEMALATE : Constants.ORDER_HANDLE_NO_TIME_TEMALATE, exJson);
    }
    private static boolean sendWorkGroupEmail(UserModel user, String noticeNumber, String orderNo, String time, boolean superTime) {
        String title;
        String content;
        if (superTime) {
            title = "待办工单超时提醒";
            content = "客服系统待办工单工信部"+orderNo+"，已超时"+time+"小时，请及时处理。";
        } else {
            title = "待办工单即将超时提醒";
            content = "客服系统待办工单工信部"+orderNo+"，将于"+time+"超时，请及时处理。";
        }
        return sendEmail( user, noticeNumber, title, content );
    }

    private static boolean sendNotice(String entId, String busiOrderId, String schema, JSONObject orderInfo, String orderNo, String time, boolean superTime, String pre) throws ServiceException {
        String title;
        String content;

        if (superTime) {
            title = "待办工单超时提醒";
            content = "客服系统待办工单"+orderNo +pre +"，已超时"+time+"小时，请及时处理。";
        } else {
            title = "待办工单即将超时提醒";
            content = "客服系统待办工单"+orderNo+ pre + "，将于"+time+"超时，请及时处理。";
        }
        return addNotice(entId, busiOrderId, schema, title, content, orderInfo);
    }
    /**
     * 发送邮件
     * @param noticeNumber 接受邮箱
     * @param title 标题
     * @param content 内容
     * @return
     */
    public static boolean sendEmail(UserModel user, String noticeNumber, String title, String content) {
        JSONObject rtJson = new JSONObject();
        try {
            JSONObject param = new JSONObject();
            param.put("serialId", ServiceID.EMAIL_INTERFACE);
            param.put("command", ServiceCommand.REQ_SEND_EMAIL);
            param.put("title", title);
            param.put("content", content);
            param.put("to", noticeNumber); // 接受账号
            param.put("source", "99");// 99-标准通知
            String sendTime = EasyCalendar.newInstance().getDateTime("-");
            param.put("sendTime", sendTime);
            param.put("busiId", user.getBusiId());
            param.put("userAcc", user.getUserAcc());
            param.put("haveAttachment", "N");// N-不存在附件

//            param.put("from", emailAcc); // 发送邮件的账号
            param.put("schema",user.getSchemaName());
            param.put("epCode", user.getEpCode());
            param.put("busiOrderId", user.getBusiOrderId());
            logger.info("请求邮件接口参数：" + param);
            IService service = ServiceContext.getService(ServiceID.EMAIL_INTERFACE);
            JSONObject result = service.invoke(param);
            if (result.getString("respCode").equals(GWConstants.RET_CODE_SUCCESS)) {
                rtJson.put("isSuccess", "1");
                rtJson.put("respDesc", "邮件消息发送成功！");

                logger.info( "[" + noticeNumber + "] 邮件消息发送成功！");
                return true;
            } else {
                rtJson.put("isSuccess", "2");
                rtJson.put("respDesc", "[" + noticeNumber + "] 邮件消息发送失败！接口返回码为：" + result.getString("respCode"));
                logger.error( "[" + noticeNumber + "] 邮件消息发送失败！接口返回码为：" + result.getString("respCode"));
                return false;
            }
        } catch (Exception e) {
            rtJson.put("isSuccess", "2");
            rtJson.put("respDesc", "邮件消息发送失败！系统异常!");
            logger.error( "[" + noticeNumber + "] 邮件消息发送接口异常！" + e.getMessage(), e);
            return false;
        }
    }



    /**
     * 发送信息
     * @param user 发送者
     * @param phone 发送者
     * @param code 模板编号
     * @param exJson 编码匹配内容 {"random":"1111", "param": "2222"}
     * @return
     */
    public static boolean sendMsg(UserModel user, String phone, String code, JSONObject exJson) {
        try {
            if(StringUtils.isNotBlank(phone)){
                //发送短信
                JSONObject param = new JSONObject();
                param.put("userAcc",user.getUserAcc());
                param.put("epCode",user.getEpCode());
                param.put("busiOrderId",user.getBusiOrderId());
                param.put("sender", Constants.APP_NAME);
                param.put("source", "1");
                param.put("command", ServiceCommand.SENDMESSAGE);
                // 接收电话与内容
                JSONObject json = new JSONObject();
                json.put("receiver", phone);// 接收号码
                json.put("content","@"+code);//模板编号
                json.put("exJson",exJson);
                param.put("receivers", Collections.singletonList(json));

                IService service = ServiceContext.getService(ServiceID.SMSGW_INTEFACE);
                JSONObject result = service.invoke(param);
                logger.info("[LoginSmsService]:发送短信：接收号码"+phone+",result:"+result);
                if (result.getString("respCode").equals(GWConstants.RET_CODE_SUCCESS)) {
                    return true;
                } else {
                    return false;
                }
            }
            logger.error("[LoginSmsService]:发送短信手机号码为空");
            return false;
        } catch (Exception e) {
            logger.error("[LoginSmsService]:发送短信报错"+e.getMessage()+e.getCause(),e);
            return false;
        }
    }

    private static boolean addFollowNotice(String entId, String busiOrderId, String schema, String handleContent, String acc) throws ServiceException {

        if(StringUtils.isNotBlank(acc)) {
            logger.info("[" + acc + "] 触发通知提醒,提醒对象:" + acc);
            JSONObject param = new JSONObject();
            param.put("serialId", 		ServiceID.NOTICE_INTERFACE);
            param.put("command", 		ServiceCommand.NOTICE_ADD_USER_NOTICE);
            param.put("sender", 		Constants.APP_NAME);
            param.put("receiverType", 	"01");// 01-个人 02-部门 03-所有人
            param.put("userAcc", 		acc);// 存储接收通知的账号,如有多个用;分割
            param.put("deptCode", 		"000");
            param.put("type", 			"系统通知");
            param.put("module", 		Constants.APP_NAME);
            param.put("title", 			"催单提醒");
            param.put("content", 		handleContent);
            param.put("url", "");// 点击通知展示的页面
            param.put("urlOpenType", "02");// 点击通知展示的页面
            param.put("method", "03");// 通知推送方式,01-汇总显示,02-单条弹屏显示,03-单条显示
            param.put("senderId", 		"system");// system
            param.put("senderName", 	"system");// system
            param.put("createUserAcc", 	"system");// system
            param.put("createUserDeptCode","1");
            param.put("epCode", entId);
            param.put("busiOrderId", busiOrderId);
            param.put("schema", schema);
            logger.info("[" + acc + "] 触发通知提醒 param:" + JSONObject.toJSONString(param));
            IService service = ServiceContext.getService(ServiceID.NOTICE_INTERFACE);
            JSONObject result = service.invoke(param);
            logger.info("[" + acc + "] 触发通知提醒 result:" + JSONObject.toJSONString(result));
            return true;
        } else {
            logger.info("[" + acc + "] 触发通知提醒失败，当前工单无处理坐席");
            return false;
        }
    }

    /**
     * 追诉通知
     * @param entId
     * @param busiOrderId
     * @param schema
     * @param noticeTitle 通知标题
     * @param noticeContent 通知内容
     * @param oldOrderInfo 工单信息
     * @throws ServiceException
     */
    public static boolean addNotice(String entId, String busiOrderId, String schema, String noticeTitle, String noticeContent, JSONObject oldOrderInfo) throws ServiceException {
        String orderId = oldOrderInfo.getString("ORDER_ID");
        String orderNo = oldOrderInfo.getString("ORDER_NO");

        // 通知提醒
        String assigneeAgent = oldOrderInfo.getString("ASSIGNEE_AGENT");
        if(StringUtils.isBlank(assigneeAgent)) {
            assigneeAgent = oldOrderInfo.getString("HANDLE_AGENT_ACC");
        }
        if(StringUtils.isBlank(assigneeAgent)) {
            assigneeAgent = oldOrderInfo.getString("RFRS_ACC");
        }
        if(StringUtils.isNotBlank(assigneeAgent)) {
            logger.info("[" + orderNo + "] 触发通知提醒,提醒对象:" + assigneeAgent);
            JSONObject param = new JSONObject();
            param.put("serialId", 		ServiceID.NOTICE_INTERFACE);
            param.put("command", 		ServiceCommand.NOTICE_ADD_USER_NOTICE);
            param.put("sender", 		Constants.APP_NAME);
            param.put("receiverType", 	"01");// 01-个人 02-部门 03-所有人
            param.put("userAcc", 		assigneeAgent);// 存储接收通知的账号,如有多个用;分割
            param.put("deptCode", 		"000");
            param.put("type", 			"系统通知");
            param.put("module", 		Constants.APP_NAME);
            param.put("title", 			noticeTitle);
            param.put("content", 		noticeContent);
            param.put("url", "");// 点击通知展示的页面
            param.put("urlOpenType", "02");// 点击通知展示的页面
            param.put("method", "03");// 通知推送方式,01-汇总显示,02-单条弹屏显示,03-单条显示
            param.put("senderId", 		"system");// system
            param.put("senderName", 	"system");// system
            param.put("createUserAcc", 	"system");// system
            param.put("createUserDeptCode","1");
            param.put("epCode", entId);
            param.put("busiOrderId", busiOrderId);
            param.put("schema", schema);

            logger.info("[" + orderNo + "] 触发通知提醒 param:" + JSONObject.toJSONString(param));
            IService service = ServiceContext.getService(ServiceID.NOTICE_INTERFACE);
            JSONObject result = service.invoke(param);
            logger.info("[" + orderNo + "] 触发通知提醒 result:" + JSONObject.toJSONString(result));
            RpaOrderLogUtils.saveLogByHandler(entId, schema, orderId, RpaOrderLogUtils.OPER_RESULT_SUCC, DateUtil.getCurrentDateStr() + "[用户追诉] 触发通知提醒", busiOrderId);
            return true;
        } else {
            logger.info("[" + orderNo + "] 触发通知提醒失败，当前工单无处理坐席");
            RpaOrderLogUtils.saveLogByHandler(entId, schema, orderId, RpaOrderLogUtils.OPER_RESULT_FAIL, DateUtil.getCurrentDateStr() + "[用户追诉] 触发通知提醒失败", busiOrderId);
            return false;
        }
    }

    public static EasyQuery getQuery() {
        return QueryFactory.getWriteQuery();
    }

}
