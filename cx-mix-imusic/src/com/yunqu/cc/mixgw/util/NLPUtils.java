package com.yunqu.cc.mixgw.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yq.busi.common.util.http.HttpResp;
import com.yq.busi.common.util.http.HttpUtil;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.service.aigc.AIGCService;

public class NLPUtils {
	
	private static Logger logger = CommonLogger.getLogger("rpa");

	/**
	 * 自动识别逻辑-NLP
	 * @param content
	 * @param phoneNumber
	 * @param productList
	 * @return
	 */
	public static JSONObject extractInfoSingle(String schema, String content, String phoneNumber, List<JSONObject> productList, StringBuilder sb) {
		JSONObject rtJson = new JSONObject();
		try {
			String url = Constants.getNlpExtractInfoAddr() + Constants.EXTRACT_INFO_SINGLE;
			String msgId = IDGenerator.getDefaultNUMID();
			String timestamp = System.currentTimeMillis() +"";
			
			JSONObject params = new JSONObject();
			params.put("msgId", msgId);
			params.put("timestamp", timestamp);
			JSONObject input = new JSONObject();
			input.put("content", content);
			input.put("phoneNumber", phoneNumber);
			// 产品列表
			input.put("productList", productList);
			params.put("input", input);
			params.put("appId", Constants.APP_NAME);

			logger.info("请求NLP工单提参接口["+ url +"]请求参数: "+ params.toJSONString());
			HttpResp resp = HttpUtil.sendPost(url, params.toJSONString());
			logger.info("请求NLP工单提参接口["+ url +"]返回内容: "+ JSONObject.toJSONString(resp));
			String resultStr = resp.getResult();
			if (StringUtils.isNotBlank(resultStr)) {
				JSONObject result = JSON.parseObject(resultStr);
				JSONArray response = result.getJSONArray("response");
				if(CommonUtil.listIsNotNull(response)) {
					for (int i=0; i<response.size(); i++) {
						JSONObject taskInfo = response.getJSONObject(i);
						String task = taskInfo.getString("task");
						if ("意图识别".equals(task) && taskInfo.containsKey("result")) {
							List<JSONObject> rpaActionList = new ArrayList<JSONObject>();
							
							List<JSONObject> resultList = taskInfo.getJSONArray("result").toJavaList(JSONObject.class);
							for(JSONObject row : resultList) {
								String product = row.getString("product");
								String action = row.getString("action");
								String number = row.getString("number");
								
								action = getAction(action);
								
								JSONObject productInfo = null;
								// 非屏蔽场景，没有识别到产品时不予处理
								if(Constants.RPA_ACTION_SCREEN.equals(action)) {
									productInfo = new JSONObject();
								} else if(StringUtils.isNotBlank(product)){
									for (JSONObject productJson : productList) {
										String productId = productJson.getString("productId");
										String productName = productJson.getString("productName");
										if(content.indexOf(productName) > -1) {
											productInfo = JSONObject.parseObject(JSONObject.toJSONString(productJson));
											break;
										} else {
											JSONObject info = AIGCService.getInstance().getMarkDataByTypeBusiId(schema, Constants.MODEL_BUSI_TYPE_03, productId, content);
											if(info != null && info.containsKey("productId")) {
												productInfo = JSONObject.parseObject(JSONObject.toJSONString(productJson));
												break;
											}
										}
									}
								}
								
								if(productInfo == null) {
									continue;
								}
								
								
								productInfo.put("action", action);
								productInfo.put("number", number);
								rpaActionList.add(productInfo);
							}
							rtJson.put("rpaActionList", rpaActionList);
							if(rpaActionList.size() > 0) {
								return rtJson;
							}
						}
					}
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return null;
	}
	
	/**
	 * 自动识别逻辑-硬编码
	 * @param content
	 * @param phoneNumber
	 * @param productList
	 * @return
	 */
	public static JSONObject extractInfoSingleCode(String schema, String content, String phoneNumber, List<JSONObject> productList, boolean isFlag, StringBuilder sb) {
		// 生成临时数据
		JSONObject rtJson = new JSONObject();
		List<JSONObject> rpaActionList = new ArrayList<JSONObject>();

		// 意图
		JSONObject data = AIGCService.getInstance().getMarkDataByType(schema, Constants.MODEL_BUSI_TYPE_02, content);
		JSONArray actionList = data.getJSONArray("actionList");
		if(CommonUtil.listIsNull(actionList)) {
			// 未识别到意图，直接返回
			logger.info("意图识别失败: content:" + content);
			sb.append("意图识别失败: content:" + content + "<br/>");
			return null;
		}

		logger.info("content[" + content + "] 命中意图成功，命中意图数:" + actionList.size());
		sb.append("命中意图成功，命中意图数:" + actionList.size() + "<br/>");
		
		int hitIntentCount = actionList.size();
		if(hitIntentCount == 1 && StringUtils.equals(Constants.RPA_ACTION_SCREEN, actionList.getString(0))) {
			// 执行屏蔽操作
			JSONObject productInfo = new JSONObject();
			productInfo.put("action", Constants.RPA_ACTION_SCREEN);
			productInfo.put("number", phoneNumber);
			rpaActionList.add(productInfo);
			rtJson.put("rpaActionList", rpaActionList);
			return rtJson;
		} else if(CommonUtil.listIsNull(productList)) {
			if(!isFlag) {
				logger.info("该手机号未识别到产品信息: content:" + content);
			}
			sb.append("该手机号未识别到产品信息<br/>");
			return null;
		}
		
		
		// 命中产品
		JSONObject hitProduct = null;
		// 命中产品数
		int hitProductCount = 0;
		for (JSONObject productJson : productList) {
			String productId = productJson.getString("productId");
			String productName = productJson.getString("productName");
			if(content.indexOf(productName) > -1) {
				hitProductCount++;
				hitProduct = productJson;
			} else {
				JSONObject info = AIGCService.getInstance().getMarkDataByTypeBusiId(schema, Constants.MODEL_BUSI_TYPE_03, productId, content);
				if(info != null && info.containsKey("productId")) {
					hitProductCount++;
					hitProduct = productJson;
				}
			}
		}
		
		logger.info("content[" + content + "] 命中产品成功，命中产品数:" + hitProductCount);
		sb.append("命中产品成功，命中产品数:" + hitProductCount + "<br/>");
		if(hitProductCount == 1) {
			for(int i = 0; i < actionList.size(); i++) {
				String action = actionList.getString(i);
				
				JSONObject productInfo = JSONObject.parseObject(JSONObject.toJSONString(hitProduct));
				productInfo.put("action", action);
				productInfo.put("number", phoneNumber);
				if(Constants.RPA_ACTION_REFUND.equals(action)) {
					String expenses = productInfo.getString("expenses");
					
			        String refundBills = RefundAmountExtractor.getRefundBills(content, expenses);
			        if(StringUtils.isBlank(refundBills)) {
			    		logger.info("content[" + content + "] 退费金额匹配失败，进行转人工");
			    		return null;
			        }
	        		productInfo.put("refundBills", refundBills);
				} else if(Constants.RPA_ACTION_CANCEL.equals(action)) {
					// 退订操作需要限制产品为在用业务来源
					if(!StringUtils.equals("zy", productInfo.getString("apiType"))) {
						String ununTime = productInfo.getString("ununTime");
						
//						订购记录已查询所有退订时间，无需再去查询退订记录
//						String productId = productInfo.getString("productId");
//						String productCode = productInfo.getString("productCode");
//						String orderTime = productInfo.getString("orderTime");
//						if(StringUtils.isBlank(ununTime)) {
//							List<JSONObject> ununOrderList = ApiUtils.getTDProductList(phoneNumber, orderTime, DateUtil.getCurrentDateStr(), Constants.OP_ACC_RPA);
//							for (JSONObject unOrderInfo : ununOrderList) {
//								if(StringUtils.equalsAny(unOrderInfo.getString("productId"), productId, productCode) || StringUtils.equalsAny(unOrderInfo.getString("feeProductId"), productId, productCode)) {
//									ununTime = unOrderInfo.getString("ununTime");
//									break;
//								}
//							}
//						}
						
						if(StringUtils.isBlank(ununTime)) {
							// 当前产品非在用业务，不可退订
							logger.error("error: 当前产品非在用业务，不可退订 productName[" + productInfo.getString("productName") + "] apiType[" + productInfo.getString("apiType") + "]");
							sb.append("当前产品非在用业务，不可退订productName[" + productInfo.getString("productName") + "] apiType[" + productInfo.getString("apiType") + "]<br/>");
							return null;
						}
						productInfo.put("refundTime", ununTime);
					}
				} else if(Constants.RPA_ACTION_SEARCH_CHANNEL.equals(action)) {
					String ununTime = productInfo.getString("ununTime");
					if(StringUtils.isBlank(ununTime)) {
                        // 当前产品未退订，查询当前用户是否销户
						String beginTime = "";
						String orderTime = productInfo.getString("orderTime");
						if(StringUtils.isBlank(orderTime)) {
							// 订购时间为空，默认查询4年前的数据
							beginTime = DateUtil.addMonth(DateUtil.TIME_FORMAT_YMD, DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD), -48);
						} else {
							beginTime = orderTime.substring(0, 10);
						}
						String endTime = DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD);

						// 是否销户
						boolean isCloseAccountFlag = false;
						// 查询开销户记录
						List<JSONObject> accCreateList = ApiUtils.queryColorRingAccCreate(phoneNumber, action, beginTime, endTime);
						if(CommonUtil.listIsNotNull(accCreateList)) {
							// 产品类型 0 视频 1音频 -1 未识别
							String vringType = productInfo.getString("vringType");
							if(StringUtils.isBlank(vringType) || StringUtils.equals(vringType, "-1")) {
								// 当前产品非在用业务，不可退订
								logger.error("error: 当前产品未退订并未识别到音频/视频，进行转人工 productName[" + productInfo.getString("productName") + "] apiType[" + productInfo.getString("apiType") + "]");
								sb.append("当前产品未退订并未识别到音频/视频，进行转人工 productName[" + productInfo.getString("productName") + "] apiType[" + productInfo.getString("apiType") + "]<br/>");
								return null;
							}

							accCreateList = accCreateList.stream().filter(t -> t.getString("business_type").contains(StringUtils.equals(vringType, "0") ? "视频" : "音频")).collect(Collectors.toList());
							if(CommonUtil.listIsNotNull(accCreateList)) {
								// 将开销户记录信息添加到productInfo中
								JSONObject newAccCreateRow = accCreateList.get(0);
								String actionDesc = newAccCreateRow.getString("action_desc");
								String recordTime = newAccCreateRow.getString("recordtime");
								if(actionDesc.indexOf("销户") > -1) {
									isCloseAccountFlag = true;
									productInfo.put("isCloseAccount", "1");
									productInfo.put("closeAccountTime", recordTime);
								}
							}

						}

						if(!isCloseAccountFlag) {
							// 未退订并且未销户进行转人工
							// 当前产品非在用业务，不可退订
							logger.error("error: 当前产品未进行退订，并且当前用户未销户 productName[" + productInfo.getString("productName") + "] apiType[" + productInfo.getString("apiType") + "]");
							sb.append("当前产品未进行退订，并且当前用户未销户 productName[" + productInfo.getString("productName") + "] apiType[" + productInfo.getString("apiType") + "]<br/>");
							return null;
						}
                    } else {
						productInfo.put("isCloseAccount", "0");
					}
				}
				rpaActionList.add(productInfo);
			}
			rtJson.put("rpaActionList", rpaActionList);
			return rtJson;
		} else if(hitProductCount == 0 && actionList.size() > 0) {
			// 未匹配到产品名称，匹配退费金额和产品缴费总价格
	        String refundBills = RefundAmountExtractor.getRefundBills(content, null);
	        if(StringUtils.isNotBlank(refundBills)) {
	        	String endTime = DateUtil.getCurrentDateStr();
	        	String beginTime = DateUtil.addMonth(DateUtil.TIME_FORMAT, endTime, -60);
	        	List<JSONObject> billList = ApiUtils.getInfoFeeBill(phoneNumber, beginTime, endTime, "system");
	        	
	        	Map<String, List<JSONObject>> productBillMap = billList.stream().collect(Collectors.groupingBy(t -> t.getString("productId")));
	        	
	        	String hitProductId = "";
	        	for(String productId : productBillMap.keySet()) {
	        		List<JSONObject> productBillList = productBillMap.get(productId);
	        		
	        		double produtFee = productBillList.stream().collect(Collectors.summingDouble(t -> t.getDoubleValue("fee")));
	        		if(Double.parseDouble(refundBills) == produtFee) {
	        			if(StringUtils.isBlank(hitProductId)) {
	        				hitProductId = productId;
	        			} else {
	        				logger.error("匹配话费金额存在重复命中[" + hitProductId + "] [" + productId + "]");
							sb.append("error: 匹配话费金额存在重复命中[" + hitProductId + "] [" + productId + "]<br/>");
							hitProductId = null;
							break;
	        			}
	        		}
	        		
	        	}
	        	if(StringUtils.isBlank(hitProductId)) {
	        		for (JSONObject productJson : productList) {
	        			if(!StringUtils.equals("zy", productJson.getString("apiType"))) {
	        				continue;
	        			}
						if(Double.parseDouble(refundBills) == productJson.getDoubleValue("expenses")) {
							String productId = productJson.getString("productId");
							if(StringUtils.isBlank(hitProductId)) {
		        				hitProductId = productId;
		        			} else {
		        				logger.error("匹配话费金额存在重复命中[" + hitProductId + "] [" + productId + "]");
								sb.append("error: 匹配话费金额存在重复命中[" + hitProductId + "] [" + productId + "]<br/>");
								
								hitProductId = null;
								break;
		        			}
						}
					}
	        	}
	        	
	        	if(StringUtils.isNotBlank(hitProductId)) {
	        		for (JSONObject productJson : productList) {
						if(StringUtils.equalsAny(hitProductId, productJson.getString("productId"), productJson.getString("feeProductId"))) {
							hitProduct = productJson;
							break;
						}
					}
	        		
	        		if(hitProduct == null) {
        				logger.error("error: 未匹配到话单对应的产品ID[" + hitProductId + "]");
						sb.append("error: 未匹配到话单对应的产品ID[" + hitProductId + "]<br/>");
						return null;
	        		}
	        		
	        		for(int i = 0; i < actionList.size(); i++) {
	    				String action = actionList.getString(i);
	    				
	    				JSONObject productInfo = JSONObject.parseObject(JSONObject.toJSONString(hitProduct));
	    				productInfo.put("action", action);
	    				productInfo.put("number", phoneNumber);
	    				if(Constants.RPA_ACTION_REFUND.equals(action)) {
	    	        		productInfo.put("refundBills", refundBills);
	    				} else if(Constants.RPA_ACTION_CANCEL.equals(action)) {
	    					// 退订操作需要限制产品为在用业务来源
	    					if(!StringUtils.equals("zy", productInfo.getString("apiType"))) {
	    						// 当前产品非在用业务，不可退订
	    		        		logger.error("error: 当前产品非在用业务，不可退订 productName[" + productInfo.getString("productName") + "] apiType[" + productInfo.getString("apiType") + "]");
	    						sb.append("当前产品非在用业务，不可退订productName[" + productInfo.getString("productName") + "] apiType[" + productInfo.getString("apiType") + "]<br/>");
	    						return null;
	    					}
	    				}
	    				rpaActionList.add(productInfo);
	    			}
	    			rtJson.put("rpaActionList", rpaActionList);
	    			return rtJson;
	        	}
	        }
		}
		sb.append("不支持自动处理");
		return null;
	}
	
	/**
	 * 2.2.	相似度单条匹配接口
	 * @param query
	 * @param title
	 * @param phoneNumber
	 * @return
	 */
	public static JSONObject textMatchingSingle(String query,String title) {
		try {
			String url =  Constants.getNlpTextMatchingAddr();
			String msgId = IDGenerator.getDefaultNUMID();
			String timestamp = System.currentTimeMillis() +"";
			
			JSONObject params = new JSONObject();
			params.put("msgId", msgId);
			params.put("timestamp", timestamp);
			
			JSONObject input = new JSONObject();
			input.put("query", query);
			input.put("title", title);
			
			params.put("input", input);

			logger.info("请求NLP相似度单条匹配接口请求参数: "+ params.toJSONString());
			HttpResp resp = HttpUtil.sendPost(url, params.toJSONString(), HttpUtil.TYPE_JSON);
			logger.info("请求NLP相似度单条匹配接口接口返回内容: "+ JSONObject.toJSONString(resp));
			String result = resp.getResult();
			if (StringUtils.isNotBlank(result)) {
				return JSON.parseObject(result);
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return null;
	}
	
	private static String getAction(String action) {
		if("查询".equals(action)) {
			return Constants.RPA_ACTION_SEARCH;
		} else if("屏蔽".equals(action)) {
			return Constants.RPA_ACTION_SCREEN;
		} else if("退订".equals(action)) {
			return Constants.RPA_ACTION_CANCEL;
		} else if("退费".equals(action)) {
			return Constants.RPA_ACTION_REFUND;
		} else {
			return action;
		}
	}
	
	private static boolean compareBill(String bill1, String bill2) {
		if(StringUtils.isAnyBlank(bill1, bill2)) {
			return false;
		}
		
		double d1 = Double.parseDouble(bill1);
		double d2 = Double.parseDouble(bill2);
		
		return d1 == d2;
	}
	
}
