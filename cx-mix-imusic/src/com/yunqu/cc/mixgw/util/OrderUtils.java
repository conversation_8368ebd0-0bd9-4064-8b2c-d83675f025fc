package com.yunqu.cc.mixgw.util;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.AttachmentUtil;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yq.busi.common.util.cache.RedisUtil;
import com.yq.busi.common.util.http.HttpResp;
import com.yq.busi.common.util.http.HttpUtil;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.base.QueryFactory;
import com.yunqu.cc.oss.base.OSSConstants;
import com.yunqu.cc.oss.model.AttachmentFile;

public class OrderUtils {

	private static Logger logger = CommonLogger.logger;
	private static Logger refundLogger = CommonLogger.getLogger("refund");

	/**
	 * 获取处理内容
	 * @param orderNo 工单编号
	 * @param schema 数据库名称
	 * @return
	 * @throws SQLException
	 */
	public static String getReplayMsg(String orderNo, String schema) throws SQLException {
		// 退费 > 退订 > 查询 > 屏蔽
		List<JSONObject> rpaActionList = QueryFactory.getWriteQuery().queryForList("select PRODUCT_ID,ACTION,REPLY_MSG,CLAIMANT from " + schema + ".C_BOX_MUSIC_PRODUCT where ORDER_NO = ? order by PRODUCT_ID,(case when ACTION = '03' then '99' else ACTION end)", new Object[] { orderNo }, new JSONMapperImpl());

		String replyMsg = "";
		// 不同产品生成不同回复语
		Map<String, List<JSONObject>> productActionList = rpaActionList.stream().collect(Collectors.groupingBy(t -> t.getString("PRODUCT_ID")));
		for(List<JSONObject> actionList : productActionList.values()) {
			String action = "";
			for (JSONObject product : actionList) {
				String productReplyMsg = "";
				if(StringUtils.isBlank(action)) {
					action = product.getString("ACTION");
					productReplyMsg = product.getString("REPLY_MSG");

					if(StringUtils.isBlank(productReplyMsg)) {
						action = "";
						continue;
					}

					// 渠道退赔直接拼接
					if(StringUtils.equals(product.getString("CLAIMANT"), Constants.CLAIMANT_01)) {
						action = "";
						replyMsg += productReplyMsg + "\n\n";
						continue;
					}
				} else {
					if("03".equals(product.getString("ACTION"))) {
						productReplyMsg = "已屏蔽业务\n";
					}
				}
				replyMsg += productReplyMsg + "\n";
			}
		}
		return replyMsg;
	}

	public static JSONObject getOrderInfo(String orderId, String entId, String busiOrderId, String schema) {
		try {
			EasySQL sql = new EasySQL();
			sql.append("select t1.ID ORDER_ID, t1.ORDER_NO, t1.CALLER, t2.*, t3.REFUND_NUM,t3.PRODUCT_DATA, t3.RPA_ACTION_DATA, t3.RPA_HANDLE_STATUS,t3.AGENT_ENTER_RESULT,t3.WECHAT_PAY,t3.WECHAT_PAY_NO,t3.CRM_IS_CLOSE ");
			sql.append("from "+ schema +".C_BO_BASE_ORDER t1");
			sql.append("left join "+ schema +".C_BOX_MUSIC_STANDARD_BASE t2 on t1.ID=t2.M_ID");
			sql.append("left join "+ schema +".C_BOX_MUSIC_STANDARD_EX t3 on t1.ID=t3.M_ID");
			sql.append("where 1=1");
			sql.append(orderId, "and t1.ID=?", false);
			sql.append(entId, "and t1.EP_CODE=?", false);
			sql.append(busiOrderId, "and t1.BUSI_ORDER_ID=?", false);

			EasyQuery query = QueryFactory.getReadQuery();
			JSONObject row = query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			return row;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return null;
	}

	public static List<JSONObject> getOrderProductList(String orderNo, String entId, String busiOrderId, String schema) {
		try {
			EasySQL sql = new EasySQL();
			sql.append("select t1.*");
			sql.append("from "+ schema +".C_BOX_MUSIC_PRODUCT t1");
			sql.append("where 1=1");
			sql.append(orderNo, "and t1.ORDER_NO=?", false);
			sql.append(entId, "and t1.ENT_ID=?", false);
			sql.append(busiOrderId, "and t1.BUSI_ORDER_ID=?", false);
			EasyQuery query = QueryFactory.getReadQuery();
			return query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return null;
	}

	public static boolean isFunctionProduct(String productType, String productId,String productCode, Integer refundNum) {

		String ayyNofunctionProductId = Constants.getAyyNofunctionProductId();
		String[] nofunctionProductIds = ayyNofunctionProductId.split(",");
		List<String> nofunctionProductIdList = Stream.of(nofunctionProductIds).collect(Collectors.toList());
		if (refundNum == 0
				&&  StringUtils.equals(productType, "3") && (StringUtils.startsWith(productId, "7") || StringUtils.startsWith(productCode, "7"))
				&& !nofunctionProductIdList.contains(productId)) {
			return true;
		} else {
			return false;
		}
	}

	public static List<String> getProductShuttleType(String orderNo, String entId, String busiOrderId, String schema) {
		try {
			// 等待一秒
//			Thread.sleep(1000);

			EasyQuery query = QueryFactory.getQuery(entId);

			EasySQL sql = new EasySQL();
			sql.append("select group_concat(distinct t1.SHUTTLE_TYPE) SHUTTLE_TYPE ");
			sql.append("from "+ schema +".C_BOX_MUSIC_PRODUCT t1 ");
			sql.append("where 1=1");
			sql.append(orderNo, "and t1.ORDER_NO=?");
			sql.append(entId, "and t1.ENT_ID=?");
			sql.append(busiOrderId, "and t1.BUSI_ORDER_ID=?");
			sql.append("ORDER BY t1.SHUTTLE_TYPE ASC");
//			EasyRow easyRow = query.queryForRow(sql.getSQL(), sql.getParams());
//			String s = easyRow.getColumnValue("SHUTTLE_TYPE");
			String s = query.queryForString(sql.getSQL(), sql.getParams());
			logger.info("类型sql" + sql.getFullSq() + ":" + s);

			return StringUtils.isBlank(s) ? new ArrayList<>() : Arrays.asList(s.split(","));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return new ArrayList<>() ;
	}

	/**
	 * 工单编号改为：工单类型前缀+日期+小时+四位随机数
	 * @return
	 */
	/**
	 * 获取工单编号
	 * @param orderNoPre
	 * @return
	 */
	public static String getOrderNo(String orderNoPre) {
		//工单编号改为：工单类型前缀+日期+小时+四位随机数
		String key = orderNoPre;
		String suffix = Constants.getOrderNoSuffixCount();
		Assert.hasText(key, "请传入工单编号前缀！");

		synchronized (key.intern()) {
			try {
				RedisUtil.lock(key, key, 3);
				String fourRandom = getFourRandom();
				String orderNo = key+suffix+fourRandom;
				boolean isSuccess = false;
				for (int i = 0; i < 15; i++) {
					String isExist = CacheUtil.get(orderNo);
					if(StringUtils.isBlank(isExist)){
						isSuccess = true;
						break;
					}
					fourRandom = getFourRandom();
					orderNo = key+suffix+fourRandom;
					if(i>=10){
						logger.info("线程ID["+Thread.currentThread().getId()+"]"+"工单编号前缀["+key+"]"+"获取工单编号后缀次数"+(i+1));
					}
				}
				if(!isSuccess){
					//生成工单编号失败
					orderNo = key+suffix+getFourRandom();
				}
				//生成的工单标号丢到缓存保存1小时
				CacheUtil.put(orderNo, "Y", 3600);
				return orderNo;
			} catch(Exception e) {
				logger.error(e.getMessage(), e);
			}finally {
				RedisUtil.unlock(key, key);
			}
			return null;
		}
	}

	/**
	 * 产生num位随机数
	 * @return num位随机数
	 */
	public static String getFourRandom(){
		String orderNoSuffixCount = "";
		int num = 5;
		if(StringUtils.isNotBlank(orderNoSuffixCount)){
			num = Integer.parseInt(orderNoSuffixCount);
		}

		double pow = Math.pow(10, num);
		int randomNum = (int)pow;

		Random random = new Random();
		String fourRandom = random.nextInt(randomNum) + "";
		int randLength = fourRandom.length();
		if(randLength<num){
			for(int i=1; i<=num-randLength; i++)
				fourRandom = "0" + fourRandom ;
		}
		return fourRandom;
	}

	public static JSONObject getOrderMainInfo(String orderId, String entId, String busiOrderId, String schema) {
		try {
			EasyQuery query = QueryFactory.getReadQuery();
			EasySQL sql = new EasySQL();
			sql.append("select t1.* from "+ schema +".C_BO_BASE_ORDER t1");
			sql.append("where 1=1");
			sql.append(orderId, "and t1.ID = ?", false);
			return query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return null;
	}

	public static JSONObject getOrderBaseInfo(String orderId, String entId, String busiOrderId, String schema) {
		try {
			EasyQuery query = QueryFactory.getReadQuery();
			EasySQL sql = new EasySQL();
			sql.append("select t1.* from "+ schema +".C_BOX_MUSIC_STANDARD_BASE t1");
			sql.append("where 1=1");
			sql.append(orderId, "and t1.M_ID=?", false);
			sql.append(entId, "and t1.ENT_ID=?", false);
			sql.append(busiOrderId, "and t1.BUSI_ORDER_ID=?", false);
			return query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return null;
	}

	public static JSONObject getOrderExInfo(String orderId, String entId, String busiOrderId, String schema) {
		try {
			EasyQuery query = QueryFactory.getWriteQuery();
			EasySQL sql = new EasySQL();
			sql.append("select t1.* from "+ schema +".C_BOX_MUSIC_STANDARD_EX t1");
			sql.append("where 1=1");
			sql.append(orderId, "and t1.M_ID=?", false);
			sql.append(entId, "and t1.ENT_ID=?", false);
			sql.append(busiOrderId, "and t1.BUSI_ORDER_ID=?", false);
			logger.info("sql:"+sql.getSQL());
			return query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return null;
	}

//	public static List<JSONObject> getOrderProductList(String orderNo, String entId, String busiOrderId, String schema) {
//
//		try {
//			EasyQuery query = QueryFactory.getReadQuery();
//			EasySQL sql = new EasySQL();
//			sql.append("select t1.* from "+ schema +".C_BOX_MUSIC_PRODUCT t1");
//			sql.append("where 1=1");
//			sql.append(orderNo, "and t1.ORDER_NO=?", false);
//			sql.append(entId, "and t1.ENT_ID=?", false);
//			sql.append(busiOrderId, "and t1.BUSI_ORDER_ID=?", false);
//			return query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
//		} catch (Exception e) {
//			logger.error(e.getMessage(), e);
//			return new ArrayList<>();
//		}
//	}

	public static String getOrderReplayMsg(String orderId, JSONObject productJson, String entId, String busiOrderId, String schema) {
		String replayMsg = "";
		try {
			String serviceId = "CC_RPA_GRAB_SERVICE";
			JSONObject data = new JSONObject();
			String command = "createReplayMsg";
			data.put("command", command);

			JSONObject mainInfo = OrderUtils.getOrderMainInfo(orderId, entId, busiOrderId, schema);
			data.put("C_BO_BASE_ORDER", mainInfo);
			JSONObject baseInfo = OrderUtils.getOrderBaseInfo(orderId, entId, busiOrderId, schema);
			String handleAcc = baseInfo.getString("HANDLE_ACC");
			if(StringUtils.isBlank(handleAcc)) {
				baseInfo.put("HANDLE_ACC", "RPA机器人");
			}

			data.put("C_BOX_MUSIC_STANDARD_BASE", baseInfo);
			JSONObject exInfo = OrderUtils.getOrderExInfo(orderId, entId, busiOrderId, schema);
			data.put("C_BOX_MUSIC_STANDARD_EX", exInfo);


			data.put("PRODUCT_INFO", productJson);
			IService service = ServiceContext.getService(serviceId);
			JSONObject result = service.invoke(data);
			logger.info("获取工单回复结果 result:" + JSONObject.toJSONString(result));
			if(result != null && "000".equals(result.getString("respCode"))) {
				replayMsg += result.getString("replayMsg");
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return replayMsg;
	}

	/**
	 * 添加跟进记录
	 * @param id	跟进记录id，可为空，不为空时会查询附件信息
	 * @param content	跟进内容
	 * @param type		跟进类型
	 * @param orderId	工单id
	 * @param user
	 * @throws Exception
	 */
	public static String addOrderFollow(String id, String taskName, String content, String type, String orderId, String orderNo, UserModel user, JSONArray fileList) throws Exception {
		String followId = addOrderFollow(id, taskName, content, type, orderId, orderNo, user);
		if(CommonUtil.listIsNotNull(fileList)) {
			for (int i = 0; i < fileList.size(); i++) {
				JSONObject row = fileList.getJSONObject(i);
				String filePath = row.getString("filePath");
				String fileName = row.getString("fileName");
				try {
					OrderUtils.upload(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), followId, filePath, fileName, "zdybd");
				} catch (Exception e) {
					logger.error("error:" + e.getMessage(), e);
				}
			}
		}
		return followId;
	}

	public static String addOrderFollow(String id, String taskName, String content, String type, String orderId, String orderNo, UserModel user, JSONArray fileList, String sourceNo) throws Exception {
		String followId = addOrderFollow(id, taskName, content, type, orderId, orderNo, user, sourceNo);
		if(CommonUtil.listIsNotNull(fileList)) {
			for (int i = 0; i < fileList.size(); i++) {
				JSONObject row = fileList.getJSONObject(i);
				String filePath = row.getString("filePath");
				String fileName = row.getString("fileName");
				try {
					OrderUtils.upload(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), followId, filePath, fileName, "zdybd");
				} catch (Exception e) {
					logger.error("error:" + e.getMessage(), e);
				}
			}
		}
		return followId;
	}

	/**
	 * 添加跟进记录
	 * @param id	跟进记录id，可为空，不为空时会查询附件信息
	 * @param content	跟进内容
	 * @param type		跟进类型
	 * @param orderId	工单id
	 * @param user
	 * @throws Exception
	 */
	public static String addOrderFollow(String id, String taskName, String content, String type, String orderId, String orderNo, UserModel user) throws Exception {
		EasyQuery writeQuery = QueryFactory.getWriteQuery();
		Assert.hasText(content, "跟进内容不能为空");
		if(StringUtils.isBlank(orderId)) {
			orderId = QueryFactory.getWriteQuery().queryForString("select ID from " + user.getSchemaName() + ".C_BO_BASE_ORDER where ORDER_NO = ? and PROC_INST_ID is null", orderNo);
		}

		Assert.hasText(orderId, "工单id不能为空");
		String HAS_FILE = "N";
		String followId = IDGenerator.getDefaultNUMID();

		boolean saveExist = false;
		if (StringUtils.isNotBlank(id)) {
			//根据id去查有没有附件   没有附件则不显示图标 有附件才显示图标
			EasySQL sql = new EasySQL();
			sql.append("select BUSI_ID FROM " + user.getSchemaName() +".c_cf_attachment");
			sql.append(" where 1=1");
			sql.append(id, " and BUSI_ID=?");
			sql.append("N", " and IS_DEL=?");
			sql.append("zdybd", " and BUSI_TYPE=?");
			String busiId = writeQuery.queryForString(sql.getSQL(), sql.getParams());
			logger.info("sql" + sql.getSQL() + "params:" + JSON.toJSONString(sql.getParams()));
			logger.info("busiId:" + busiId);
			if (StringUtils.isNotBlank(busiId)) {//代表有附件
				HAS_FILE = "Y";
				followId = id;
			}
			//判断是新增/修改
			saveExist = writeQuery.queryForExist("SELECT COUNT(1) FROM "+user.getSchemaName() +".C_BO_ORDER_FOLLOW WHERE ID = ?" , new Object[]{id});
		}

		EasyRecord record = new EasyRecord(user.getSchemaName() +".C_BO_ORDER_FOLLOW", "ID");
		record.put("ID", followId);
		record.put("HAS_FILE", HAS_FILE);
		record.put("CONTENT", content);
		record.put("TASK_NAME", taskName);
		record.put("TYPE", type);
		record.put("ORDER_ID", orderId);
		record.put("CREATE_TIME", DateUtil.getCurrentDateStr());
		record.put("ARRIVAL_TIME", DateUtil.getCurrentDateStr());
		record.put("CREATE_NAME", user.getUserName());
		record.put("CREATE_ACC", user.getUserAcc());
		record.put("CREATE_DEPT_CODE", user.getDeptCode());
		record.put("EP_CODE", user.getEpCode());
		record.put("BUSI_ORDER_ID", user.getBusiOrderId());
		record.put("CREATE_TIMESTAMP", System.currentTimeMillis());
		if (saveExist) {
			writeQuery.update(record);
		}else{
			writeQuery.save(record);
		}
		return followId;
	}

	public static String addOrderFollow(String id, String taskName, String content, String type, String orderId, String orderNo, UserModel user, String  sourceNo) throws Exception {
		EasyQuery writeQuery = QueryFactory.getWriteQuery();
		Assert.hasText(content, "跟进内容不能为空");
		if(StringUtils.isBlank(orderId)) {
			orderId = QueryFactory.getWriteQuery().queryForString("select ID from " + user.getSchemaName() + ".C_BO_BASE_ORDER where ORDER_NO = ? and PROC_INST_ID is null", orderNo);
		}

		Assert.hasText(orderId, "工单id不能为空");
		String HAS_FILE = "N";
		String followId = IDGenerator.getDefaultNUMID();

		boolean saveExist = false;
		if (StringUtils.isNotBlank(id)) {
			//根据id去查有没有附件   没有附件则不显示图标 有附件才显示图标
			EasySQL sql = new EasySQL();
			sql.append("select BUSI_ID FROM " + user.getSchemaName() +".c_cf_attachment");
			sql.append(" where 1=1");
			sql.append(id, " and BUSI_ID=?");
			sql.append("N", " and IS_DEL=?");
			sql.append("zdybd", " and BUSI_TYPE=?");
			String busiId = writeQuery.queryForString(sql.getSQL(), sql.getParams());
			logger.info("sql" + sql.getSQL() + "params:" + JSON.toJSONString(sql.getParams()));
			logger.info("busiId:" + busiId);
			if (StringUtils.isNotBlank(busiId)) {//代表有附件
				HAS_FILE = "Y";
				followId = id;
			}
			//判断是新增/修改
			saveExist = writeQuery.queryForExist("SELECT COUNT(1) FROM "+user.getSchemaName() +".C_BO_ORDER_FOLLOW WHERE ID = ?" , new Object[]{id});
		}

		EasyRecord record = new EasyRecord(user.getSchemaName() +".C_BO_ORDER_FOLLOW", "ID");
		record.put("ID", followId);
		record.put("HAS_FILE", HAS_FILE);
		record.put("CONTENT", content);
		record.put("TASK_NAME", taskName);
		record.put("TYPE", type);
		record.put("ORDER_ID", orderId);
		record.put("CREATE_TIME", DateUtil.getCurrentDateStr());
		record.put("ARRIVAL_TIME", DateUtil.getCurrentDateStr());
		record.put("CREATE_NAME", user.getUserName());
		record.put("CREATE_ACC", user.getUserAcc());
		record.put("CREATE_DEPT_CODE", user.getDeptCode());
		record.put("EP_CODE", user.getEpCode());
		record.put("BUSI_ORDER_ID", user.getBusiOrderId());
		record.put("CREATE_TIMESTAMP", System.currentTimeMillis());
		record.put("SOURCE_NO", sourceNo);
		if (saveExist) {
			writeQuery.update(record);
		}else{
			writeQuery.save(record);
		}
		return followId;
	}


	public static String addOrderFollow(String id, String taskName, String content, String type, String orderId, String orderNo,  String schema, String createTime) throws Exception {
		EasyQuery writeQuery = QueryFactory.getWriteQuery();
		Assert.hasText(content, "跟进内容不能为空");
		if(StringUtils.isBlank(orderId)) {
			orderId = QueryFactory.getWriteQuery().queryForString("select ID from " + schema + ".C_BO_BASE_ORDER where ORDER_NO = ? and PROC_INST_ID is null", orderNo);
		}

		Assert.hasText(orderId, "工单id不能为空");
		String HAS_FILE = "N";
		String followId = IDGenerator.getDefaultNUMID();

		boolean saveExist = false;
		if (StringUtils.isNotBlank(id)) {
			//根据id去查有没有附件   没有附件则不显示图标 有附件才显示图标
			EasySQL sql = new EasySQL();
			sql.append("select BUSI_ID FROM " + schema +".c_cf_attachment");
			sql.append(" where 1=1");
			sql.append(id, " and BUSI_ID=?");
			sql.append("N", " and IS_DEL=?");
			sql.append("zdybd", " and BUSI_TYPE=?");
			String busiId = writeQuery.queryForString(sql.getSQL(), sql.getParams());
			logger.info("sql" + sql.getSQL() + "params:" + JSON.toJSONString(sql.getParams()));
			logger.info("busiId:" + busiId);
			if (StringUtils.isNotBlank(busiId)) {//代表有附件
				HAS_FILE = "Y";
				followId = id;
			}
			//判断是新增/修改
			saveExist = writeQuery.queryForExist("SELECT COUNT(1) FROM "+schema +".C_BO_ORDER_FOLLOW WHERE ID = ?" , new Object[]{id});
		}

		EasyRecord record = new EasyRecord(schema +".C_BO_ORDER_FOLLOW", "ID");
		record.put("ID", followId);
		record.put("HAS_FILE", HAS_FILE);
		record.put("CONTENT", content);
		record.put("TASK_NAME", taskName);
		record.put("TYPE", type);
		record.put("ORDER_ID", orderId);
		record.put("CREATE_TIME", DateUtil.getCurrentDateStr());
		record.put("ARRIVAL_TIME", createTime);
		record.put("CREATE_NAME", "rpa机器人");
		record.put("CREATE_ACC", "system");
		record.put("CREATE_DEPT_CODE", "");
		record.put("EP_CODE", Constants.getEntId());
		record.put("BUSI_ORDER_ID", Constants.getBusiOrderId());
		record.put("CREATE_TIMESTAMP", System.currentTimeMillis());
		if (saveExist) {
			writeQuery.update(record);
		}else{
			writeQuery.save(record);
		}
		return followId;
	}

	public static String getProductId(String schema) throws SQLException {
		String recordId = DateUtil.getCurrentDateStr("yyyyMMddHH") + new Random().nextInt(10000);
		if(QueryFactory.getWriteQuery().queryForExist("select ID from " + schema + ".C_BOX_MUSIC_PRODUCT where ID = ?", recordId)) {
			return getProductId(schema);
		}
		return recordId;
	}

	public static void refundOrderCallback(String orderNo, String fee, String dealResult, UserModel user) throws Exception {
		String entId = user.getEpCode();
		String schema = user.getSchemaName();
		String busiOrderId = user.getBusiOrderId();

		refundLogger.info("[" + orderNo + "]退费回调[" + dealResult + "]");
		EasyQuery query = QueryFactory.getQuery(entId);


		EasySQL sql = new EasySQL("select t2.ID ORDER_ID,t1.AUTO_REPLAY_MSG,t2.ORDER_NO,t2.BUSI_ORDER_ID,t3.REFUND_ACC,t1.AGENT_ENTER_RESULT,t4.CREATE_ACC,t2.CALLER");
		sql.append("from " + schema + ".C_BOX_MUSIC_STANDARD_EX t1");
		sql.append("left join " + schema + ".C_BO_BASE_ORDER t2 on t1.M_ID = t2.ID");
		sql.append("left join " + schema + ".C_BOX_MUSIC_STANDARD_BASE t3 on t3.M_ID = t2.ID");
		sql.append("left join " + schema + ".C_BO_BASE_ORDER t4 on t1.M_ID = t4.ID");
		sql.append(orderNo, "where t2.ORDER_NO = ?", false);
		sql.append(orderNo, "or t2.ID = ?", false);
		JSONObject data = query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		if(data == null) {
			refundLogger.error(CommonUtil.getClassNameAndMethod(OrderUtils.class) + " 工单记录不存在 orderNo:" + orderNo);
			return;
		}

		// 更新客服产品处理状态
		orderNo = data.getString("ORDER_NO");
		String orderId = data.getString("ORDER_ID");
		String createAcc = data.getString("CREATE_ACC");
		String agentEnterResult = data.getString("AGENT_ENTER_RESULT");

		String custPhone = data.getString("CALLER");
		// 退赔账户
		String refundAcc = data.getString("REFUND_ACC");

		// 生成回复内容
		String autoReplayMsg = data.getString("AUTO_REPLAY_MSG");

		if(Constants.DEAL_RESULT_01.equals(dealResult)) {
			if(!"01".equals(agentEnterResult)) {
				List<JSONObject> productList = query.queryForList("select * from " + schema + ".C_BOX_MUSIC_PRODUCT where ORDER_NO = ? and ACTION = ? and (CLAIMANT = ? OR CLAIMANT = '')", new Object[] {orderNo, Constants.RPA_ACTION_REFUND, Constants.CLAIMANT_02}, new JSONMapperImpl());
				for (JSONObject productInfo : productList) {
					String productId = productInfo.getString("PRODUCT_ID");
					String refundTime = productInfo.getString("REFUND_TIME");

					// 退订时间为空时，从退订记录中获取退订时间
					if(StringUtils.isBlank(refundTime)) {
						String productCode = productInfo.getString("PRODUCT_CODE");
						String orderTime = productInfo.getString("ORDER_TIME");
						List<JSONObject> ununOrderList = ApiUtils.getTDProductList(custPhone, orderTime, DateUtil.getCurrentDateStr(), Constants.OP_ACC_RPA);
						for (JSONObject unOrderInfo : ununOrderList) {
							if(StringUtils.equalsAny(unOrderInfo.getString("productId"), productId, productCode) || StringUtils.equalsAny(unOrderInfo.getString("feeProductId"), productId, productCode)) {
								refundTime = unOrderInfo.getString("ununTime");
								productInfo.put("REFUND_TIME", refundTime);
								break;
							}
						}
					}
					String handleMsg = OrderUtils.getOrderReplayMsg(orderId, productInfo, entId, busiOrderId, schema);
					if(StringUtils.isBlank(handleMsg)) {
						refundLogger.error("[" + orderNo + "][" + productInfo.getString("PRODUCT_NAME") + "]生成处理内容失败");
					}
					QueryFactory.getWriteQuery().execute("update " + schema + ".C_BOX_MUSIC_PRODUCT set DEAL_RESULT=?,REPLY_MSG=?,REFUND_TIME=? where ID = ?", Constants.DEAL_RESULT_01, handleMsg,refundTime, productInfo.getString("ID"));
				}
				autoReplayMsg = OrderUtils.getReplayMsg(orderNo, schema);
				if(StringUtils.isNotBlank(autoReplayMsg)) {
					OrderUtils.addOrderFollow(orderId, "工单处理", autoReplayMsg, Constants.FOLLOW_TYPE_66, orderId, orderNo, user);
				}
			} else {
				QueryFactory.getWriteQuery().execute("update " + schema + ".C_BOX_MUSIC_PRODUCT set DEAL_RESULT=? where ORDER_NO = ? and ACTION = ? and CLAIMANT = ?", Constants.DEAL_RESULT_01, orderNo, Constants.RPA_ACTION_REFUND, Constants.CLAIMANT_02);
			}
			// 记录RPA操作日志
			OrderUtils.addOrderFollow(orderId, "工单处理", DateUtil.getCurrentDateStr() + "[" + refundAcc + "][充值退费] 充值金额[" + fee + "]成功", Constants.FOLLOW_TYPE_1, orderId, null, user);
		} else {
			QueryFactory.getWriteQuery().execute("update " + schema + ".C_BOX_MUSIC_PRODUCT set DEAL_RESULT=? where ORDER_NO = ? and ACTION = ? and CLAIMANT = ?", Constants.DEAL_RESULT_02, orderNo, Constants.RPA_ACTION_REFUND, Constants.CLAIMANT_02);
			OrderUtils.addOrderFollow(orderId, "工单处理", DateUtil.getCurrentDateStr() + "[" + refundAcc + "][充值退费] 充值金额[" + fee + "]退费充值回调失败", Constants.FOLLOW_TYPE_1, orderId, null, user);
		}

		// 查询退费产品信息
		sql = new EasySQL("select sum(case when DEAL_RESULT in ('01', '02', '04') or DEAL_RESULT is null then 1 else 0 end) ALL_COUNT,");
		sql.append("sum(case when DEAL_RESULT = '01' then 1 else 0 end) SUCC_COUNT,");
		sql.append("sum(case when DEAL_RESULT = '02' then 1 else 0 end) FAIL_COUNT");
		sql.append("from " + schema + ".C_BOX_MUSIC_PRODUCT");
		sql.append("where 1=1");
		sql.append(orderNo, "and ORDER_NO = ?");
		sql.append(Constants.RPA_ACTION_REFUND, "and ACTION = ?");
		JSONObject productData = query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

		int succCount = productData.getIntValue("SUCC_COUNT");
		int failCount = productData.getIntValue("FAIL_COUNT");
		int allCount = productData.getIntValue("ALL_COUNT");

		int waitCount = allCount - succCount - failCount;

		// 更新工单信息
		EasyRecord baseRecord = new EasyRecord(schema + ".C_BOX_MUSIC_STANDARD_BASE", "M_ID");
		baseRecord.put("M_ID", orderId);
		// 待处理数
		baseRecord.put("EX_6", waitCount);
		baseRecord.put("REFUND_CALLBACK_TIME", DateUtil.getCurrentDateStr());

		EasyRecord record = new EasyRecord(schema + ".C_BOX_MUSIC_STANDARD_EX", "M_ID");
		record.put("M_ID", orderId);
		// 回复内容
		record.put("AUTO_REPLAY_MSG", autoReplayMsg);
		if(succCount == allCount && succCount > 0 && StringUtils.equals(dealResult, "01")) {
			// 全部成功
			record.put("REFUND_RESULT", "01");
			baseRecord.put("ORDER_STATUS", "02");
		} else {
			// 退费失败
			record.put("REFUND_RESULT", "02");
		}

		query.update(baseRecord);
		query.update(record);

		// 没有待办工单数 退费失败时，直接转人工
		if(waitCount == 0 || !StringUtils.equals(dealResult, "01")) {
			IService service = ServiceContext.getService("CC_EORDER_COMPELETE_RECEIVETASK");
			JSONObject reqParam = new JSONObject();
			reqParam.put("orderId", orderId);
			reqParam.put("orderNo", data.getString("ORDER_NO"));
			reqParam.put("entId", entId);
			reqParam.put("busiOrderId", data.getString("BUSI_ORDER_ID"));
			reqParam.put("schema", schema);

			JSONObject flowVar  = new JSONObject();
			flowVar.put("REFUND_RESULT", record.getString("REFUND_RESULT"));
			flowVar.put("AGENT_ENTER_RESULT", agentEnterResult);
			flowVar.put("CREATE_ACC", createAcc);
			reqParam.put("flowVar", flowVar);

			refundLogger.info("进行工单流转 params:" + reqParam.toJSONString());
			JSONObject result = service.invoke(reqParam);
			refundLogger.info("进行工单流转 result:" + JSONObject.toJSONString(result));
			if(result == null || result.getIntValue("state") != 1) {
				MinistryNoticeUtil.sendErrorNotice("orderNo[" + orderNo + "]退费等待结果接收异常，请查看日志");
			}
		}

	}

	/**
	 * 转存邮件附件
	 * @param schema  数据库
	 * @param entId
	 * @param busiOrderId
	 * @param serialId     记录的唯一id
	 * @param recordFile   文件路径，必须存在，且当前服务器能直接访问，格式如 /home/<USER>/1.txt
	 * @param busiType     业务类型，用于区分文件目录，如 voice、media、email
	 * @return
	 * @throws Exception
	 */
	public static JSONObject upload(String schema,String entId,String busiOrderId,String busiId, String fileUrl, String fileName,String busiType) throws Exception {
		JSONObject resultJson = new JSONObject();
		resultJson.put("state", 500);

		logger.info("转存附件到外部存储: busiType = " + busiType+", fileUrl = "+fileUrl+", fileName = "+fileName+ ", busiId = "+busiId+", schema = "+schema+", entId = "+entId+", busiOrderId = "+busiOrderId);

		File file = null;
		if(fileUrl.startsWith("/")) {
			file = new File(fileUrl);
			if(!file.exists()){
				logger.error("文件上传失败,文件不存在:" + fileUrl);
				throw new Exception("文件不存在!");
			}
		} else if(fileUrl.startsWith("ftp://")) {
			String tempFileUrl = getFileUrlByFtp(fileUrl, fileName);
			file = new File(tempFileUrl);
		} else {
			String tempFileUrl = getFileUrlByHttp(fileUrl, fileName);
			file = new File(tempFileUrl);
		}

		FileInputStream inputStream = null;
		try {
			//文件流
			inputStream = new FileInputStream(file);

			String bucket = OSSConstants.getOssMarsServerBucket();
			//生成附件对象
			AttachmentFile afile = new AttachmentFile(bucket, inputStream, busiType, file.getName());
			afile.setOssType(OSSConstants.getOssSwitch());
			afile.setDelUploadOldFile(false); //默认不删除原文件
			afile.setSchemaName(schema);
			afile.setEntId(entId);
			afile.setBusiOrderId(busiOrderId);
			afile.setBusiId(busiId);
			afile.setFileSize(file.length());
			//组装用户对象
			UserModel user = new UserModel();
			user.setUserAcc("system");
			user.setUserName("system-"+Constants.APP_NAME);
			user.setUserNo("system");
			user.setEpCode(entId);
			user.setBusiOrderId(busiOrderId);
			user.setSchemaName(schema);

			//调用cc-base方法上传，包含上传、入库等
			JSONObject result = AttachmentUtil.upload(afile, user);
			if(result==null){
				logger.error("文件上传失败：无返回，busiId = "+busiId);
				return resultJson;
			}

			int state = result.getIntValue("state");
			if(state!=1){
				logger.error("文件上传失败："+result + ",busiId = "+busiId);
				return resultJson;
			}

			JSONObject data = result.getJSONObject("data");
			if(data==null){
				logger.error("文件上传失败：返回结果里无data参数,busiId = "+busiId);
				return resultJson;
			}

			String idUrl = data.getString("idUrl");

			logger.info(" 文件上传路径："+afile.getFullPath()+",原文件名:"+fileName+",idUrl = "+idUrl);
			resultJson.put("state", 1);
			resultJson.put("idUrl", idUrl);

			if(file.exists()) {
				file.delete();
			}
			return resultJson;

		} catch (Exception e) {
			logger.error("上传文件失败:"+e.getMessage(),e);
			throw e;
		} finally {
			if(inputStream!=null){
				inputStream.close();
			}
		}
	}

	/**
	 * 下载ftp到本地并返回本地文件地址
	 * @param fileUrl
	 * @param fileName
	 * @return
	 * @throws Exception
	 */
	private static String getFileUrlByFtp(String fileUrl, String fileName) throws Exception {
		fileUrl = fileUrl.substring(6);

		String dirPath = "/bakups/temp/";
		File dirFile = new File(dirPath);
		if(!dirFile.exists()) {
			dirFile.mkdirs();
		}

		if(!FTPUtils.downloadFile(fileUrl, dirPath + fileName)) {
			logger.error("ftp附件下载失败:" + fileUrl);
			throw new Exception("ftp附件下载失败!");
		}

		File file = new File(dirPath + fileName);
		if(!file.exists()){
			logger.error("文件上传失败,文件不存在:" + dirPath + fileName);
			throw new Exception("文件不存在!");
		}
		return dirPath + fileName;
	}

	/**
	 * 下载http到本地并返回本地文件地址
	 * @param fileUrl
	 * @param fileName
	 * @return
	 * @throws Exception
	 */
	private static String getFileUrlByHttp(String fileUrl, String fileName) throws Exception {
		String dirPath = "/bakups/temp/";
		File dirFile = new File(dirPath);
		if(!dirFile.exists()) {
			dirFile.mkdirs();
		}
		HttpResp resp = HttpUtil.sendGetStream(tranformStyle(fileUrl), "", "utf-8", dirPath, fileName);
		if(resp.getCode() != 200) {
			logger.error("邮件附件下载失败:" + fileUrl);
			throw new Exception("邮件附件下载失败!");
		}

		File file = new File(dirPath + fileName);
		if(!file.exists()){
			logger.error("文件上传失败,文件不存在:" + dirPath + fileName);
			throw new Exception("文件不存在!");
		}
		return dirPath + fileName;
	}

	/**
	 * 对中文字符进行UTF-8编码
	 * @param source 要转义的字符串
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	public static String tranformStyle(String source) throws UnsupportedEncodingException {
		char[] arr = source.toCharArray();
		StringBuilder sb = new StringBuilder();
		for(int i = 0; i < arr.length; i++)
		{
			char temp = arr[i];
			if(isChinese(temp))
			{
				sb.append(URLEncoder.encode("" + temp, "UTF-8"));
				continue;
			}
			sb.append(arr[i]);
		}
		return sb.toString();
	}

	/**
	 * 判断是不是中文字符
	 * @param c
	 * @return
	 */
	public static boolean isChinese(char c) {

		Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);

		if (ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS

				|| ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS

				|| ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A

				|| ub == Character.UnicodeBlock.GENERAL_PUNCTUATION

				|| ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION

				|| ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS) {

			return true;

		}

		return false;
	}


	public static void exeHandle(String orderId, String schema) {
		try {
			setNodeName(orderId, schema);
			setOrderStatus(orderId, schema);
			updateType(orderId, schema);
			setHandleContent(orderId, schema);
		} catch (Exception e) {
			logger.error("归档回调报错：" + e.getMessage());
		}
	}
	private static void setOrderStatus(String orderId, String schema) throws SQLException {
		EasyQuery query = QueryFactory.getWriteQuery();
		EasySQL sql = new EasySQL();
		sql.append("update " + schema + ".c_box_music_standard_base set ORDER_STATUS = '02' where m_id =  ? ");
		query.execute(sql.getSQL(), orderId);
	}

	public static void setHandleContent(String orderId, String schema) throws SQLException {
		EasyQuery query = QueryFactory.getWriteQuery();
		//
		String followType66 = Constants.FOLLOW_TYPE_33;
		EasySQL sql = new EasySQL();
		sql.append("update "+schema+".c_bo_base_order set content =  ");
		sql.append("(select "+ DBFuncUtils.groupConcat(query.getTypes(), "CONTENT")+" from " + schema+ ".c_bo_order_follow where type = ? and order_id = ? )");
		sql.append(" where id = ?");
		query.execute(sql.getSQL(), followType66, orderId, orderId);
	}

	private static void setNodeName(String orderId, String schema) throws SQLException {
		EasyQuery query = QueryFactory.getWriteQuery();
		EasySQL sql = new EasySQL();
		sql.append("update "+schema + ".c_bo_order_ex set CURR_HANDLER = '', CURR_NODE= '工单归档' where order_id = ? ");
		query.execute(sql.getSQL(), orderId);
	}

	private static void updateType(String orderId, String schema) throws SQLException {
		EasyQuery query = QueryFactory.getQuery(Constants.getEntId());
		query.execute("update   " + schema + ".c_box_music_standard_base" +
				" set  FIRST_TYPE_NAME = ( select  name from " + schema + ".c_cf_common_tree"+ " where CODE= "+schema+".c_box_music_standard_base.FIRST_TYPE_CODE )" +
				"  where M_ID = ? && FIRST_TYPE_CODE !=''  && FIRST_TYPE_NAME ='' ", orderId);
		query.execute("update  "+schema+".c_box_music_standard_base " +
				"set  SECOND_TYPE_NAME= ( select  name from "+schema+".c_cf_common_tree   where CODE= "+schema+".c_box_music_standard_base.SECOND_TYPE_CODE)" +
				"  where M_ID = ? && SECOND_TYPE_CODE!=''  && SECOND_TYPE_NAME='' ", orderId);
		query.execute("update  "+schema+".c_box_music_standard_base " +
				"set  THIRD_TYPE_NAME = ( select  name from "+schema+".c_cf_common_tree   where CODE= "+schema+".c_box_music_standard_base.THIRD_TYPE_CODE) " +
				"  where M_ID = ? && THIRD_TYPE_CODE!=''  && THIRD_TYPE_NAME='' ", orderId);

	}

	public static String crmFileUploads(String exJson) {
		StringBuilder filepath = new StringBuilder();

		try {
			if (exJson == null) {
				return "";
			}
			String[] fileJson = exJson.split(",");
			for (String s : fileJson) {
				JSONObject jsonObject = JSONObject.parseObject(s);
				String fileUrl = jsonObject.getString("url");

				String remoteBaseDir = ""; // 目录
				String fileName = generateFileName(fileUrl); // 定制文件名
				String remoteFilePath = remoteBaseDir + "/" + fileName;
				//  根据s3http地址获取文件流
				File file = null;
				if(fileUrl.startsWith("/")) {
					file = new File(fileUrl);
					if(!file.exists()){
						logger.error("文件上传失败,文件不存在:" + fileUrl);
					}
				} else {
					String dirPath = "/bakups/temp/";
					File dirFile = new File(dirPath);
					if(!dirFile.exists()) {
						dirFile.mkdirs();
					}
					HttpResp resp = HttpUtil.sendGetStream(tranformStyle(fileUrl), "", "utf-8", dirPath, fileName);
					if(resp.getCode() != 200) {
						logger.error("CRM附件S3下载失败:" + fileUrl);
					}

					file = new File(dirPath + fileName);
					if(!file.exists()){
						logger.error("文件上传失败,文件不存在:" + dirPath + fileName);
					}
				}

//				FileInputStream inputStream = null;
//				//文件流
//				 inputStream = new FileInputStream(file);

				try (FileInputStream inputStream = new FileInputStream(file)) {
					boolean uploadSuccess = FTPUtils.uploadFile(inputStream, remoteFilePath);
					if (uploadSuccess) {
						filepath.append(fileName).append(",");
					} else {
						throw new Exception("CRM附件上传失败失败!");
					}
				} catch (Exception e) {
					logger.error(e.getMessage(), e);
				} finally {
					boolean delete = file.delete();
					if (!delete) {
						logger.error("文件删除失败:" + file.getAbsolutePath());
					}
				}


			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		if (filepath.length() > 0) {
			filepath.deleteCharAt(filepath.length() - 1);
		}
		return filepath.toString();
	}

	public static String generateFileName(String httpFilePath) {
		String fileNameKey = "cx-mix-crm-filename";
		String fileNameIndex = "000";
		Object o = CacheUtil.get(fileNameKey);
		if (o == null) {
			fileNameIndex = "001";
		} else {
			fileNameIndex = String.format("%03d", Integer.parseInt(o.toString()) + 1);
			CacheUtil.put(fileNameKey, fileNameIndex);
		}
		// 1. 发起方系统平台编码（10位）
		String fromSysCode = Constants.getCrmSrcSysId();
		// 2. 目标落地方系统平台编码（10位）
		String toSysCode = Constants.getCrmDstSysId();
		// 3. 业务功能编码（8位）
		String bizCode = "BUS43001";
		// 4. 当前日期（8位）
		String date = new java.text.SimpleDateFormat("yyyyMMdd").format(new java.util.Date());
		// 5. 数据类型（1位）
		String dataType = "A";
		// 6. 顺序号（3位），随机生成
		String seqNo = fileNameIndex;
		// 7. 扩展名
		String ext = "";
		int lastDot = httpFilePath.lastIndexOf('.');
		if (lastDot != -1) {
			ext = httpFilePath.substring(lastDot);
		}
		// 拼接
		return fromSysCode + toSysCode + bizCode + date + dataType + seqNo + ext;
	}



}
