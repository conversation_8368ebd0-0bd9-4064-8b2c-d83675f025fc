package com.yunqu.cc.mixgw.util;

import com.yunqu.cc.mixgw.base.CommonLogger;
import com.jcraft.jsch.*;
import com.yunqu.cc.mixgw.base.Constants;
import org.apache.log4j.Logger;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.Vector;

/**
 * SFTP文件上传下载工具类 (重构版)
 *
 * <AUTHOR>
 */
public final class FTPUtils {

    private static final Logger log = CommonLogger.getLogger();

    // SFTP连接参数（上传）
//    private static String uploadHost = Constants.getCrmUplaodFtpIpPort().split(":")[0];
//    private static int uploadPort = Integer.parseInt(Constants.getCrmUplaodFtpIpPort().split(":")[1]);
//    private static String uploadUsername = Constants.getCrmUplaodFtpUsername();
//    private static String uploadPassword = Constants.getCrmUplaodFtpPwd();
//    private static volatile boolean uploadInitialized = true;
//
//    // SFTP连接参数（下载）
//    private static String downloadHost = Constants.getCrmUplaodFtpIpPort().split(":")[0];
//    private static int downloadPort = Integer.parseInt(Constants.getCrmUplaodFtpIpPort().split(":")[1]);
//    private static String downloadUsername = Constants.getCrmUplaodFtpUsername();
//    private static String downloadPassword = Constants.getCrmUplaodFtpPwd();
//    private static volatile boolean downloadInitialized = true;

    /**
     * 初始化SFTP上传连接参数
     */
    public static synchronized void initUpload() {
//        uploadHost = "*************";
//        uploadPort = 12600; // SFTP默认端口是22
//        uploadUsername = "ayy_ftp_test";
//        uploadPassword = "fdc2681bf92c3871a1f5be0c2b6cc12f";
//        uploadInitialized = true;
//        log.info("SFTP upload connection parameters initialized.");
    }

    /**
     * 初始化SFTP下载连接参数
     */
    public static synchronized void initDownload() {
//        downloadHost = "*************";
//        downloadPort = 12600; // SFTP默认端口是22
//        downloadUsername = "ayy_ftp_upload";
//        downloadPassword = "8d27caa7daba3521b5e3b157d7560018";
//        downloadInitialized = true;
//        log.info("SFTP download connection parameters initialized.");
    }

    /**
     * 检查上传参数是否已经初始化
     *
     * @throws IllegalStateException 如果未初始化
     */
    private static void checkUploadInitialized() {
//        if (!uploadInitialized) {
//            throw new IllegalStateException("FTPUtils upload parameters have not been initialized. Please call initUpload() first.");
//        }
    }

    /**
     * 检查下载参数是否已经初始化
     *
     * @throws IllegalStateException 如果未初始化
     */
    private static void checkDownloadInitialized() {
//        if (!downloadInitialized) {
//            throw new IllegalStateException("FTPUtils download parameters have not been initialized. Please call initDownload() first.");
//        }
    }

    private FTPUtils() {
        // 工具类，防止实例化
    }

    /**
     * 连接并登录到SFTP服务器（上传专用）
     *
     * @return 一个配置好的ChannelSftp实例
     * @throws JSchException 如果连接失败
     * @throws SftpException 如果登录失败
     */
    private static ChannelSftp connectForUpload() throws JSchException, SftpException {
        checkUploadInitialized();
        JSch jsch = new JSch();
        Session session = jsch.getSession(Constants.getCrmUplaodFtpUsername(), Constants.getCrmUplaodFtpIpPort().split(":")[0], Integer.parseInt(Constants.getCrmUplaodFtpIpPort().split(":")[1]));
        session.setPassword(Constants.getCrmUplaodFtpPwd());
        session.setTimeout(50000);
        session.setConfig("userauth.gssapi-with-mic", "no");

        session.setConfig("StrictHostKeyChecking", "no");
        session.setConfig("PreferredAuthentications", "password,keyboard-interactive,publickey");
        session.setConfig("Cipher", "aes128-ctr,aes192-ctr,aes256-ctr,3des-ctr,arcfour,arcfour128,arcfour256");
        session.setConfig("KexAlgorithms", "diffie-hellman-group1-sha1,diffie-hellman-group14-sha1,diffie-hellman-group-exchange-sha1,diffie-hellman-group-exchange-sha256");
        session.setConfig("ServerAliveInterval", "120000"); // 例如，设置服务器活跃检查间隔
        session.setConfig("server_host_key", "ssh-dss,ssh-rsa,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521");
        session.setConfig("compression.c2s", "zlib,<EMAIL>,none");

        try {
            session.connect();
            ChannelSftp channelSftp = (ChannelSftp) session.openChannel("sftp");
            channelSftp.connect();
            log.info("SFTP upload connection successful ");
            return channelSftp;
        } catch (JSchException e) {
            String errorMsg = e.getMessage();
            if (errorMsg != null && (errorMsg.contains("Auth fail") || errorMsg.contains("Authentication failed"))) {
                log.error("SFTP authentication failed for user '" + Constants.getCrmUplaodFtpUsername() + "' on host '" + Constants.getCrmUplaodFtpIpPort() + "'. Please check username and password.");
            }
            log.error("Failed to connect to SFTP server (upload): " + Constants.getCrmUplaodFtpIpPort(), e);
            throw e;
        }
    }

    /**
     * 连接并登录到SFTP服务器（下载专用）
     *
     * @return 一个配置好的ChannelSftp实例
     * @throws JSchException 如果连接失败
     * @throws SftpException 如果登录失败
     */
    private static ChannelSftp connectForDownload() throws JSchException, SftpException {
        checkDownloadInitialized();
        JSch jsch = new JSch();
        Session session = jsch.getSession(Constants.getCrmDownlaodFtpUsername(), Constants.getCrmDownlaodFtpIpPort().split(":")[0], Integer.parseInt(Constants.getCrmDownlaodFtpIpPort().split(":")[1]));
        session.setPassword(Constants.getCrmDownlaodFtpPwd());

        session.setConfig("userauth.gssapi-with-mic", "no");
        session.setConfig("StrictHostKeyChecking", "no");
        session.setTimeout(50000);
        session.setConfig("PreferredAuthentications", "password,keyboard-interactive,publickey");
        session.setConfig("Cipher", "aes128-ctr,aes192-ctr,aes256-ctr,3des-ctr,arcfour,arcfour128,arcfour256");
        session.setConfig("KexAlgorithms", "diffie-hellman-group1-sha1,diffie-hellman-group14-sha1,diffie-hellman-group-exchange-sha1,diffie-hellman-group-exchange-sha256");
        session.setConfig("ServerAliveInterval", "120000"); // 例如，设置服务器活跃检查间隔
        session.setConfig("server_host_key", "ssh-dss,ssh-rsa,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521");
        session.setConfig("compression.s2c", "zlib,<EMAIL>,none");
        session.setConfig("compression.c2s", "zlib,<EMAIL>,none");


        try {
            session.connect();
            ChannelSftp channelSftp = (ChannelSftp) session.openChannel("sftp");
            channelSftp.connect();
            log.info("SFTP download connection successful ");
            return channelSftp;
        } catch (JSchException e) {
            String errorMsg = e.getMessage();
            if (errorMsg != null && (errorMsg.contains("Auth fail") || errorMsg.contains("Authentication failed"))) {
                log.error("SFTP authentication failed for user '" + Constants.getCrmDownlaodFtpUsername() + "' on host '" + Constants.getCrmDownlaodFtpIpPort() + "'. Please check username and password.");
            }
            log.error("Failed to connect to SFTP server (download): " + Constants.getCrmDownlaodFtpIpPort(), e);
            throw e;
        }
    }

    /**
     * 安全地关闭SFTP连接
     *
     * @param channelSftp SFTP通道实例
     */
    private static void disconnect(ChannelSftp channelSftp) {
        if (channelSftp != null && channelSftp.isConnected()) {
            try {
                channelSftp.disconnect();
                log.info("SFTP disconnected successfully.");
            } catch (Exception e) {
                log.error("Error disconnecting from SFTP server.", e);
            }
        }
    }

    /**
     * 确保远程目录存在。如果不存在，则逐级创建。
     *
     * @param channelSftp   SFTP通道
     * @param remoteDirPath 远程目录路径 (使用 / 作为分隔符)
     * @throws SftpException 如果创建目录失败
     */
    private static void ensureRemoteDirectoryExists(ChannelSftp channelSftp, String remoteDirPath) throws SftpException {
        if (remoteDirPath == null || remoteDirPath.isEmpty()) {
            return;
        }

        // 总是从根目录开始检查
        channelSftp.cd("/");

        String[] pathElements = remoteDirPath.split("/");
        for (String singleDir : pathElements) {
            if (singleDir.isEmpty()) {
                continue;
            }
            try {
                channelSftp.cd(singleDir);
            } catch (SftpException e) {
                // 目录不存在，创建它
                channelSftp.mkdir(singleDir);
                channelSftp.cd(singleDir);
            }
        }
    }

    /**
     * 上传文件到SFTP服务器
     *
     * @param localFilePath  本地文件完整路径
     * @param remoteFilePath 远程文件完整路径 (使用 / 作为分隔符)
     * @return 如果上传成功返回true, 否则返回false
     */
    public static boolean uploadFile(String localFilePath, String remoteFilePath) {
        File localFile = new File(localFilePath);
        if (!localFile.exists()) {
            log.error("Local file not found: " + localFilePath);
            return false;
        }

        try (InputStream inputStream = new FileInputStream(localFile)) {
            return uploadFile(inputStream, remoteFilePath);
        } catch (IOException e) {
            log.error("Error reading local file: " + localFilePath, e);
            return false;
        }
    }

    /**
     * 上传文件流到SFTP服务器
     *
     * @param inputStream    要上传的文件输入流
     * @param remoteFilePath 远程文件完整路径 (使用 / 作为分隔符)
     * @return 如果上传成功返回true, 否则返回false
     */
    public static boolean uploadFile(InputStream inputStream, String remoteFilePath) {
        ChannelSftp channelSftp = null;
        try {
            channelSftp = connectForUpload();

            // 从路径中提取目录和文件名
            String remoteDirPath = new File(remoteFilePath).getParent();
            String remoteFileName = new File(remoteFilePath).getName();

            if (remoteDirPath != null) {
                ensureRemoteDirectoryExists(channelSftp, remoteDirPath);
            }

            log.info("Uploading file to " + remoteFilePath);
            channelSftp.put(inputStream, remoteFileName);
            log.info("File uploaded successfully.");
            return true;
        } catch (Exception e) {
            log.error("Error during SFTP upload.", e);
            return false;
        } finally {
            disconnect(channelSftp);
        }
    }

    /**
     * 从SFTP服务器下载文件
     *
     * @param remoteFilePath 远程文件完整路径 (使用 / 作为分隔符)
     * @param localFilePath  本地存储完整路径
     * @return 如果下载成功返回true, 否则返回false
     */
    public static boolean downloadFile(String remoteFilePath, String localFilePath) {
        File localFile = new File(localFilePath);
        File parentDir = localFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            if (!parentDir.mkdirs()) {
                log.error("Failed to create local directory: " + parentDir.getAbsolutePath());
                return false;
            }
        }

        try (OutputStream outputStream = new FileOutputStream(localFile)) {
            return downloadFile(remoteFilePath, outputStream);
        } catch (IOException e) {
            log.error("Error creating local file output stream for " + localFilePath, e);
            return false;
        }
    }

    /**
     * 从SFTP服务器下载文件到输出流
     *
     * @param remoteFilePath 远程文件完整路径 (使用 / 作为分隔符)
     * @param outputStream   文件将写入此输出流
     * @return 如果下载成功返回true, 否则返回false
     */
    public static boolean downloadFile(String remoteFilePath, OutputStream outputStream) {
        ChannelSftp channelSftp = null;
        try {
            channelSftp = connectForDownload();
            log.info("Downloading file from " + remoteFilePath);

            channelSftp.get(remoteFilePath, outputStream);
            log.info("File downloaded successfully.");
            return true;
        } catch (Exception e) {
            log.error("Error during SFTP download.", e);
            return false;
        } finally {
            disconnect(channelSftp);
        }
    }

    /**
     * 删除SFTP服务器上的文件
     *
     * @param remoteFilePath 要删除的远程文件的完整路径 (使用 / 作为分隔符)
     * @return 如果删除成功返回true, 否则返回false
     */
    public static boolean deleteFile(String remoteFilePath) {
        ChannelSftp channelSftp = null;
        try {
            channelSftp = connectForDownload();
            log.info("Deleting file: " + remoteFilePath);
            channelSftp.rm(remoteFilePath);
            log.info("File deleted successfully.");
            return true;
        } catch (Exception e) {
            log.error("Error while deleting file " + remoteFilePath, e);
            return false;
        } finally {
            disconnect(channelSftp);
        }
    }

    /**
     * 在SFTP服务器上移动/重命名文件
     *
     * @param fromPath 源文件路径
     * @param toPath   目标文件路径
     * @return 如果成功返回true, 否则返回false
     */
    public static boolean moveFile(String fromPath, String toPath) {
        ChannelSftp channelSftp = null;
        try {
            channelSftp = connectForDownload();

            // 确保目标目录存在
            String toDirPath = new File(toPath).getParent();
            if (toDirPath != null) {
                ensureRemoteDirectoryExists(channelSftp, toDirPath);
            }

            // 返回根目录以便使用绝对路径
            channelSftp.cd("/");

            log.info("Moving file from " + fromPath + " to " + toPath);
            channelSftp.rename(fromPath, toPath);
            log.info("File moved successfully.");
            return true;
        } catch (Exception e) {
            log.error("Error moving file.", e);
            return false;
        } finally {
            disconnect(channelSftp);
        }
    }

    /**
     * 复制SFTP服务器上的文件
     * 注意：此操作通过下载到内存再上传实现，对大文件可能消耗较多内存。
     *
     * @param fromPath 源文件路径
     * @param toPath   目标文件路径
     * @return 如果成功返回true, 否则返回false
     */
    public static boolean copyFile(String fromPath, String toPath) {
        ChannelSftp downloadClient = null;
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            downloadClient = connectForDownload();
            log.info("Copying step 1/2: Downloading " + fromPath + " to memory.");
            downloadClient.get(fromPath, baos);
            disconnect(downloadClient); // 先断开下载连接
            log.info("Copying step 2/2: Uploading from memory to " + toPath);
            try (InputStream inputStream = new ByteArrayInputStream(baos.toByteArray())) {
                return uploadFile(inputStream, toPath); // 用uploadFile，自动用上传参数
            }
        } catch (Exception e) {
            log.error("Error copying file.", e);
            return false;
        } finally {
            if (downloadClient != null && downloadClient.isConnected()) {
                disconnect(downloadClient);
            }
        }
    }

    /**
     * 列出指定目录下的所有文件和文件夹名
     *
     * @param remoteDirPath 远程目录路径
     * @return 文件名列表, 如果失败则返回null
     */
    public static List<String> listFileNames(String remoteDirPath) {
        ChannelSftp channelSftp = null;
        try {
            channelSftp = connectForDownload();
            Vector<ChannelSftp.LsEntry> files = channelSftp.ls(remoteDirPath);
            if (files == null || files.isEmpty()) {
                log.info("No files found in directory: " + remoteDirPath);
                return new ArrayList<>();
            }

            List<String> fileNames = new ArrayList<>();
            for (ChannelSftp.LsEntry entry : files) {
                fileNames.add(entry.getFilename());
            }
            return fileNames;
        } catch (Exception e) {
            log.error("Failed to list files in " + remoteDirPath, e);
            return null;
        } finally {
            disconnect(channelSftp);
        }
    }

    public static void main(String[] args) {
        // 初始化SFTP连接参数
        initUpload();
        initDownload();
        // --- 文件路径定义 ---
        String remoteBaseDir = "/upload/";
        String remoteFileName = "upload_test.txt";
        String remoteFilePath = remoteBaseDir + "/" + remoteFileName;
        System.out.println("--- 1. 上传测试 (从内存字符串) ---");
        String fileContent = "This is a test file uploaded by SFTPUtils.\nLine 2.";
        try (InputStream inputStream = new ByteArrayInputStream(fileContent.getBytes(StandardCharsets.UTF_8))) {
            boolean uploadSuccess = FTPUtils.uploadFile(inputStream, remoteFilePath);
            System.out.println("Upload success: " + uploadSuccess);
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("----------------------------------------\n");
        System.out.println("--- 测试完成 ---");
    }
}

