package com.yunqu.cc.mixgw.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yq.busi.common.util.http.HttpResp;
import com.yq.busi.common.util.http.HttpUtil;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.service.aigc.AIGCService;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ProvinceNLPUtils {
	
	private static Logger logger = CommonLogger.getLogger("rpa");

	/**
	 * 自动识别逻辑-硬编码
	 * @param content
	 * @param phoneNumber
	 * @param productList
	 * @return
	 */
	public static JSONObject extractInfoSingleCode(String schema, String content, String phoneNumber, List<JSONObject> productList) {
		// 生成临时数据
		JSONObject rtJson = new JSONObject();
		List<JSONObject> rpaActionList = new ArrayList<JSONObject>();

		// 命中产品
		JSONObject hitProduct = null;
		// 命中产品数
		int hitProductCount = 0;
		if(CommonUtil.listIsNotNull(productList)) {
			for (JSONObject productJson : productList) {
				String productId = productJson.getString("productId");
				String productName = productJson.getString("productName");
				if(content.indexOf(productName) > -1) {
					hitProductCount++;
					hitProduct = productJson;
					rpaActionList.add(productJson);
				} else {
					JSONObject info = AIGCService.getInstance().getMarkDataByTypeBusiId(schema, Constants.MODEL_BUSI_TYPE_03, productId, content);
					if(info != null && info.containsKey("productId")) {
						hitProductCount++;
						hitProduct = productJson;
						rpaActionList.add(productJson);
					}
				}
			}

			logger.info("content[" + content + "] 命中产品成功，命中产品数:" + hitProductCount);
		}

		boolean isRefundFlag = false;

		// 意图
		JSONObject data = AIGCService.getInstance().getMarkDataByType(schema, Constants.MODEL_BUSI_TYPE_02, content);
		JSONArray actionList = data.getJSONArray("actionList");
		if(CommonUtil.listIsNotNull(actionList)) {
			if(actionList.contains(Constants.RPA_ACTION_REFUND)) {
				// 包含退费意图
				isRefundFlag = true;
				logger.info("content[" + content + "] 包含退费意图");
			}
		}

		rtJson.put("rpaActionList", rpaActionList);
		if(hitProductCount == 1 && isRefundFlag) {
			JSONObject productInfo = rpaActionList.get(0);
			productInfo.put("action", Constants.RPA_ACTION_REFUND);
			productInfo.put("number", phoneNumber);
			String expenses = productInfo.getString("expenses");

			String refundBills = RefundAmountExtractor.getRefundBills(content, expenses);
			if(StringUtils.isBlank(refundBills)) {
				refundBills = "0";
			}
			productInfo.put("refundBills", refundBills);
			rtJson.put("isRefund", isRefundFlag);
			rtJson.put("refundBills", refundBills);
			return rtJson;
		} else if(isRefundFlag) {
			String refundBills = RefundAmountExtractor.getRefundBills(content, null);
			if(StringUtils.isBlank(refundBills)) {
				refundBills = "0";
			}
			rtJson.put("isRefund", isRefundFlag);
			rtJson.put("refundBills", refundBills);
			return rtJson;
		}
		return rtJson;
	}

}
