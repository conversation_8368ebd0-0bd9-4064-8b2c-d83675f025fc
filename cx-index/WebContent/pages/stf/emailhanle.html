
<!DOCTYPE html>
<html>

<head>
  <title>处理</title>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="viewport"
        content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
  <!-- 基础的 css js 资源 -->
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css">
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.0">
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.0">
  <script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
  <script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
  <script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
  <script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.0"></script>
  <script type="text/javascript" src="/cc-opm/static/js/my_i18n.js?v=2021110901"></script>
  <script type="text/javascript" src="/cc-base/static/js/i18n.js?v=20140426"></script>
  <script type="text/javascript" src="/cc-opm/pages/js/stf/extends.js"></script>
  <link rel="stylesheet" href="/cc-quality/static/css/qualityControl.css?v=1.0.0">

  <style>
    .thrity-table {
      padding: 16px 24px;
      height: calc(100% - 100px);
      overflow: auto;
    }

    .quality-item-content {
      height: calc(100vh - 168px);
      overflow: auto;
    }

    .right-panel-bottom {
      position: absolute;
      bottom: 16px;
      height: 72px;
      width: calc(50% - 27px);
      background-color: #fff;
      border-radius: 4px;
      display: flex;
      align-items: center;
      border-top: 1px solid #ebf1f8;
      justify-content: flex-end
    }

    .pointText {
      color: #868686;
      font-size: 14px;
      font-weight: normal;
      line-height: 22px;
    }

    .pointValue {
      font-size: 20px;
      font-weight: bold;
      line-height: 22px;
      color: #F03838;
    }
  </style>
</head>

<body class="yq-page-full vue-box">
<div id="app" class="flex yq-table-page">
  <div class="panel">
    <div class="left-panel">
      <div class="flex yq-card">
        <div class="card-header" v-if="iframeFlat == false">
          <div class="head-title">{{getI18nValue('会话内容')}}</div>
        </div>

        <div class="card-header" v-if="iframeFlat == true">
          <div class="head-title">{{getI18nValue('留言')}}</div>
          <el-link @click="closeLeve" style="margin-left: 93%" target="_blank" type="primary" title="关闭留言">X</el-link>
        </div>
        <div class="thrity-table" v-if="iframeFlat == false">
          <el-descriptions :column="1" border label-class-name="desc-item" content-class-name="desc-value">
            <el-descriptions-item label="邮件发送人" >
              <el-link @click="callEmail"  target="_blank" type="primary"  title="点击进行回复">
                {{ mediaRecord.EMAIL_FROM_NAME  }}
              </el-link>
            </el-descriptions-item>
            <el-descriptions-item label="邮件账号" >
              <el-link @click="callEmailRecord"  target="_blank" type="primary"  title="邮件账号，点击进行查看记录">
                {{ mediaRecord.SESSION_ID  }}
              </el-link>
            </el-descriptions-item>
            <el-descriptions-item label="邮件发送号码" >{{ mediaRecord.EMAIL_FROM }}</el-descriptions-item>
            <el-descriptions-item label="邮件所有接收号码" >{{ mediaRecord.EMAIL_TO_ALL }}</el-descriptions-item>
            <el-descriptions-item label="标题" >{{ mediaRecord.TITLE }}</el-descriptions-item>
            <el-descriptions-item label="回执码" >{{ mediaRecord.CASE_NUM }}</el-descriptions-item>
            <el-descriptions-item label="邮件满意度" >{{ SATISF_IDES[mediaRecord.SATISF_CODE] }}</el-descriptions-item>
            <el-descriptions-item label="满意度评价时间" >{{ mediaRecord.SATISF_TIME }}</el-descriptions-item>
            <el-descriptions-item label="首次处理人姓名" >{{ mediaRecord.FIRST_HANDLE_ACC_NAME }}</el-descriptions-item>
            <el-descriptions-item label="最新处理人姓名" >{{ mediaRecord.HANDLE_ACC_NAME }}</el-descriptions-item>
            <el-descriptions-item label="首次回复人姓名" >{{ mediaRecord.FIRST_REPLY_ACC_NAME }}</el-descriptions-item>
            <el-descriptions-item label="客户最新邮件id" >{{ mediaRecord.EMAIL_ID }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="thrity-table" v-if="iframeFlat == true">
          <iframe :src="iframeUrl" style="height: 100%; width: 100%"></iframe>
        </div>
      </div>
    </div>
    <div class="right-panel">
      <el-collapse v-model="activeNames" class="base-panel">
        <el-collapse-item name="1">
          <template slot="title">
            <div class="base-panel-title">
              <!--                            <div>{{getI18nValue('处理')}}</div>-->
              <div style="display: flex;align-items: center;">
                <el-divider direction="vertical"></el-divider>
                <div style="color: #868686;font-weight: normal;">
                </div>
              </div>
            </div>
          </template>
          <div class="quality-item-content">
            <el-form :model="satisfiedRecord" ref="ruleForm">
              <el-form-item :label="getI18nValue('原满意度')"  prop="SATISFY_CODE " label-width="150px">
                <el-input disabled="true" :placeholder="getI18nValue('无')" v-model="satisfiedRecord.SATISFY_COTENT " style="width:80%;"></el-input>

              </el-form-item>
              <el-form-item :label="getI18nValue('原满意度说明')"  prop="SATISFY_BAKUP" label-width="150px">
                <el-input disabled="true" type="textarea" :rows="5" :placeholder="getI18nValue('无')" v-model="satisfiedRecord.SATISFY_BAKUP" style="width:80%;"></el-input>
              </el-form-item>

              <el-form-item :label="getI18nValue('修改满意度')"  prop="NEW_SATISFY_CODE" label-width="150px" :rules="[{ required: true, message: '不能为空' }]">
                <el-select v-model="satisfiedRecord.NEW_SATISFY_CODE" :placeholder="getI18nValue('请选择')"  style="width:80%;">
                  <el-option v-for="(key,value) of SATISF_IDES" :label="key" :value="value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="getI18nValue('客户不满意原因')"  prop="UNSATISFY_REASON"  label-width="150px" :rules="[{ required: true, message: '不能为空' }]">
                <el-select v-model="satisfiedRecord.UNSATISFY_REASON" :placeholder="getI18nValue('请选择')" :style="labw">
                  <!--                                           multiple-->
                  <!--                                           filterable-->
                  <!--                                           allow-create-->
                  <!--                                           default-first-option-->
                  <!--                                >-->
                  <el-option v-for="(key,value) of SATISF_IDES" :label="key" :value="value"></el-option>
                </el-select>
                <el-input v-if="satisfiedRecord.UNSATISFY_REASON == 5" :placeholder="getI18nValue('请输入')" v-model="satisfiedRecord.UNSATISFY_REASON_NEW" style="width:40%;"></el-input>
              </el-form-item>
              <el-form-item :label="getI18nValue('修改说明')"  prop="NEW_SATISFY_CODE" label-width="150px">
                <el-input type="textarea" :rows="5" :placeholder="getI18nValue('请输入')" v-model="satisfiedRecord.HANDLE_BAKUP " style="width:80%;"></el-input>
              </el-form-item>
            </el-form>
          </div>
        </el-collapse-item>
      </el-collapse>
      <div class="right-panel-bottom">
        <el-button type="primary" class="mr-r16" @click="dispose">{{ getI18nValue('提交') }}</el-button>
      </div>
    </div>
  </div>
</div>
<script>
  var app = new Vue({
    components: {},
    watch: {
      "satisfiedRecord.UNSATISFY_REASON": {
        handler(n) {
          console.log(n)
          if (n == 5) {
            this.labw = "width:40%;"
          } else {
            this.labw = "width:80%;"
          }
        },
      },
    },
    el: '#app',

    data() {
      return {
        iframeFlat: false,
        iframeUrl: "",
        activeNames: ['1'],
////////////////////////////////////////////
        labw: "width:80%;",
        UNSATISFY_REASONES: {
          1: "坐席服务态度差",
          2:"坐席能力不够",
          3:"客户恶意评价",
          4:"客户操作错误",
          // 5:"自定义"
        },
        SATISF_IDES:{
          0:"未评价",
          1: "非常满意",
          2: "满意",
          3: "一般",
          4: "不满意",
          99: "未邀评"
        },
        sessionId: "",
        satisfied: "",
        mediaRecord: {},
        satisfiedRecord: {},

        CLEAR_CAUSEES: {
          1: "用户主动结束服务",
          2: "坐席结束服务",
          3: "超时结束",
          4: "转移挂机"
        },
        CREATE_CAUSEES: {
          1: "用户请求",
          2: "坐席发起",
          3: "转移接入"
        },
        SERVER_STATEES: {
          1: "排队中",
          2: "服务中",
          3: "服务结束"
        },
        FOLLOW_STATUSES: {
          1: "待跟进",
          2:"跟进中",
          3:"完成跟进"
        },
        callBackFun: {},
        recordes: {}
      }
    },
    mounted() {
      console.log(yq.q('EID', ""))
      console.log(yq.q("ID", ""))
      this.sessionId = yq.q('EID', "")
      this.satisfied = yq.q("ID", "")
      this.callBackFun = yq.q('layer', '')
      console.log(this.callBackFun)
      window.closeLayer = this.closeLayer
      this.initChatList(this.satisfiedRecord)
    },
    created() {

    },
    methods: {
      closeLeve() {
        this.iframeFlat = false
      },
      callEmail (){
        top.popup.openTab("/cc-email/pages/email/hw/email-index2.jsp",getI18nValue("回复"),{parentID:this.mediaRecord.ID,
          caseNum:this.mediaRecord.CASE_NUM,title:this.mediaRecord.TITLE,EMAIL_FROM:this.mediaRecord.EMAIL_FROM,EMAIL_TO:this.mediaRecord.EMAIL_TO,orderId:this.mediaRecord.ORDER_ID,
          oldTitle:this.mediaRecord.TITLE,customerId:this.mediaRecord.CUSTINFO_ID,STATUS:this.mediaRecord.STATUS,firstRealyTime:this.mediaRecord.FIRST_REPLY_TIME,sendTime:this.mediaRecord.CREATE_TIME,emailToAll:this.mediaRecord.EMAIL_TO_ALL,emailSessionId:this.mediaRecord.SESSION_ID});
        var num = this.mediaRecord.MOBILE;
        // if (top.parent.parent.CallControl.getFunc("makecall")) {
        //   top.parent.parent.ccbar_plugin.callControl.makeCall(num);
        // } else {
        //   // layer.alert(getI18nValue('当前话务状态不可用'), { title: getI18nValue("提示"), btn: [getI18nValue("确定")] });
        //   yq.msg({
        //     type: "error",
        //     message: "当前话务状态不可用!",
        //   });
        // }
      },
      callEmailRecord() {
        this.iframeUrl = "/cc-email/pages/email/remarks/emailSessionLog.jsp?&emailSessionId=" + this.mediaRecord.SESSION_ID
        this.iframeFlat = true

        // var url = "/cc-email/pages/email/remarks/emailSessionLog.jsp?&emailSessionId=" + this.mediaRecord.SESSION_ID ;
        // top.popup.layerShow({
        //   type: 2,
        //   title: getI18nValue('会话记录'),
        //   zIndex: 999999999,
        //   offset: '40px',
        //   area: ['400px', '320px']
        // }, url, null);

        // var data = {
        //   data: {
        //     "params": {"emailSessionId": this.mediaRecord.SESSION_ID },
        //     "controls":["emailSession.getSessionLogList"]}
        // }
        // yq.tableCall('/cc-email/webcall', data, function (res) {
        //   this.recordes =  res['emailSession.getSessionLogList'].data
        //   console.log(this.recordes)
        // }).finally(res => {})
      },
      dispose() {
        this.$refs.ruleForm.validate(valid => {
          if (valid) {
            var _this = this从
            console.log(this.satisfiedRecord);
            var NewQuery = {}
            _this.satisfiedRecord.NEW_SATISFY_COTENT = _this.SATISF_IDES[_this.satisfiedRecord.NEW_SATISFY_CODE]
            for (var key in _this.satisfiedRecord) {
              NewQuery["unsatisfied."+key] = _this.satisfiedRecord[key]
            }

            yq.remoteCall('/cc-opm/servlet/UnsatisfiedVisitServlet?action=dispose', NewQuery, data => {
              _this.$message({
                message: data.msg,
                type: data.state == 1 ? 'success' : 'error',
              });
              setTimeout(() => {
                parent.closeLayer(this.callBackFun)
              }, 1000);
            })
          }
        })
      },
      closeLayer(funName) {
        if (funName) {
          window.layer.close(this[funName])
          if(funName == 'backLayer'){
            parent.closeLayer(this.callBackFun)
          }
        }
      },
      initChatList() {
        let data = {
          EID: this.sessionId
        }
        yq.remoteCall('/cc-opm/webcall?action=UnsatisfiedVisitDao.emailRecord', data).then(res => {
          this.mediaRecord = res.data
        })
        let data2 = {
          SID: this.satisfied
        }
        yq.remoteCall('/cc-opm/webcall?action=UnsatisfiedCommonDao.staticRecord', data2).then(res => {
          this.satisfiedRecord = res.data
        })
      },

    },
  })
</script>
</body>

</html>