/**
 * 将本身的日期对象按格式化的方式输出成字符串
 * 
 * @param formatter
 *            格式化类型： 默认yyyy-MM-dd
 * @returns {String}
 */
function dateFormat(date, formatter) {
	if (!formatter || formatter == "") {
		formatter = "yyyy-MM-dd";
	}
	var year = date.getFullYear().toString();
	var month = (date.getMonth() + 1).toString();
	var day = date.getDate().toString();
	var hour = date.getHours().toString();
	var minute = date.getMinutes().toString();
	var second = date.getSeconds().toString();
	var returnDate = formatter;

	var yearMarker = formatter.replace(/[^y|Y]/g, '');
	if (yearMarker.length > 1) {
		if (yearMarker.length == 2) {
			year = year.substring(2, 4);
		}
		returnDate = returnDate.replace(yearMarker, year);
	}
	var monthMarker = formatter.replace(/[^M]/g, '');
	if (monthMarker.length > 1) {
		if (month.length == 1) {
			month = "0" + month;
		}
		returnDate = returnDate.replace(monthMarker, month);
	}
	var dayMarker = formatter.replace(/[^d]/g, '');
	if (dayMarker.length > 1) {
		if (day.length == 1) {
			day = "0" + day;
		}
		returnDate = returnDate.replace(dayMarker, day);
	}
	var hourMarker = formatter.replace(/[^h|H]/g, '');
	if (hourMarker.length > 1) {
		if (hour.length == 1) {
			hour = "0" + hour;
		}
		returnDate = returnDate.replace(hourMarker, hour);
	}
	var minuteMarker = formatter.replace(/[^m]/g, '');
	if (minuteMarker.length > 1) {
		if (minute.length == 1) {
			minute = "0" + minute;
		}
		returnDate = returnDate.replace(minuteMarker, minute);
	}
	var secondMarker = formatter.replace(/[^s|S]/g, '');
	if (secondMarker.length > 1) {
		if (second.length == 1) {
			second = "0" + second;
		}
		returnDate = returnDate.replace(secondMarker, second);
	}
	return returnDate;
}
/**
 * 截取时间里的时分秒
 * @param dateStr
 */
function substrHMS( dateStr){
	if(!dateStr){
		return "";
	}
	if(dateStr.length == 19){
		return dateStr.substring(11);
	}else{
		return dateStr;
	}
}
/**
 * 获取当前时间
 */
function getNowTime() {
	return dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss")
}
/**
 * 获取昨天的此时
 */
function getYesterDayTime() {
	var date = new Date();
	date.setTime(date.getTime() - 24 * 60 * 60 * 1000);
	return dateFormat(date, "yyyy-MM-dd HH:mm:ss")
}
///////////////////获取今天，昨天，本周，上周，本月，上月，今年，去年，格式：yyyy-MM-dd
/**
 * 获取今天日期
 */
function getTodayDate() {
	return getNowTime().substring(0, 10);
}

/**
 * 获取昨天的日期
 */
function getYesterDayDate() {
	return getYesterDayTime().substring(0, 10);
}



/**
 * 获取本周的开始日期
 */
function getThisWeekStartDate() {
	var date = new Date();
	var dayOfWeek = date.getDay(); // 今天本周的第几天
	var nowDay = date.getDate(); // 当前日
	date.setDate(nowDay - dayOfWeek + 1);
	return dateFormat(date, "yyyy-MM-dd")
}

/**
 * 获取本周的结束日期
 */
function getThisWeekEndDate() {
	var date = new Date();
	var dayOfWeek = date.getDay(); // 今天本周的第几天
	var nowDay = date.getDate(); // 当前日
	date.setDate(nowDay + (6 - dayOfWeek) + 1);
	return dateFormat(date, "yyyy-MM-dd")
}

/**
 * 获取上周的开始日期
 */
function getLastWeekStartDate() {
	var date = new Date();
	var dayOfWeek = date.getDay(); // 今天本周的第几天
	var nowDay = date.getDate(); // 当前日
	date.setDate(nowDay - dayOfWeek - 6);
	return dateFormat(date, "yyyy-MM-dd")
}

/**
 * 获取上周的结束日期
 */
function getLastWeekEndDate() {
	var date = new Date();
	var dayOfWeek = date.getDay(); // 今天本周的第几天
	var nowDay = date.getDate(); // 当前日
	date.setDate(nowDay - dayOfWeek);
	return dateFormat(date, "yyyy-MM-dd")
}

/**
 * 获取本月的开始日期
 */
function getThisMonthStartDate() {
	var date = new Date();
	date.setDate(1);
	return dateFormat(date, "yyyy-MM-dd")
}

/**
 * 获取本月的结束日期
 */
function getThisMonthEndDate() {
	var date = new Date();
	date.setDate(1);
	date.setMonth(date.getMonth() + 1);
	date.setTime(date.getTime() - 24 * 60 * 60 * 1000);
	return dateFormat(date, "yyyy-MM-dd")
}

/**
 * 获取上月的开始日期
 */
function getLastMonthStartDate() {
	var date = new Date();
	date.setDate(1);
	date.setMonth(date.getMonth() - 1);
	return dateFormat(date, "yyyy-MM-dd")
}

/**
 * 获取上月的结束日期
 */
function getLastMonthEndDate() {
	var date = new Date();
	date.setDate(1);
	date.setTime(date.getTime() - 24 * 60 * 60 * 1000);
	return dateFormat(date, "yyyy-MM-dd")
}
/**
 * 获取今年第一天
 */
function getThisYearStartDate(){
   var date = new Date();
   return date.getFullYear()+"-01-01";
}
/*
 * 获取今年最后一天
 */
function getThisYearEndDate(){
   var date = new Date();
   return date.getFullYear()+"-12-31";
}
/*
 * 获取去年第一天
 */
function getLastYearStartDate(){
   var date = new Date();
   return date.getFullYear()-1+"-01-01";
}
/*
 * 取去年最后一天
 */
function getLastYearEndDate(){
   var date = new Date();
   return date.getFullYear()-1+"-12-31";
}
//近三个月
function getRecentlyThreeMonthStartDate() {
	var date = new Date();
	date.setMonth(date.getMonth()-3);
	//date.setTime(date.getTime() - 3600 * 1000 * 24);
	return dateFormat(date, "yyyy-MM-dd")
}

var onCasecade = function(p){
	var dateRange = p.val();
	if(dateRange == "today") {
		var date = getTodayDate();
		$("#startDate").val(date);
		$("#endDate").val(date);
	}
	else if(dateRange == "thisWeek") {
		$("#startDate").val(getThisWeekStartDate());
		$("#endDate").val(getThisWeekEndDate());
	}
	else if(dateRange == "thisMonth") {
		$("#startDate").val(getThisMonthStartDate());
		$("#endDate").val(getThisMonthEndDate());
	}
	else if(dateRange == "yesterday") {
		var date = getYesterDayDate();
		$("#startDate").val(date);
		$("#endDate").val(date);
	}		
	else if(dateRange == "lastWeek") {
		$("#startDate").val(getLastWeekStartDate());
		$("#endDate").val(getLastWeekEndDate());
	}		
	else if(dateRange == "lastMonth") {
		$("#startDate").val(getLastMonthStartDate());
		$("#endDate").val(getLastMonthEndDate());
	}
	else if(dateRange == "thisYear") {
		$("#startDate").val(getThisYearStartDate());
		$("#endDate").val(getThisYearEndDate());
	}
	else if(dateRange == "lastYear") {
		$("#startDate").val(getLastYearStartDate());
		$("#endDate").val(getLastYearEndDate());
	}
	else if(dateRange == "RecentlyThreeMonth") {
		$("#startDate").val(getRecentlyThreeMonthStartDate());
		$("#endDate").val(getTodayDate());
	}
	
}
///////////////////获取今天，昨天，本周，上周，本月，上月，今年，去年，格式：yyyy-MM-dd hh:mm:ss
/**
 * 获取今天开始时间
 */
function getTodayStartTime() {
	var date = new Date();
	 var month=date.getMonth()+1;
	 if (month < 10) {
	     month = '0' + month;
	   }
	 day=date.getDate();
	 if(day<10){
	   day = "0"+day;
	 }
	 return date.getFullYear()+"-"+month+"-"+day+" 00:00:00";
}
/**
 * 获取今天结束时间
 */
function getTodayEndTime() {
	var date = new Date();
	  var month=date.getMonth()+1;
	  if (month < 10) {
	    month = '0' + month;
	  }
	  day=date.getDate();
	   if(day<10){
	     day = "0"+day;
	   }
	  return date.getFullYear()+"-"+month+"-"+day+" 23:59:59";
}

/**
 * 获取昨天开始时间
 */
function getYesterDayStartTime() {
	var date=new Date();     
	 date.setDate(date.getDate()-1);     
	 month=Number(date.getMonth())+1
	 if (month < 10) {
	     month = '0' + month
	   }
	 day=date.getDate();
	 if(day<10){
	   day = "0"+day;
	 }
	 return date.getFullYear()+"-"+month+"-"+day+" 00:00:00"; 
}
/**
 * 获取昨天结束时间
 */
function getYesterDayEndTime() {
	var date=new Date();     
	  date.setDate(date.getDate()-1);     
	  month=Number(date.getMonth())+1
	  if (month < 10) {
	    month = '0' + month
	  }
	  day=date.getDate();
	   if(day<10){
	     day = "0"+day;
	   }
	  return date.getFullYear()+"-"+month+"-"+day+" 23:59:59";
}



/**
 * 获取本周的开始日期
 */
function getThisWeekStartTime() {
	var date = new Date();
	var dayOfWeek = date.getDay(); // 今天本周的第几天
	var nowDay = date.getDate(); // 当前日
	date.setDate(nowDay - dayOfWeek + 1);
	return dateFormat(date, "yyyy-MM-dd")+" 00:00:00"
}

/**
 * 获取本周的结束日期
 */
function getThisWeekEndTime() {
	var date = new Date();
	var dayOfWeek = date.getDay(); // 今天本周的第几天
	var nowDay = date.getDate(); // 当前日
	date.setDate(nowDay + (6 - dayOfWeek) + 1);
	return dateFormat(date, "yyyy-MM-dd")+" 23:59:59"
}

/**
 * 获取上周的开始日期
 */
function getLastWeekStartTime() {
	var date = new Date();
	var dayOfWeek = date.getDay(); // 今天本周的第几天
	var nowDay = date.getDate(); // 当前日
	date.setDate(nowDay - dayOfWeek - 6);
	return dateFormat(date, "yyyy-MM-dd")+" 00:00:00"
}

/**
 * 获取上周的结束日期
 */
function getLastWeekEndTime() {
	var date = new Date();
	var dayOfWeek = date.getDay(); // 今天本周的第几天
	var nowDay = date.getDate(); // 当前日
	date.setDate(nowDay - dayOfWeek);
	return dateFormat(date, "yyyy-MM-dd")+" 23:59:59"
}

/**
 * 获取本月的开始日期
 */
function getThisMonthStartTime() {
	var date = new Date();
	date.setDate(1);
	return dateFormat(date, "yyyy-MM-dd")+" 00:00:00"
}

/**
 * 获取本月的结束日期
 */
function getThisMonthEndTime() {
	var date = new Date();
	date.setDate(1);
	date.setMonth(date.getMonth() + 1);
	date.setTime(date.getTime() - 24 * 60 * 60 * 1000);
	return dateFormat(date, "yyyy-MM-dd")+" 23:59:59"
}

/**
 * 获取上月的开始日期
 */
function getLastMonthStartTime() {
	var date = new Date();
	date.setDate(1);
	date.setMonth(date.getMonth() - 1);
	return dateFormat(date, "yyyy-MM-dd")+" 00:00:00"
}

/**
 * 获取上月的结束日期
 */
function getLastMonthEndTime() {
	var date = new Date();
	date.setDate(1);
	date.setTime(date.getTime() - 24 * 60 * 60 * 1000);
	return dateFormat(date, "yyyy-MM-dd")+" 23:59:59"
}


// 近一个月
function getRecentlyOneMonthStartTime() {
	var date = new Date();
	date.setMonth(date.getMonth()-1);
	//date.setTime(date.getTime() - 3600 * 1000 * 24 * 30);
	return dateFormat(date, "yyyy-MM-dd")+" 00:00:00"
}

//近三个月
function getRecentlyThreeMonthStartTime() {
	var date = new Date();
	date.setMonth(date.getMonth()-3);
	//date.setTime(date.getTime() - 3600 * 1000 * 24);
	return dateFormat(date, "yyyy-MM-dd")+" 00:00:00"
}

/**
 * 获取今年第一天
 */
function getThisYearStartTime(){
   var date = new Date();
   return date.getFullYear()+"-01-01"+" 00:00:00"
}
/*
 * 获取今年最后一天
 */
function getThisYearEndTime(){
   var date = new Date();
   return date.getFullYear()+"-12-31"+" 23:59:59"
}
/*
 * 获取去年第一天
 */
function getLastYearStartTime(){
   var date = new Date();
   return date.getFullYear()-1+"-01-01"+" 00:00:00"
}
/*
 * 取去年最后一天
 */
function getLastYearEndTime(){
   var date = new Date();
   return date.getFullYear()-1+"-12-31"+" 23:59:59"
}

var onCasecadeTime = function(p){
	var dateRange = p.val();
	if(dateRange == "today") {
		
		$("#startDate").val(getTodayStartTime());
		$("#endDate").val(getTodayEndTime);
	}
	else if(dateRange == "yesterday") {
		$("#startDate").val(getYesterDayStartTime());
		$("#endDate").val(getYesterDayEndTime());
	}		
	else if(dateRange == "thisWeek") {
		$("#startDate").val(getThisWeekStartTime());
		$("#endDate").val(getThisWeekEndTime());
	}
	else if(dateRange == "thisMonth") {
		$("#startDate").val(getThisMonthStartTime());
		$("#endDate").val(getThisMonthEndTime());
	}
	else if(dateRange == "lastWeek") {
		$("#startDate").val(getLastWeekStartTime());
		$("#endDate").val(getLastWeekEndTime());
	}		
	else if(dateRange == "lastMonth") {
		$("#startDate").val(getLastMonthStartTime());
		$("#endDate").val(getLastMonthEndTime());
	}
	else if(dateRange == "thisYear") {
		$("#startDate").val(getThisYearStartTime());
		$("#endDate").val(getThisYearEndTime());
	}
	else if(dateRange == "lastYear") {
		$("#startDate").val(getLastYearStartTime());
		$("#endDate").val(getLastYearEndTime());
	}
	else if(dateRange == "RecentlyOneMonth") {
		$("#startDate").val(getRecentlyOneMonthStartTime());
		$("#endDate").val(getTodayEndTime());
	}
	else if(dateRange == "RecentlyThreeMonth") {
		$("#startDate").val(getRecentlyThreeMonthStartTime());
		$("#endDate").val(getTodayEndTime());
	}
}
/**
 * 获取今天开始时间
 */
function getTodayS() {
	var date = new Date();
	 var month=date.getMonth()+1;
	 if (month < 10) {
	     month = '0' + month;
	   }
	 day=date.getDate();
	 if(day<10){
	   day = "0"+day;
	 }
	 return date.getFullYear()+"-"+month+"-"+day;
}
/**
 * 获取今天结束时间
 */
function getTodayE() {
	var date = new Date();
	  var month=date.getMonth()+1;
	  if (month < 10) {
	    month = '0' + month;
	  }
	  day=date.getDate();
	   if(day<10){
	     day = "0"+day;
	   }
	  return date.getFullYear()+"-"+month+"-"+day;
}

/**
 * 获取昨天开始时间
 */
function getYesterDayS() {
	var date=new Date();     
	 date.setDate(date.getDate()-1);     
	 month=Number(date.getMonth())+1
	 if (month < 10) {
	     month = '0' + month
	   }
	 day=date.getDate();
	 if(day<10){
	   day = "0"+day;
	 }
	 return date.getFullYear()+"-"+month+"-"+day; 
}
/**
 * 获取昨天结束时间
 */
function getYesterDayE() {
	var date=new Date();     
	  date.setDate(date.getDate()-1);     
	  month=Number(date.getMonth())+1
	  if (month < 10) {
	    month = '0' + month
	  }
	  day=date.getDate();
	   if(day<10){
	     day = "0"+day;
	   }
	  return date.getFullYear()+"-"+month+"-"+day;
}

/**
 * 获取本周的开始日期
 */
function getThisWeekS() {
	var date = new Date();
	var dayOfWeek = date.getDay(); // 今天本周的第几天
	var nowDay = date.getDate(); // 当前日
	date.setDate(nowDay - dayOfWeek + 1);
	return dateFormat(date, "yyyy-MM-dd")
}

/**
 * 获取本周的结束日期
 */
function getThisWeekE() {
	var date = new Date();
	var dayOfWeek = date.getDay(); // 今天本周的第几天
	var nowDay = date.getDate(); // 当前日
	date.setDate(nowDay + (6 - dayOfWeek) + 1);
	return dateFormat(date, "yyyy-MM-dd")
}

//近一个月
function getRecentlyOneMonthS() {
	var date = new Date();
	date.setMonth(date.getMonth()-1);
	return dateFormat(date, "yyyy-MM-dd")
}

//近三个月
function getRecentlyThreeMonthS() {
	var date = new Date();
	date.setMonth(date.getMonth()-3);
	return dateFormat(date, "yyyy-MM-dd")
}
var onChange = function(p){
	var dateRange = p.val();
	if(dateRange == "today") {
		
		$("#startDate").val(getTodayS());
		$("#endDate").val(getTodayE);
	}
	else if(dateRange == "yesterday") {
		$("#startDate").val(getYesterDayS());
		$("#endDate").val(getYesterDayE());
	}		
	else if(dateRange == "thisWeek") {
		$("#startDate").val(getThisWeekS());
		$("#endDate").val(getThisWeekE());
	}
	else if(dateRange == "RecentlyOneMonth") {
		$("#startDate").val(getRecentlyOneMonthS());
		$("#endDate").val(getTodayE());
	}
	else if(dateRange == "RecentlyThreeMonth") {
		$("#startDate").val(getRecentlyThreeMonthS());
		$("#endDate").val(getTodayE());
	}
	
	/**
	 * 对秒进行格式化
	 * @param value
	 * @returns
	 */
	function formatSeconds(value) {
		if(!value){
			return "";
		}
		if(value=="--"){
			return value;
		}
	    if(parseInt(value).toString() == 'NaN'){
	    	return "0";
	    }
	    var theTime = parseInt(value);// 秒

	    return theTime;
	}

}

