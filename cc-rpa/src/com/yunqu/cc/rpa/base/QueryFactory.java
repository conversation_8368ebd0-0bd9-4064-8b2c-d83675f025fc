package com.yunqu.cc.rpa.base;

import java.util.Random;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.db.EasyQuery;


public class QueryFactory {

	/**
	 * 获取读数据源(对于mysql一般指向从库数据源，数据可能有点延迟)
	 * @return
	 */
	public static EasyQuery getReadQuery(){
		return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_READ_NAME);
	}
	
	/**
	 * 随机获取一个写数据源(当没有request请求时，要使用query对象，可以用该方法)
	 * @return
	 */
	public static EasyQuery getWriteQuery(){
		Random random = new Random();
		int num = random.nextInt(3);
		if(num==2){
			return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_TWO);
		}
		return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_ONE);
	}
	
	/**
	 * 根据企业id平均分配，获取写数据源
	 * @param entId
	 * @return
	 */
	public static EasyQuery getQuery(String entId){
		if(StringUtils.isBlank(entId)){
			return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_ONE);
		}
		int _entId = Integer.parseInt(entId);
		if(_entId%2==0){  
			return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_ONE);
		}
		return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_TWO);
	}
	
}
