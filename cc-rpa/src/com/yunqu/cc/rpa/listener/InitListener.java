package com.yunqu.cc.rpa.listener;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;

import com.yunqu.cc.rpa.service.thread.ThreadMgr;


@WebListener
public class InitListener implements ServletContextListener {

	@Override
	public void contextDestroyed(ServletContextEvent arg0) {
		ThreadMgr.stop();
	}

	@Override
	public void contextInitialized(ServletContextEvent arg0) {
		ThreadMgr.start();
	}

}
