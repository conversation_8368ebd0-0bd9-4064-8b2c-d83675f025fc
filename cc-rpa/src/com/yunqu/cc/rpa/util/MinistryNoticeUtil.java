package com.yunqu.cc.rpa.util;

import java.util.Collections;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.utils.calendar.EasyCalendar;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.rpa.base.CommonLogger;
import com.yunqu.cc.rpa.base.Constants;
import com.yunqu.cc.rpa.base.QueryFactory;

/**
 * 工信部短信邮件通知
 */
public class MinistryNoticeUtil {
	
    private static final Logger logger = CommonLogger.getLogger();

    private final static String ERROR_TEMPLATE_CODE = "1091549368";
    
    public static void sendErrorNotice(String content) {
    	try {
	        JSONObject exJson = new JSONObject();
	        exJson.put("conten", content);
	
	        UserModel userModel = new UserModel();
	        userModel.setUserAcc("system");
	        userModel.setEpCode(Constants.getEntId());
	        userModel.setBusiOrderId(Constants.getBusiOrderId());
	        for (String phone : Constants.getErrorNoticePhone().split(",")) {
	            sendMsg(userModel, phone, ERROR_TEMPLATE_CODE, exJson);
	        }
	        
	        String title = "";
	        content = "新客服系统RPA机器人监控预警，" + content + "，有异常，请及时处理。";
	        for (String email : Constants.getErrorNoticeEmail().split(",")) {
	            sendEmail(userModel, email, title, content);
	        }
    	} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(MinistryNoticeUtil.class) + " error:" + e.getMessage(), e);
		}
    }

    /**
     * 发送信息
     * @param user 发送者
     * @param phone 发送者
     * @param code 模板编号
     * @param exJson 编码匹配内容 {"random":"1111", "param": "2222"}
     * @return
     */
    private static boolean sendMsg(UserModel user, String phone, String code, JSONObject exJson) {
        try {
            if(StringUtils.isNotBlank(phone)){
                //发送短信
                JSONObject param = new JSONObject();
                param.put("userAcc",user.getUserAcc());
                param.put("epCode",user.getEpCode());
                param.put("busiOrderId",user.getBusiOrderId());
                param.put("sender", Constants.APP_NAME);
                param.put("source", "1");
                param.put("command", ServiceCommand.SENDMESSAGE);
                // 接收电话与内容
                JSONObject json = new JSONObject();
                json.put("receiver", phone);// 接收号码
                json.put("content","@"+code);//模板编号
                json.put("exJson",exJson);
                param.put("receivers", Collections.singletonList(json));

                IService service = ServiceContext.getService(ServiceID.SMSGW_INTEFACE);
                JSONObject result = service.invoke(param);
                logger.info("[LoginSmsService]:发送短信：接收号码"+phone+",result:"+result);
                if (result.getString("respCode").equals(GWConstants.RET_CODE_SUCCESS)) {
                    return true;
                } else {
                    return false;
                }
            }
            logger.error("[LoginSmsService]:发送短信手机号码为空");
            return false;
        } catch (Exception e) {
            logger.error("[LoginSmsService]:发送短信报错"+e.getMessage()+e.getCause(),e);
            return false;
        }
    }
    
    /**
     * 发送邮件
     * @param noticeNumber 接受邮箱
     * @param title 标题
     * @param content 内容
     * @return
     */
    private static boolean sendEmail(UserModel user, String noticeNumber, String title, String content) {
        JSONObject rtJson = new JSONObject();
        try {
            JSONObject param = new JSONObject();
            param.put("serialId", ServiceID.EMAIL_INTERFACE);
            param.put("command", ServiceCommand.REQ_SEND_EMAIL);
            param.put("title", title);
            param.put("content", content);
            param.put("to", noticeNumber); // 接受账号
            param.put("source", "99");// 99-标准通知
            String sendTime = EasyCalendar.newInstance().getDateTime("-");
            param.put("sendTime", sendTime);
            param.put("busiId", user.getBusiId());
            param.put("userAcc", user.getUserAcc());
            param.put("haveAttachment", "N");// N-不存在附件

//            param.put("from", emailAcc); // 发送邮件的账号
            param.put("schema",user.getSchemaName());
            param.put("epCode", user.getEpCode());
            param.put("busiOrderId", user.getBusiOrderId());
            logger.info("请求邮件接口参数：" + param);
            IService service = ServiceContext.getService(ServiceID.EMAIL_INTERFACE);
            JSONObject result = service.invoke(param);
            if (result.getString("respCode").equals(GWConstants.RET_CODE_SUCCESS)) {
                rtJson.put("isSuccess", "1");
                rtJson.put("respDesc", "邮件消息发送成功！");

                logger.info( "[" + noticeNumber + "] 邮件消息发送成功！");
                return true;
            } else {
                rtJson.put("isSuccess", "2");
                rtJson.put("respDesc", "[" + noticeNumber + "] 邮件消息发送失败！接口返回码为：" + result.getString("respCode"));
                logger.error( "[" + noticeNumber + "] 邮件消息发送失败！接口返回码为：" + result.getString("respCode"));
                return false;
            }
        } catch (Exception e) {
            rtJson.put("isSuccess", "2");
            rtJson.put("respDesc", "邮件消息发送失败！系统异常!");
            logger.error( "[" + noticeNumber + "] 邮件消息发送接口异常！" + e.getMessage(), e);
            return false;
        }
    }
    
    public static EasyQuery getQuery() {
        return QueryFactory.getWriteQuery();
    }

}
