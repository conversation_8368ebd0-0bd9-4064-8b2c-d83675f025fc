/** 登录页面样式 */

body {
    background-image: url("../images/bg_login.png");
    background-repeat: no-repeat;
    background-size: cover;
}

.login-wrapper, body, html {
    height: 100%
}

.login-wrapper {
    overflow: auto;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}

.login-body {
    padding: 10px 10px 40px 10px;
}

.login-body > .layui-card {
    max-width: 360px;
    margin: 0 auto;
}

.login-body .layui-card-header {
    font-weight: 700;
    font-size: 15px;
}

.login-body .layui-form-pane .layui-form-label {
    width: 48px;
}

.login-body .layui-form-pane .layui-input-block {
    margin-left: 48px;
}

.login-body .login-captcha {
    width: 100%;
    cursor: pointer;
}

.login-body .login-other > * {
    display: inline-block;
    vertical-align: middle;
    margin-right: 10px;
    font-size: 14px;
}

.login-body .login-other .layui-icon {
    position: relative;
    top: 2px;
    font-size: 26px;
}

.login-body .login-other a:hover {
    opacity: .8;
}

.layui-icon-login-qq {
    color: #3492ED
}

.layui-icon-login-wechat {
    color: #4DAF29
}

.layui-icon-login-weibo {
    color: #CF1900
}

/** 底部样式 */
.login-footer {
    text-align: center;
    line-height: 30px;
    color: rgba(255, 255, 255, 0.7) !important;
    padding-bottom: 20px;
}

.login-footer span {
    padding: 0 5px
}

.login-footer a {
    padding: 0 5px;
    color: rgba(255, 255, 255, 0.7) !important;
}

.login-footer a:hover {
    color: rgba(255, 255, 255, 0.4) !important;
}

/** 头部样式 */
.login-header {
    font-family: Myriad Pro, Helvetica Neue, Arial, Helvetica, sans-serif;
    font-size: 20px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.85) !important;
    padding: 10px;
}

.login-header img {
    height: 40px;
}

/** 移动设备样式 */
@media screen and (min-height: 590px) {
    .login-footer {
        position: absolute;
        bottom: 0;
        width: 100%;
    }
}

@media screen and (min-height: 670px) {
    .login-body {
        padding: 110px 10px 40px 10px;
    }
}

/** 辅助样式 */
.layui-link {
    color: #029789 !important;
}

.layui-link:hover {
    opacity: .8;
}

.pull-right {
    float: right;
}

.inline-block {
    display: inline-block;
}