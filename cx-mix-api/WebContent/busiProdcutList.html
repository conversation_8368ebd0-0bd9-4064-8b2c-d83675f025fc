<!DOCTYPE html>
<html>

<head>
  <title>查询已订业务</title>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
  <!-- 基础的 css js 资源 -->
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css"/>
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.0"/>
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.0"/>
  <script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
  <script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
  <script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
  <script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.0"></script>
  <style>
    .yq-table-page {
      background-color: #fff;
    }
    .title-box {
      padding: 16px;
      border-bottom: 1px solid #E8E8E8;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .search-box {
      padding: 10px 24px 0;
    }
    .el-form.el-form--inline .el-input__inner {
      height: auto !important;
      line-height: auto !important;
      background: #f2f4f7;
    }
    .table-box {
      padding: 24px;
      padding-top: 0;
    }
  </style>
</head>

<body class="yq-page-full vue-box">
<div id="productBusi" class="flex yq-table-page" element-loading-text="加载中..." v-cloak>
  <div>
    <el-container>
      <el-header>
        <h1>查询已订业务</h1>
      </el-header>
      <el-main>
        <h3>您已订购的业务：</h3>
<!--        生成一个列表，每一行包含产品名称、价格和一个我要申诉按钮，价格只是一个数字，前面有资费，后面有单位-->
        <el-dialog title="申诉" :visible.sync="dialogVisible" width="90%">
          <el-input type="textarea" v-model="reason" placeholder="请输入申诉理由"></el-input>
          <span slot="footer" class="dialog-footer">
            <el-button @click="submit()">提交</el-button>
            <el-button @click="dialogVisible = false">取消</el-button>
          </span>
        </el-dialog>
        <el-table :data="services" border style="width: 100%">
          <el-table-column label="产品" prop="pro">
            <template slot-scope="scope">
              <p> {{scope.row.productName}} </p>
              <p> 资费 {{scope.row.price}} {{scope.row.chargeType == '1' ? '/次' : '/每月'}} </p>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="success" @click="showDialog(scope.row)">我要申诉</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
    </el-container>
  </div>
</div>
<script>
  var orderFramePage = new Vue({
    components: {},
    el: '#productBusi',
    data: function () {
      return {
        dialogVisible: false,
        reason: '',
        phone: '',
        code: '',
        services: [],
        product: {}
      }
    },
    computed: {

    },
    beforeCreate() {
    },
    created() {
      // 获取链接中的参数
      this.phone = yq.q("phone", "");
      this.token = localStorage.getItem("sms_code_media_token");

      this.getList()

    },
    watch: {},
    methods: {
      showDialog(row) {
        this.product = {}
        this.product = row
        this.dialogVisible = true;
      },
      submit() {
        if (!this.token) {
          this.$message.error("未登录!");
          return;
        }
        // 此处添加提交申诉的逻辑
        yq.remoteCall(`/cx-mix-api/mediaServlet?action=createOrder`, {
          "phone" : this.phone,
          "product" : this.product,
          "content" : this.reason
        },function () {},{
          headers: {
            'Authorization': this.token
          }
        }).then((result) => {
          if(result.state == 0) {
            this.$message.error("提交失败!")
          } else {
            this.$message.success("提交成功!");
          }
        })
        this.product = {}
        this.dialogVisible = false;
      },
      getList() {
        if (!this.token) {
          this.$message.error("未登录!");
          return;
        }
        // header 放上token

        yq.remoteCall(`/cx-mix-api/mediaServlet?action=getProductList`, {
          "phone" : this.phone
        },function () {},{
          headers: {
            'Authorization': this.token
          }
        }).then((result) => {
          result.state == 0 ? this.$message.error("查询失败!") : this.$message.success("查询成功!");
          this.services = result.data;
        })
      }
    },
    mounted: function () {
    }
  })
</script>
</body>

</html>