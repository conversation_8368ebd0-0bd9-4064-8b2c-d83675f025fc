<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="规则添加"></title>
	<style>
		input:-webkit-autofill {  
	    -webkit-box-shadow: 0 0 0px 1000px white inset;  
		}  
		.select2-selection__rendered{text-align: left;}
		.select2-container--bootstrap{width: inherit!important;z-index: 100000000}
		.select2-container--bootstrap .select2-selection{font-size: 13px;}
		.select2-selection{background-color: #fff!important;}
	</style>
	</EasyTag:override>
<EasyTag:override name="content">
			<form id="easyform" autocomplete="off"   autocomplete="off" style="">
				  <table class="table table-edit table-vzebra" >
	              	<tbody >
		                    
	                         
	                        <tr>
		                        <td i18n-content="名称" class="required" ></td>
		                        <td>
		                        	<input type="text"  name="NAME" data-rules="required"  class="form-control input-sm readonly-class">
		                        </td>
			                 </tr>
			                 
			                  <tr>
		                        <td i18n-content="账号" class="required" ></td>
		                        <td>
		                        	<input type="text"  name="ACC" data-rules="required"  class="form-control input-sm readonly-class">
		                        </td>
			                 </tr>
			                 
			                  <tr>
		                     		<td i18n-content="省份" class="required" ></td>
			                        <td>
			                        <select class="form-control input-sm" name="PROVICE_CODE" data-rules="required"   data-mars="common.getDict(AYY_PROVICE)">
			                        	<option value="">请选择</option>
			                        	<option value="all">全国</option>
	                      			</select>
			                        </td>
		                     </tr>
							<tr>
								<td i18n-content="密码" class="required" ></td>
								<td>
									<input type="text"  name="PWD" data-rules="required"  class="form-control input-sm readonly-class">
								</td>
							</tr>
							<tr>
								<td i18n-content="密钥" class="required" ></td>
								<td>
									<input type="text"  name="SECRET_KEY" data-rules="required"  class="form-control input-sm readonly-class">
								</td>
							</tr>
							<tr>
								<td i18n-content="渠道号" ></td>
								<td>
									<input type="text"  name="CHANNEL_ID"  class="form-control input-sm readonly-class">
								</td>
							</tr>
							
		               </tbody>

					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-info ml-20"  type="button" onclick="Busi.ajaxSubmitForm()" i18n-content="保存"></button>
					   		<button type="button" class="btn btn-default btn-sm ml-20" onclick="popup.layerClose(this)" i18n-content="关闭">  </button>
				   </div>
				    
				</table>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
	jQuery.namespace("Busi");
	
	$(function(){
		$("#easyform").render({success:function(result){
		
		}});
	});
	
	Busi.ajaxSubmitForm = function(){
		 if(form.validate("#easyform")){
			 Busi.addData(); 
		 }else{
			 i18nTooltip();
			 return ;
		 }
	}
	
	Busi.addData = function(){
		
		var data = form.getJSONObject("#easyform");
		ajax.remoteCall("${ctxPath}/servlet/ayyAccMemuServlet?action=add",data,function(result) { 
			if(result.state == 1){
				
				parent.layer.msg(result.msg,{icon: 1,offset:'80px',time:2000},function(){
					$("#searchForm").queryData({jumpOne:false});
				});
				parent.SystemRegTemp.loadData();
				popup.layerClose("#easyform");
			}else{
				layer.alert(getI18nValue(result.msg),{icon: 5});
			}
		  }
		);
	}
	 
	
	 

	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>