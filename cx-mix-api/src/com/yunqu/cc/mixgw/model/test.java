package com.yunqu.cc.mixgw.model;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.DateUtil;

import java.io.*;
import java.util.Collection;
import java.util.HashSet;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-28
 */
public class test {

    public static void main(String[] args) throws IOException {
        String jsonString = "{\n" +
                "            \"productid\": \"135999999999999000429\",\n" +
                "            \"mobile\": \"18925020934\",\n" +
                "            \"updateTime\": \"2024-04-24 10:24:09\",\n" +
                "            \"packageType\": \"包月\",\n" +
                "            \"productName\": \"视频彩铃包月\",\n" +
                "            \"orderTime\": \"2024-04-24 10:21:58\",\n" +
                "            \"serialId\": \"202404241021571892502093491089990031845114\",\n" +
                "            \"unUseTime\": \"2024-04-24 10:24:09\",\n" +
                "            \"price\": 1200,\n" +
                "            \"feeProductId\": \"910899900318\",\n" +
                "            \"validTime\": \"2024-05-01 00:00:00\",\n" +
                "            \"SerialId\": \"202404241021571892502093491089990031845114\",\n" +
                "            \"effectTime\": \"2024-04-24 10:21:58\",\n" +
                "            \"state\": \"已退订\",\n" +
                "            \"fee_status\": \"计费成功\",\n" +
                "            \"channelId\": \"5230\",\n" +
                "            \"productType\": 2\n" +
                "        }";
        JSONObject jsonObject = JSONObject.parseObject(jsonString);
        BiSuperJSON biSuperJSON = new BiSuperJSON(jsonObject);
        System.out.println(biSuperJSON);
        biSuperJSON.put("111", "1111");
        System.out.println(biSuperJSON);
    }

}
