package com.yunqu.cc.mixgw.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.IBaseService;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.model.Dict;
import com.yq.busi.common.service.SchemaService;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.base.QueryFactory;
import com.yunqu.cc.mixgw.inf.CXIVRService;
import com.yunqu.cc.mixgw.util.*;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ProductDelayService  extends IBaseService {
    private static Logger logger = CommonLogger.getIvrLogger();

    private String ACCOUNT_CODE="**********";
    @Override
    public String getServiceId() {
        return "CX_PRODUCT_DELAY_CLOCK_SERVICE";
    }

    @Override
    public String getName() {
        return "延时退订产品";
    }

    @Override
    public JSONObject invokeMethod(JSONObject jsonObject) throws ServiceException{
        JSONArray entArray = SchemaService.findEntBusiSchema();
        logger.info("entArray" + entArray.toString());
        for(int i = 0; i < entArray.size(); i++) {
            JSONObject row = entArray.getJSONObject(i);
            try {
                execService(row.getString("ENT_ID"), row.getString("BUSI_ORDER_ID"), row.getString("SCHEMA_NAME"), row.getString("BUSI_ID"));
            } catch (SQLException e) {
            }
        }
        return null;
    }
    public void execService(String entId, String busiOrderId, String schema, String busiId) throws SQLException {
        String ayyUnorderProductDelay = Constants.getAyyUnorderProductDelay();
        int delayTime = Integer.parseInt(ayyUnorderProductDelay) * 2 + 1;
        String time = DateUtil.addMinute(DateUtil.TIME_FORMAT, DateUtil.getCurrentDateStr(), -delayTime);

        EasySQL sql = new EasySQL();
        sql.append("select * from " + schema + ".cx_mix_delay_product" );
        sql.append(" where 1 = 1 ");
        sql.append(delayTime, " and IS_HANDLED <= ? ");
//        sql.append(time, " and create_time <=  ? ");
        EasyQuery query = QueryFactory.getQuery(entId);
        List<JSONObject> jsonObjects = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
        logger.info("[扫描延时产品记录为]：" + jsonObjects);
        for (JSONObject jsonObject : jsonObjects) {
            JSONArray productList = jsonObject.getJSONArray("PRODUCT_LIST");
            String phonenumber = jsonObject.getString("PHONE");
            String opType = jsonObject.getString("OP_TYPE");
            String createTime = jsonObject.getString("CREATE_TIME");
            String isHandled = jsonObject.getString("IS_HANDLED");

            if (StringUtils.equals(opType, "1") && StringUtils.equals(isHandled, "0")) {
                for (Object p : productList) {
                    JSONObject product = (JSONObject) p;
                    String productId = product.getString("productId");
                    String productName = product.getString("productName");
                    String productType = product.getString("productType");

                    JSONObject result = new JSONObject();
                    JSONObject value = new JSONObject();
                    JSONObject para = new JSONObject();
                    para.put("productType", jsonObject.getString("PRODUCT_TYPE"));
                    para.put("productid", productId);
                    para.put("phonenumber", phonenumber);
                    para.put("productName", productName);
                    value.put("para", para);
                    if (StringUtils.equals(productType,"3")) {//ismp
                        value.put("svc", BusiInfUtils.ISMP_PRODUCT_UNSUBSCRIBE_SVC);
                        result= BusiHandleInfReqUtils.getInstance().ismpProductUnsubscribe(value);
                    }else if (StringUtils.equals(productType,"5")) {//视频彩铃
                        value.put("svc",BusiInfUtils.VIDEO_COLOR_RING_UNSUBSCRIBE_SVC);
                        result=BusiHandleInfReqUtils.getInstance().videoColorRingUnsubscribe(value);
                    }else if (StringUtils.equals(productType,"6")) {//视频彩铃盒
                        value.put("svc",BusiInfUtils.VIDEO_COLOR_RING_BOX_UNSUBSCRIBE_SVC);
                        result= BusiHandleInfReqUtils.getInstance().videoColorRingBoxUnsubscribe(value);
                    }else if (StringUtils.equalsAny(productType,"1","2")) {//音频彩铃退订
                        value.put("svc",BusiInfUtils.VOICE_COLOR_RING_UNSUBSCRIBE_SVC);
                        result=BusiHandleInfReqUtils.getInstance().voiceColorRingUnsubscribe(value);
                    }else {
                        result.put("respDesc", "产品类型不正确.");
                        result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
                        result.put("data", false);
                    }
                    logger.info("[单个产品退订 result]："+result);
                    if (result!=null) {
                        JSONObject res=result.getJSONObject("res");
                        String code=res.getString("st");
                        String smsCode="";
                        String msg="";
                        if (BusiInfUtils.RES_CODE.equals(code)) {
                            result.put("respDesc", "成功");
                            result.put("respCode", GWConstants.RET_CODE_SUCCESS);
                            result.put("data", true);
                            smsCode= IvrUtils.ALL_SUCC_CODE;
                            msg="成功";
                            //退订处理完成之后发送短信
                            JSONObject o=new JSONObject();
                            o.put("ProductName", productName);
                            o.put("Url", IvrUtils.ALL_SUCC_URL);
                            JSONObject smsResult= SmsUtil.smsSend(phonenumber,Constants.SUCCESS_MSG_CODE,o,Constants.getEntId(),Constants.getBusiOrderId(), Constants.GW_SMS_CHANNEL_CODE);
                            logger.info("[单个产品退订]"+productName+",发送短信结果："+smsResult);
                            // TODO 创建归档工单
                        }else {
                            result.put("respDesc",res.getString("msg"));
                            result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
                            result.put("data", false);
                            smsCode=IvrUtils.ALL_FAIL_CODE;
                            msg="失败";
                            // TODO 创建在途工单



                        }


                    }
                }
                //创建工单
//                JSONObject pear=new JSONObject();
//                pear.put("phonenumber", phonenumber);
//                pear.put("entId", Constants.getEntId());
//                pear.put("handleContent", template + "退订情况：单个产品退订。" + "产品名称:"+productName+",退订"+msg);
//                pear.put("handleContent", "产品名称:"+productName+",退订"+msg);
//                if (BusiInfUtils.RES_CODE.equals(code)) {
//                    pear.put("appealSource", Constants.APPEAL_SOURCE_13);
//                }else {
//                    pear.put("appealSource", Constants.APPEAL_SOURCE_03);
//                }
//                Map<String,String> map=new HashedMap();
//                map.put(productId, productName);
//                JSONObject orderJson= createOrder(pear,map, false);
//                logger.info("【创建工单结果】"+orderJson);
            }

            else if (StringUtils.equals(opType, "2") && StringUtils.equals(isHandled, "0")) {
                try {
                    JSONObject value = new JSONObject();
                    JSONObject para = new JSONObject();
                    para.put("phonenumber", phonenumber);
                    value.put("para", para);
                    if (para==null) {
                        logger.info("para null");
                    }
                    value.put("SOURCE", "2");
                    value.put("svc",BusiInfUtils.VIDEO_COLOR_RING_ACC_CLOSE_SVC);
                    JSONObject json=BusiHandleInfReqUtils.getInstance().videoColorRingAccClose(value);

                    if (json!=null) {
                        JSONObject res=json.getJSONObject("res");
                        String code=res.getString("st");
                        if (BusiInfUtils.RES_CODE.equals(code)) {
                            logger.info("销户成功");
                            //销户处理完成之后发送短信
                            JSONObject o=new JSONObject();
                            o.put("ProductName", "");
//                            JSONObject smsResult=SmsUtil.smsSend(phonenumber,ACCOUNT_CODE,o,Constants.getEntId(),Constants.getBusiOrderId());
                            JSONObject resultSms= SmsUtil.smsSend(phonenumber,Constants.RIGHT_MSG_CODE,IvrUtils.getParam("close", ""),entId,busiOrderId, Constants.GW_SMS_CHANNEL_CODE);

                            logger.info("[ivr销户短信],发送短信结果："+resultSms);
                            // 建归档工单
                            createOrder(value, new HashedMap(), true);
                            return;
                        }else {
                            logger.info("销户失败");
                            createOrder(value, new HashedMap(), false);
                            // 建在途归档
                        }
                    }

                } catch (Exception e) {
                    logger.error(CommonUtil.getClassNameAndMethod(this)+e.getMessage(),e);
                }
            }


        }

    }

    public static JSONObject createOrder(JSONObject value,Map<String,String>map, boolean isEnd) {

        JSONObject result = new JSONObject();
        result.put("respDesc", "处理失败");
        result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
        result.put("data", false);
        try {
            String phoneNumber=value.getString("phonenumber");

            if (StringUtils.isBlank(phoneNumber)) {
                result.put("respDesc", "手机号不能为空");
                result.put("data", false);
                return result;
            }
            String handleContent=value.getString("handleContent");
            if (StringUtils.isBlank(handleContent)) {
                handleContent="退订";
            }

            String appealSource=value.getString("appealSource");
            if (StringUtils.isBlank(appealSource)) {
                appealSource=Constants.APPEAL_SOURCE_15;
            }
            String class1=value.getString("class1");
            if (StringUtils.isBlank(class1)) {
                class1=Constants.CLASS_01;
            }
            String class2=value.getString("class2");
            if (StringUtils.isBlank(class2)) {
                class2=Constants.CLASS_02;
            }
            String class3=value.getString("class3");
            if (StringUtils.isBlank(class3)) {
                class3=Constants.CLASS_03;
            }
            String entId=value.getString("entId");
            if (StringUtils.isBlank(entId)) {
                entId=Constants.getEntId();
            }
            //生产工单编号
            String time = DateUtil.getCurrentDateStr();

            IService service1 = ServiceContext.getService("CC_FORMMARK_ORDER_NO");
            JSONObject invokeData = service1.invoke(new JSONObject());
            String orderNo = invokeData.getString("data");
            if (StringUtils.isBlank(orderNo)) {
                orderNo = OrderUtils.createOrderNo(time);
            }
//			JSONObject jsonCall=HCodeExUtil.getAreacode(phoneNumber);
            JSONObject jsonCall = getAreacode(phoneNumber);
            JSONObject param=new JSONObject();
            param.put("caller", phoneNumber);
            param.put("urgency","01");//普通工单
            param.put("appealSource", appealSource);
            param.put("orderStatus", isEnd ? "02" : "01");//处理完成
            param.put("areaCode", jsonCall.getString("provinceCode"));
            param.put("handleContent", handleContent);
            param.put("class1", class1);
            param.put("class2", class2);
            param.put("class3",class3);
            param.put("command","addOrder");
            param.put("entId",entId);
            param.put("isRefund","N");
            param.put("isAutoHandle","N");
            param.put("orderNo", orderNo);
            IService service = ServiceContext.getService("IM_ORDER_FLOW_SERVICE");
            if (service!=null) {
                logger.info("[调用工单服务IM_ORDER_FLOW_SERVICE]"+",[请求]"+param);
                JSONObject resultObj = service.invoke(param);
                logger.info("[调用工单服务IM_ORDER_FLOW_SERVICE]"+",[返回]"+resultObj);

                //插入工单产品表
                if (map!=null && map.size()>0) {
                    logger.info("[ivr退订产品写入工单产品表]"+orderNo);
                    insertProduct(orderNo,map);
                }
                result.put("respDesc", "处理成功");
                result.put("respCode", GWConstants.RET_CODE_SUCCESS);
                result.put("data", true);
            }else {
                result.put("respDesc", "工单服务ID:IM_ORDER_FLOW_SERVICE不存在");
            }

        } catch (Exception e) {
            logger.error(e.getMessage(),e);
        }

        return result;
    }

    private static JSONObject getAreacode(String phone) {
        JSONObject json = new JSONObject();
        JSONObject para = new JSONObject();
        para.put("phonenumber", phone);
        json.put("para", para);
        JSONObject result = BusiHandleInfReqUtils.getInstance().queryHPhone(json);
        if(result == null) {
            return null;
        }
        JSONObject inf = result.getJSONObject("inf");
        if(inf == null || !"0000".equals(inf.getString("res_code"))) {
            logger.error("[" + phone + "]查询imusic接口获取号段信息失败");
            return null;
        }
        String provinceName = inf.getString("province");

        List<Dict> dictList = DictCache.getAllDictListByGroupCode(Constants.getEntId(), "AYY_PROVICE");
        Map<String, String> paramMap = dictList.stream().collect(Collectors.toMap(Dict::getName, Dict::getCode));
        String provinceCode = paramMap.get(provinceName);

        JSONObject callJson = new JSONObject();
        callJson.put("provinceCode", provinceCode);
        callJson.put("provinceName", provinceName);

        return callJson;
    }
    private static void insertProduct(String orderNo, Map<String, String> map) throws SQLException {
        String entId=Constants.getEntId();
        String busiOrderId=Constants.getBusiOrderId();
        String schema = SchemaService.findSchemaByEntId(entId);
        EasyQuery query = QueryFactory.getQuery(entId);
        String table=schema+".C_BOX_MUSIC_PRODUCT";
        String sql="insert into "+table+" (ID,ORDER_NO,PRODUCT_ID,PRODUCT_NAME,ENT_ID,BUSI_ORDER_ID,CREATE_TIME) values(?,?,?,?,?,?,?)";
        List<Object[]> listResult = new ArrayList<>();
        String time=DateUtil.getCurrentDateStr();
        for (Map.Entry<String, String> entry:map.entrySet()) {
            listResult.add(new Object[] {
                    IDGenerator.getDefaultNUMID(),
                    orderNo,
                    entry.getKey(),
                    entry.getValue(),
                    entId,
                    busiOrderId,
                    time
            });
        }
        query.executeBatch(sql, listResult);
    }


}
