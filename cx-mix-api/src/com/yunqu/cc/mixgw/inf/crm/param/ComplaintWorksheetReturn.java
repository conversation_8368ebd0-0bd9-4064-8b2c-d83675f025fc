package com.yunqu.cc.mixgw.inf.crm.param;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;

/**
 * 工单回退/回复信息类
 */
@XmlAccessorType(XmlAccessType.FIELD)
public class ComplaintWorksheetReturn {
    private String WORKSHEET_ID;
    private String EXT_WORKSHEET_ID;
    private String COMPLAINT_WORKSHEET_RETURN_ID;
    private String COMPLAINT_WORKSHEET_ID;
    private String DEAL_TYPE;
    private String DEAL_PERSON;
    private String DEAL_PHONE;
    private String DEAL_RESULT;
    private String DEAL_RESULT_CUST;
    private String DEAL_COMMENT;
    private String DEAL_TIME;
    private String COMPLAINT_REASON_TYPE;
    private String RESPONSIBILITY_REASON;
    private String AUDIT_PERSON;
    private String AUDIT_RESULT;
    private String AUDIT_TIME;
    private String ATTACHMENT_PATH;

    private String COMPLAINT_REASON_TYPE_NEW;

    public void setComplaintReasonTypeNew(String complaintReasonTypeNew) {
        this.COMPLAINT_REASON_TYPE_NEW = complaintReasonTypeNew;
    }


    public void setDealResultCustomer(String DealResultCustomer) {
        this.DEAL_RESULT_CUST = DealResultCustomer;
    }

    public String getDealResultCustomer() {
        return this.DEAL_RESULT_CUST;
    }
    public String getWORKSHEET_ID() {
        return WORKSHEET_ID;
    }

    public void setWorksheetId(String worksheetId) {
        this.WORKSHEET_ID = worksheetId;
    }

    public String getEXT_WORKSHEET_ID() {
        return EXT_WORKSHEET_ID;
    }

    public void setExtWorksheetId(String extWorksheetId) {
        this.EXT_WORKSHEET_ID = extWorksheetId;
    }

    public String getCOMPLAINT_WORKSHEET_RETURN_ID() {
        return COMPLAINT_WORKSHEET_RETURN_ID;
    }

    public void setComplaintWorksheetReturnId(String complaintWorksheetReturnId) {
        this.COMPLAINT_WORKSHEET_RETURN_ID = complaintWorksheetReturnId;
    }

    public String getCOMPLAINT_WORKSHEET_ID() {
        return COMPLAINT_WORKSHEET_ID;
    }

    public void setComplaintWorksheetId(String complaintWorksheetId) {
        this.COMPLAINT_WORKSHEET_ID = complaintWorksheetId;
    }

    public String getDEAL_TYPE() {
        return DEAL_TYPE;
    }

    public void setDealType(String dealType) {
        this.DEAL_TYPE = dealType;
    }

    public String getDEAL_PERSON() {
        return DEAL_PERSON;
    }

    public void setDealPerson(String dealPerson) {
        this.DEAL_PERSON = dealPerson;
    }

    public String getDEAL_PHONE() {
        return DEAL_PHONE;
    }

    public void setDealPhone(String dealPhone) {
        this.DEAL_PHONE = dealPhone;
    }

    public String getDEAL_RESULT() {
        return DEAL_RESULT;
    }

    public void setDealResult(String dealResult) {
        this.DEAL_RESULT = dealResult;
    }

    public String getDEAL_COMMENT() {
        return DEAL_COMMENT;
    }

    public void setDealComment(String dealComment) {
        this.DEAL_COMMENT = dealComment;
    }

    public String getDEAL_TIME() {
        return DEAL_TIME;
    }

    public void setDealTime(String dealTime) {
        this.DEAL_TIME = dealTime;
    }

    public String getCOMPLAINT_REASON_TYPE() {
        return COMPLAINT_REASON_TYPE;
    }

    public void setComplaintReasonType(String complaintReasonType) {
        this.COMPLAINT_REASON_TYPE = complaintReasonType;
    }

    public String getRESPONSIBILITY_REASON() {
        return RESPONSIBILITY_REASON;
    }

    public void setResponsibilityReason(String responsibilityReason) {
        this.RESPONSIBILITY_REASON = responsibilityReason;
    }

    public String getAUDIT_PERSON() {
        return AUDIT_PERSON;
    }

    public void setAuditPerson(String auditPerson) {
        this.AUDIT_PERSON = auditPerson;
    }

    public String getAUDIT_RESULT() {
        return AUDIT_RESULT;
    }

    public void setAuditResult(String auditResult) {
        this.AUDIT_RESULT = auditResult;
    }

    public String getAUDIT_TIME() {
        return AUDIT_TIME;
    }

    public void setAuditTime(String auditTime) {
        this.AUDIT_TIME = auditTime;
    }

    public String getATTACHMENT_PATH() {
        return ATTACHMENT_PATH;
    }

    public void setAttachmentPath(String attachmentPath) {
        this.ATTACHMENT_PATH = attachmentPath;
    }
}